{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_show_hardsearch_count_index_list = block_data[92].output\n", "good_show_hardsearch_exposure_ratio_list = block_data[93].output\n", "good_show_hardsearch_item_id_list = block_data[94].output\n", "good_show_hardsearch_lagV1_list = block_data[95].output\n", "good_show_hardsearch_lagV2_list = block_data[96].output\n", "good_show_hardsearch_pagecode_id_list = block_data[97].output\n", "good_show_hardsearch_uniform_spu_id_list = block_data[98].output\n"]}], "source": ["lines = \"\"\"\n", "good_show_hardsearch_count_index_list = block_data[102].output\n", "good_show_hardsearch_exposure_ratio_list = block_data[103].output\n", "good_show_hardsearch_item_id_list = block_data[104].output\n", "good_show_hardsearch_lagV1_list = block_data[105].output\n", "good_show_hardsearch_lagV2_list = block_data[106].output\n", "good_show_hardsearch_pagecode_id_list = block_data[107].output\n", "good_show_hardsearch_uniform_spu_id_list = block_data[108].output\n", "\"\"\".strip().splitlines()\n", "\n", "index = 92\n", "for line in lines:\n", "    # 替换idx为index\n", "    import re\n", "    # 查找block_data[数字]模式并替换为block_data[index]\n", "    new_line = re.sub(r'block_data\\[\\d+\\]', f'block_data[{index}]', line)\n", "    print(new_line)\n", "    index += 1\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sparse0 = block_data[0].output\n", "sparse1 = block_data[1].output\n", "sparse2 = block_data[2].output\n", "sparse3 = block_data[3].output\n", "sparse4 = block_data[4].output\n", "sparse5 = block_data[5].output\n", "sparse6 = block_data[6].output\n", "sparse7 = block_data[7].output\n", "sparse8 = block_data[8].output\n", "sparse9 = block_data[9].output\n", "sparse10 = block_data[10].output\n", "sparse11 = block_data[11].output\n", "sparse12 = block_data[12].output\n", "sparse13 = block_data[13].output\n", "sparse14 = block_data[14].output\n", "sparse15 = block_data[15].output\n", "SIM_G_SEQ_16_380 = block_data[16].output\n", "SIM_G_SEQ_16_381 = block_data[17].output\n", "SIM_G_SEQ_8_382 = block_data[18].output\n", "SIM_G_SEQ_8_383 = block_data[19].output\n", "SIM_G_SEQ_8_384 = block_data[20].output\n", "akg_combine_371 = block_data[21].output\n", "ExtractPhotoSharkEmbedding = block_data[22].output\n", "ExtractItemSharkEmbedding = block_data[23].output\n", "ExtractUserPinnerformerEmbedding = block_data[24].output\n", "ExtractItemPinnerformerEmbedding = block_data[25].output\n", "dense = block_data[26].output\n", "cot_user = block_data[27].output\n", "cot_photo = block_data[28].output\n", "cot_dense = block_data[29].output\n", "entity = block_data[30].output\n", "is_ai_tag = block_data[31].output\n", "ExtractUserDenseAdClick7dVideolClipEmb = block_data[32].output\n", "ExtractUserDenseAdClick30dVideolClipEmb = block_data[33].output\n", "ExtractUserDenseAdClick60dVideolClipEmb = block_data[34].output\n", "ExtractUserDenseOrderpay7dVideolClipEmb = block_data[35].output\n", "ExtractUserDenseOrderpay30dVideolClipEmb = block_data[36].output\n", "ExtractUserDenseOrderpay60dVideolClipEmb = block_data[37].output\n", "ExtractPhotoConcatKeywordVideoClip = block_data[38].output\n", "reco_sparse = block_data[39].output\n", "RECO_SIM_G_SEQ_16_380 = block_data[40].output\n", "RECO_SIM_G_SEQ_16_381 = block_data[41].output\n", "RECO_SIM_G_SEQ_8_382 = block_data[42].output\n", "RECO_SIM_G_SEQ_8_383 = block_data[43].output\n", "RECO_SIM_G_SEQ_8_384 = block_data[44].output\n", "reco_dense = block_data[45].output\n", "sft_dense = block_data[46].output\n", "reco_sft_dense = block_data[47].output\n", "multimodal_emb = block_data[48].output\n", "coupon = block_data[49].output\n", "match_dense = block_data[50].output\n", "user_ecom_rq = block_data[51].output\n", "ecom_multimodal_emb = block_data[52].output\n", "ec_detail = block_data[53].output\n", "ExtractDensePhotoCommentStats = block_data[54].output\n", "ExtractPhotoQcpxCouponAmt = block_data[55].output\n", "dpo = block_data[56].output\n", "ExtractUserDenseDpoFea = block_data[57].output\n", "good_click_cate2cate_real_price_list_extend = block_data[58].output\n", "good_click_cate2cate_category_list_extend = block_data[59].output\n", "good_click_cate2cate_carry_type_list_extend = block_data[60].output\n", "good_click_cate2cate_lag_list_extend = block_data[61].output\n", "good_click_cate2cate_item_id_list_extend = block_data[62].output\n", "good_click_cate2cate_seller_id_list_extend = block_data[63].output\n", "colossus_rs_count_index_list = block_data[64].output\n", "colossus_rs_item_id_list = block_data[65].output\n", "colossus_rs_lagV1_list = block_data[66].output\n", "colossus_rs_lagV2_list = block_data[67].output\n", "colossus_rs_pagecode_id_list = block_data[68].output\n", "colossus_rs_uniform_spu_id_list = block_data[69].output\n", "eshop_ad = block_data[70].output\n", "ue_score_sparse = block_data[71].output\n", "has_uescore = block_data[72].output\n", "ExtractUserDenseGrpoFea = block_data[73].output\n", "grpo_sparse = block_data[74].output\n", "good_click_softsearch_item_id_list_top50 = block_data[75].output\n", "good_click_softsearch_seller_id_list_top50 = block_data[76].output\n", "good_click_softsearch_lag_list_top50 = block_data[77].output\n", "good_click_softsearch_category_list_top50 = block_data[78].output\n", "good_click_softsearch_carry_type_list_top50 = block_data[79].output\n", "good_click_softsearch_price_list_top50 = block_data[80].output\n", "good_click_softsearch_item_count_list_top50 = block_data[81].output\n", "good_click_softsearch_topk_values = block_data[82].output\n", "good_click_softsearch_topk_indices = block_data[83].output\n", "good_show_hardsearch_count_index_list = block_data[84].output\n", "good_show_hardsearch_exposure_ratio_list = block_data[85].output\n", "good_show_hardsearch_item_id_list = block_data[86].output\n", "good_show_hardsearch_lagV1_list = block_data[87].output\n", "good_show_hardsearch_lagV2_list = block_data[88].output\n", "good_show_hardsearch_pagecode_id_list = block_data[89].output\n", "good_show_hardsearch_uniform_spu_id_list = block_data[90].output\n"]}], "source": ["lines = \"\"\"sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,\n", "good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,\n", "colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,\n", "eshop_ad, ue_score_sparse, has_uescore, ExtractUserDenseGrpoFea, grpo_sparse,\n", "good_click_softsearch_item_id_list_top50, good_click_softsearch_seller_id_list_top50,\n", "good_click_softsearch_lag_list_top50, good_click_softsearch_category_list_top50,\n", "good_click_softsearch_carry_type_list_top50, good_click_softsearch_price_list_top50,\n", "good_click_softsearch_item_count_list_top50, good_click_softsearch_topk_values, good_click_softsearch_topk_indices,\n", "good_show_hardsearch_count_index_list, good_show_hardsearch_exposure_ratio_list,\n", "good_show_hardsearch_item_id_list, good_show_hardsearch_lagV1_list, good_show_hardsearch_lagV2_list,\n", "good_show_hardsearch_pagecode_id_list, good_show_hardsearch_uniform_spu_id_list\"\"\".replace('\\n','').split(',')\n", "index = 0\n", "for line in lines:\n", "    \n", "    # 查找block_data[数字]模式并替换为block_data[index]\n", "    new_line = f\"{line.strip()} = block_data[{index}].output\"\n", "    print(new_line)\n", "    index += 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# meta_first.py"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sparse0 = block_data[0].output\n", "sparse1 = block_data[1].output\n", "sparse2 = block_data[2].output\n", "sparse3 = block_data[3].output\n", "sparse4 = block_data[4].output\n", "sparse5 = block_data[5].output\n", "sparse6 = block_data[6].output\n", "sparse7 = block_data[7].output\n", "sparse8 = block_data[8].output\n", "sparse9 = block_data[9].output\n", "sparse10 = block_data[10].output\n", "sparse11 = block_data[11].output\n", "sparse12 = block_data[12].output\n", "sparse13 = block_data[13].output\n", "sparse14 = block_data[14].output\n", "sparse15 = block_data[15].output\n", "SIM_G_SEQ_16_380 = block_data[16].output\n", "SIM_G_SEQ_16_381 = block_data[17].output\n", "SIM_G_SEQ_8_382 = block_data[18].output\n", "SIM_G_SEQ_8_383 = block_data[19].output\n", "SIM_G_SEQ_8_384 = block_data[20].output\n", "akg_combine_371 = block_data[21].output\n", "ExtractPhotoSharkEmbedding = block_data[22].output\n", "ExtractItemSharkEmbedding = block_data[23].output\n", "ExtractUserPinnerformerEmbedding = block_data[24].output\n", "ExtractItemPinnerformerEmbedding = block_data[25].output\n", "dense = block_data[26].output\n", "cot_user = block_data[27].output\n", "cot_photo = block_data[28].output\n", "cot_dense = block_data[29].output\n", "entity = block_data[30].output\n", "is_ai_tag = block_data[31].output\n", "ExtractUserDenseAdClick7dVideolClipEmb = block_data[32].output\n", "ExtractUserDenseAdClick30dVideolClipEmb = block_data[33].output\n", "ExtractUserDenseAdClick60dVideolClipEmb = block_data[34].output\n", "ExtractUserDenseOrderpay7dVideolClipEmb = block_data[35].output\n", "ExtractUserDenseOrderpay30dVideolClipEmb = block_data[36].output\n", "ExtractUserDenseOrderpay60dVideolClipEmb = block_data[37].output\n", "ExtractPhotoConcatKeywordVideoClip = block_data[38].output\n", "reco_sparse = block_data[39].output\n", "RECO_SIM_G_SEQ_16_380 = block_data[40].output\n", "RECO_SIM_G_SEQ_16_381 = block_data[41].output\n", "RECO_SIM_G_SEQ_8_382 = block_data[42].output\n", "RECO_SIM_G_SEQ_8_383 = block_data[43].output\n", "RECO_SIM_G_SEQ_8_384 = block_data[44].output\n", "reco_dense = block_data[45].output\n", "sft_dense = block_data[46].output\n", "reco_sft_dense = block_data[47].output\n", "multimodal_emb = block_data[48].output\n", "coupon = block_data[49].output\n", "match_dense = block_data[50].output\n", "user_ecom_rq = block_data[51].output\n", "ecom_multimodal_emb = block_data[52].output\n", "ec_detail = block_data[53].output\n", "ExtractDensePhotoCommentStats = block_data[54].output\n", "ExtractPhotoQcpxCouponAmt = block_data[55].output\n", "dpo = block_data[56].output\n", "ExtractUserDenseDpoFea = block_data[57].output\n", "good_click_cate2cate_real_price_list_extend = block_data[58].output\n", "good_click_cate2cate_category_list_extend = block_data[59].output\n", "good_click_cate2cate_carry_type_list_extend = block_data[60].output\n", "good_click_cate2cate_lag_list_extend = block_data[61].output\n", "good_click_cate2cate_item_id_list_extend = block_data[62].output\n", "good_click_cate2cate_seller_id_list_extend = block_data[63].output\n", "colossus_rs_count_index_list = block_data[64].output\n", "colossus_rs_item_id_list = block_data[65].output\n", "colossus_rs_lagV1_list = block_data[66].output\n", "colossus_rs_lagV2_list = block_data[67].output\n", "colossus_rs_pagecode_id_list = block_data[68].output\n", "colossus_rs_uniform_spu_id_list = block_data[69].output\n", "eshop_ad = block_data[70].output\n", "ExtractUserDenseGrpoFea = block_data[71].output\n", "grpo_sparse = block_data[72].output\n", "good_click_softsearch_item_id_list_top50 = block_data[73].output\n", "good_click_softsearch_seller_id_list_top50 = block_data[74].output\n", "good_click_softsearch_lag_list_top50 = block_data[75].output\n", "good_click_softsearch_category_list_top50 = block_data[76].output\n", "good_click_softsearch_carry_type_list_top50 = block_data[77].output\n", "good_click_softsearch_price_list_top50 = block_data[78].output\n", "good_click_softsearch_item_count_list_top50 = block_data[79].output\n", "good_click_softsearch_topk_values = block_data[80].output\n", "good_click_softsearch_topk_indices = block_data[81].output\n", "good_show_hardsearch_count_index_list = block_data[82].output\n", "good_show_hardsearch_exposure_ratio_list = block_data[83].output\n", "good_show_hardsearch_item_id_list = block_data[84].output\n", "good_show_hardsearch_lagV1_list = block_data[85].output\n", "good_show_hardsearch_lagV2_list = block_data[86].output\n", "good_show_hardsearch_pagecode_id_list = block_data[87].output\n", "good_show_hardsearch_uniform_spu_id_list = block_data[88].output\n"]}], "source": ["lines = \"\"\"sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,\n", "good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,\n", "colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,\n", "eshop_ad, ExtractUserDenseGrpoFea, grpo_sparse,\n", "good_click_softsearch_item_id_list_top50, good_click_softsearch_seller_id_list_top50,\n", "good_click_softsearch_lag_list_top50, good_click_softsearch_category_list_top50,\n", "good_click_softsearch_carry_type_list_top50, good_click_softsearch_price_list_top50,\n", "good_click_softsearch_item_count_list_top50, good_click_softsearch_topk_values, good_click_softsearch_topk_indices,\n", "good_show_hardsearch_count_index_list, good_show_hardsearch_exposure_ratio_list,\n", "good_show_hardsearch_item_id_list, good_show_hardsearch_lagV1_list, good_show_hardsearch_lagV2_list,\n", "good_show_hardsearch_pagecode_id_list, good_show_hardsearch_uniform_spu_id_list\"\"\".replace('\\n','').split(',')\n", "index = 0\n", "for line in lines:\n", "    \n", "    # 查找block_data[数字]模式并替换为block_data[index]\n", "    new_line = f\"{line.strip()} = block_data[{index}].output\"\n", "    print(new_line)\n", "    index += 1"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}