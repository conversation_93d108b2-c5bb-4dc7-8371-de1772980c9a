{"BSSimProcessor": {"cache_capacity": 50000, "cache_expire_time": 120, "scene_type": "ad", "table_name": "adHubJoinSimData"}, "ad_neixunhuan_filter": true, "add_default_only_miss": false, "apply_graph_transform": true, "attr_mapping": {"mapping": {}}, "batch_timeout_micros": 4000, "btq_incr_topic": "ad_predict_item_info_v2_id_incr", "btq_queue_name": "dsp_szy_grpo_v3", "btq_read_shard": true, "capacity_in_mem": true, "cmd_name": "/ad/dsp/sim/landingpage_submit_rate:dsp_lps_sv_szy_grpo_v3_1", "compute_context_num": 4, "convert_in_local": false, "default_layer_precison_mode": "fp32", "do_not_store_btq_item": true, "embedding_min_fill_ratio": 0.01, "enable_dragon_pipeline": true, "enable_embedding_lookup_directly": true, "enable_field_capacity_check": false, "enable_fixed_cache": false, "enable_idt_item": true, "enable_item_opt": true, "enable_local_feature": false, "enable_pb_cache": false, "enable_run_sub_graph": true, "enable_skip_parse_item": true, "enable_strict_type_constraint": true, "enable_timing_cache": false, "enable_vector_for_validate": [false], "feature_disable_nearby": false, "fp16_layers_prefix": "share_bottom_layer_new_0/", "grappler_transform_params": "pruning,constfold,arithmetic,dependency", "infer_disable_nearby": false, "infer_request_compress_type": 4, "is_negative_valid": true, "item_feature_cache_capacity": 5000000, "item_feature_cache_capacity_in_gb": 80, "item_feature_cache_shard_num": 16, "dragon_grpc_use_multi_eventloop": true, "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_2_shards", "item_service_shard_num": 1, "item_service_timeout": 60, "item_shard_num": 1, "kai_model": true, "ln_fusion": true, "log_to_kafka": false, "lru_capacity": 350000, "lru_capacity_gb": 75, "max_batching_size": 1024, "max_enqueued_batches": 200, "mio_embedding_update_qps": 1500000, "model_max_lag": 3000, "model_src": "btq", "model_type": "infer_service_tf", "need_parse_reco_user_info": true, "only_read_incr_item": true, "output_op_name": "softmax_merge_v3", "output_value_width": 1, "pb_cache_lru_capacity": 7500000, "precision_mode": "fp16", "predict_result_falcon": {"enable": true, "replace_falcon": true}, "predict_value_num": 8, "prune_bs_attr": true, "remap_slot_sign_feature": {"ad_sign_prefix_bit_num": 12, "output_slot_bit_num": 12, "reco_gsu_sign_prefix_bit_num": 10, "reco_sign_prefix_bit_num": 16}, "enable_kv_item_value_empty": true, "remove_sign_prefix": true, "skip_embedding_vector_len_check": true, "stat_fea_value_frequence": 100, "stat_fea_value_max_item_count": 10, "stat_fea_value_mode": 1, "stat_fea_with_cmdkey": false, "strict_layer_precison_mode": true, "template_pipeline_keys": ["bs_reco_sim_v3_subflow", "bs_spu_akg_v5", "ad_inner_order_mmu_user_semantic_id_0103", "ad_inner_order_good_click_sim3_size_redis", "ad_inner_order_good_show_seq_v3"], "template_pipeline_thread_count": 40, "transform_params": "remove_nodes(op=StopGradient,op=CheckNumerics,op=Identity) merge_duplicate_nodes", "trt_max_workspace_size": 1073741824, "use_brpc": false, "use_bs_fast_feature": true, "use_bs_reco_userinfo": true, "use_common_idt_service": true, "use_data_converter": true, "use_dist_item_service": true, "use_double_buffer_loader": false, "use_exist_fix_version": true, "use_full_bs_user_info": true, "use_idt": true, "use_opt_item_server": true, "use_simplified_raw_fea": true, "validator_check_by_minute": false, "validator_duration": 3, "validator_max_roc": 2.0, "vector_op_names": ["inner_merge_toplayer_embedding"], "vector_value_nums": [256], "zero_miss_sign": true, "model_meta_path": "/data/project/sync_server/ad_dsp_sim_landingpage_submit_rate_dsp_lps_sv_szy_grpo_v3_1/real_model_first.meta", "feature_path": "/data/project/sync_server/ad_dsp_sim_landingpage_submit_rate_dsp_lps_sv_szy_grpo_v3_1/real_model_feature", "feature_convert_file": "/data/project/sync_server/ad_dsp_sim_landingpage_submit_rate_dsp_lps_sv_szy_grpo_v3_1/feature_map_for_ps.txt", "model_path": "/data/project/sync_server/ad_dsp_sim_landingpage_submit_rate_dsp_lps_sv_szy_grpo_v3_1/model_version", "deploy": {"hdfs_root": "/home/<USER>/big_model_rollback/dsp_szy_grpo_v3", "model_name": "ad_dsp_sim_landingpage_submit_rate_dsp_lps_sv_szy_grpo_v3_1", "model_feature": "real_model_feature", "model_meta": "real_model_first.meta", "ps_root": "/data/project/sync_server/ad_dsp_sim_landingpage_submit_rate_dsp_lps_sv_szy_grpo_v3_1/", "model_feature_convert": "feature_map_for_ps.txt"}, "dragon_feature_config": [{"capacity": 40000001, "category": "user", "class_name": "user_mmu_851", "embedding_len": 32, "field": 297, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 851, "size": 40000001, "slot": 851}, {"capacity": 10001, "category": "user", "class_name": "user_mmu_852", "embedding_len": 32, "field": 298, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 852, "size": 10001, "slot": 852}, {"capacity": 10001, "category": "user", "class_name": "user_mmu_853", "embedding_len": 32, "field": 299, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 853, "size": 10001, "slot": 853}, {"capacity": 100001, "category": "user", "class_name": "user_mmu_854", "embedding_len": 32, "field": 300, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 854, "size": 100001, "slot": 854}, {"capacity": 10000001, "category": "user", "class_name": "user_mmu_855", "embedding_len": 32, "field": 301, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 855, "size": 10000001, "slot": 855}, {"capacity": 101, "category": "user", "class_name": "colossus_rs_count_index_list", "concat_len": 800, "embedding_len": 4, "field": 302, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1014, "size": 101, "slot": 1014}, {"capacity": 10000001, "category": "user", "class_name": "colossus_rs_item_id_list", "concat_len": 800, "embedding_len": 16, "field": 303, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1017, "size": 10000001, "slot": 1017}, {"capacity": 101, "category": "user", "class_name": "colossus_rs_lagV1_list", "concat_len": 800, "embedding_len": 4, "field": 304, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1018, "size": 101, "slot": 1018}, {"capacity": 10001, "category": "user", "class_name": "colossus_rs_lagV2_list", "concat_len": 400, "embedding_len": 4, "field": 305, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1019, "size": 10001, "slot": 1019}, {"capacity": 101, "category": "user", "class_name": "colossus_rs_pagecode_id_list", "concat_len": 1600, "embedding_len": 4, "field": 306, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1020, "size": 101, "slot": 1020}, {"capacity": 10000001, "category": "user", "class_name": "colossus_rs_uniform_spu_id_list", "concat_len": 400, "embedding_len": 16, "field": 307, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1022, "size": 10000001, "slot": 1022}, {"capacity": 100000001, "category": "combine", "class_name": "long_term_pids_v2", "concat_len": 1600, "embedding_len": 16, "field": 456, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 380, "size": 100000001, "slot": 346}, {"capacity": 100000001, "category": "combine", "class_name": "long_term_aids_v2", "concat_len": 1600, "embedding_len": 16, "field": 457, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 381, "size": 100000001, "slot": 347}, {"capacity": 5000001, "category": "combine", "class_name": "long_term_play_v2", "concat_len": 800, "embedding_len": 8, "field": 458, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 382, "size": 5000001, "slot": 348}, {"capacity": 2001, "category": "combine", "class_name": "long_term_tags_v2", "concat_len": 800, "embedding_len": 8, "field": 459, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 383, "size": 2001, "slot": 349}, {"capacity": 1001, "category": "combine", "class_name": "long_term_times_v2", "concat_len": 800, "embedding_len": 8, "field": 460, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 384, "size": 1001, "slot": 350}, {"capacity": 10000001, "category": "combine", "class_name": "akg_graph_371", "concat_len": 160, "embedding_len": 8, "field": 461, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 371, "size": 10000001, "slot": 371}, {"capacity": 40000001, "category": "combine", "class_name": "user_mmu_cross_856", "embedding_len": 32, "field": 462, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 856, "size": 40000001, "slot": 851}, {"capacity": 40000001, "category": "combine", "class_name": "user_mmu_cross_857", "embedding_len": 32, "field": 463, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 857, "size": 40000001, "slot": 851}, {"capacity": 40000001, "category": "combine", "class_name": "user_mmu_cross_858", "embedding_len": 32, "field": 464, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 858, "size": 40000001, "slot": 851}, {"capacity": 10001, "category": "combine", "class_name": "user_mmu_cross_859", "embedding_len": 32, "field": 465, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 859, "size": 10001, "slot": 859}, {"capacity": 310, "category": "combine", "class_name": "good_click_cate2cate_real_price_list_extend_new", "concat_len": 800, "embedding_len": 8, "field": 466, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 883, "size": 310, "slot": 883}, {"capacity": 100001, "category": "combine", "class_name": "good_click_cate2cate_category_list_extend_new", "concat_len": 800, "embedding_len": 8, "field": 467, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 885, "size": 100001, "slot": 885}, {"capacity": 10, "category": "combine", "class_name": "good_click_cate2cate_carry_type_list_extend_new", "concat_len": 400, "embedding_len": 4, "field": 468, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 888, "size": 10, "slot": 888}, {"capacity": 2001, "category": "combine", "class_name": "good_click_cate2cate_lag_list_extend_new", "concat_len": 800, "embedding_len": 8, "field": 469, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 889, "size": 2001, "slot": 889}, {"capacity": 1000001, "category": "combine", "class_name": "good_click_cate2cate_item_id_list_extend_new", "concat_len": 1600, "embedding_len": 16, "field": 470, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 891, "size": 1000001, "slot": 891}, {"capacity": 3000001, "category": "combine", "class_name": "good_click_cate2cate_seller_id_list_extend_new", "concat_len": 1600, "embedding_len": 16, "field": 471, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 894, "size": 3000001, "slot": 894}], "emp_service": {"0": [{"capacity": 40000001, "embedding_len": 32, "field": 297, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 851, "size": 40000001, "slot": 851}, {"capacity": 0, "embedding_len": 32, "field": 462, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 856, "size": 40000001, "slot": 851}, {"capacity": 0, "embedding_len": 32, "field": 463, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 857, "size": 40000001, "slot": 851}, {"capacity": 0, "embedding_len": 32, "field": 464, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 858, "size": 40000001, "slot": 851}, {"capacity": 100000001, "embedding_len": 16, "field": 452, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 163, "size": 100000001, "slot": 138}, {"capacity": 100000001, "embedding_len": 16, "field": 454, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 165, "size": 100000001, "slot": 163}, {"capacity": 100000001, "embedding_len": 16, "field": 310, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 193, "size": 100000001, "slot": 193}, {"capacity": 10000001, "embedding_len": 16, "field": 0, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 1, "size": 10000001, "slot": 1}, {"capacity": 10000001, "embedding_len": 16, "field": 359, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 311, "size": 10000001, "slot": 311}, {"capacity": 10000001, "embedding_len": 16, "field": 253, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 442, "size": 10000001, "slot": 442}, {"capacity": 0, "embedding_len": 16, "field": 260, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 454, "size": 10000001, "slot": 442}, {"capacity": 0, "embedding_len": 16, "field": 400, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 466, "size": 10000001, "slot": 442}, {"capacity": 10000001, "embedding_len": 16, "field": 255, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 444, "size": 10000001, "slot": 444}, {"capacity": 0, "embedding_len": 16, "field": 262, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 456, "size": 10000001, "slot": 444}, {"capacity": 0, "embedding_len": 16, "field": 402, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 468, "size": 10000001, "slot": 444}, {"capacity": 10000001, "embedding_len": 16, "field": 295, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 636, "size": 10000001, "slot": 636}, {"capacity": 3000001, "embedding_len": 16, "field": 65, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 79, "size": 3000001, "slot": 79}, {"capacity": 2000001, "embedding_len": 16, "field": 66, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 80, "size": 2000001, "slot": 80}, {"capacity": 2000001, "embedding_len": 16, "field": 33, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 36, "size": 2000001, "slot": 36}, {"capacity": 1000001, "embedding_len": 16, "field": 50, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 55, "size": 1000001, "slot": 55}, {"capacity": 1000001, "embedding_len": 16, "field": 383, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 407, "size": 1000001, "slot": 407}, {"capacity": 1000001, "embedding_len": 16, "field": 365, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 317, "size": 1000001, "slot": 317}, {"capacity": 1000001, "embedding_len": 16, "field": 129, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 169, "size": 1000001, "slot": 169}, {"capacity": 1000001, "embedding_len": 16, "field": 47, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 52, "size": 1000001, "slot": 52}, {"capacity": 1000001, "embedding_len": 16, "field": 43, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 48, "size": 1000001, "slot": 48}, {"capacity": 1000001, "embedding_len": 16, "field": 377, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 401, "size": 1000001, "slot": 401}, {"capacity": 1000001, "embedding_len": 16, "field": 369, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 393, "size": 1000001, "slot": 393}, {"capacity": 1000001, "embedding_len": 16, "field": 130, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 170, "size": 1000001, "slot": 170}, {"capacity": 1000001, "embedding_len": 16, "field": 194, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 320, "size": 1000001, "slot": 320}, {"capacity": 1000001, "embedding_len": 16, "field": 53, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 60, "size": 1000001, "slot": 60}, {"capacity": 1000001, "embedding_len": 16, "field": 361, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 313, "size": 1000001, "slot": 313}, {"capacity": 1000001, "embedding_len": 16, "field": 224, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 373, "size": 1000001, "slot": 373}, {"capacity": 1000001, "embedding_len": 16, "field": 200, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 326, "size": 1000001, "slot": 326}, {"capacity": 1000001, "embedding_len": 16, "field": 21, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 24, "size": 1000001, "slot": 24}, {"capacity": 1000001, "embedding_len": 16, "field": 195, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 321, "size": 1000001, "slot": 321}, {"capacity": 1000001, "embedding_len": 16, "field": 54, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 61, "size": 1000001, "slot": 61}, {"capacity": 1000001, "embedding_len": 16, "field": 362, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 314, "size": 1000001, "slot": 314}, {"capacity": 1000001, "embedding_len": 16, "field": 196, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 322, "size": 1000001, "slot": 322}, {"capacity": 1000001, "embedding_len": 16, "field": 72, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 86, "size": 1000001, "slot": 86}, {"capacity": 1000001, "embedding_len": 16, "field": 379, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 403, "size": 1000001, "slot": 403}, {"capacity": 1000001, "embedding_len": 16, "field": 386, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 724, "size": 1000001, "slot": 724}, {"capacity": 1000001, "embedding_len": 16, "field": 125, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 161, "size": 1000001, "slot": 161}, {"capacity": 1000001, "embedding_len": 16, "field": 132, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 172, "size": 1000001, "slot": 172}, {"capacity": 1000001, "embedding_len": 16, "field": 371, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 395, "size": 1000001, "slot": 395}, {"capacity": 1000000, "embedding_len": 16, "field": 282, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 494, "size": 1000000, "slot": 494}, {"capacity": 500001, "embedding_len": 16, "field": 44, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 49, "size": 500001, "slot": 49}, {"capacity": 500001, "embedding_len": 16, "field": 63, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 74, "size": 500001, "slot": 74}, {"capacity": 500001, "embedding_len": 16, "field": 74, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 88, "size": 500001, "slot": 88}, {"capacity": 100001, "embedding_len": 64, "field": 413, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 728, "size": 100001, "slot": 728}, {"capacity": 300001, "embedding_len": 16, "field": 82, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 106, "size": 300001, "slot": 106}, {"capacity": 300001, "embedding_len": 16, "field": 20, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 23, "size": 300001, "slot": 23}, {"capacity": 300001, "embedding_len": 16, "field": 12, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 15, "size": 300001, "slot": 15}, {"capacity": 300001, "embedding_len": 16, "field": 83, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 107, "size": 300001, "slot": 107}, {"capacity": 200001, "embedding_len": 16, "field": 75, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 89, "size": 200001, "slot": 89}, {"capacity": 100001, "embedding_len": 16, "field": 73, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 87, "size": 100001, "slot": 87}, {"capacity": 100001, "embedding_len": 16, "field": 14, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 17, "size": 100001, "slot": 17}, {"capacity": 100001, "embedding_len": 16, "field": 121, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 157, "size": 100001, "slot": 157}, {"capacity": 100001, "embedding_len": 16, "field": 215, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 353, "size": 100001, "slot": 353}, {"capacity": 100001, "embedding_len": 16, "field": 207, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 335, "size": 100001, "slot": 335}, {"capacity": 100001, "embedding_len": 16, "field": 241, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 431, "size": 100001, "slot": 431}, {"capacity": 100001, "embedding_len": 16, "field": 80, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 99, "size": 100001, "slot": 92}, {"capacity": 100001, "embedding_len": 16, "field": 204, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 702, "size": 100001, "slot": 702}, {"capacity": 100001, "embedding_len": 16, "field": 290, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 631, "size": 100001, "slot": 631}, {"capacity": 100001, "embedding_len": 16, "field": 217, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 355, "size": 100001, "slot": 355}, {"capacity": 100001, "embedding_len": 16, "field": 8, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 10, "size": 100001, "slot": 10}, {"capacity": 100001, "embedding_len": 16, "field": 221, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 359, "size": 100001, "slot": 359}, {"capacity": 100001, "embedding_len": 16, "field": 135, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 178, "size": 100001, "slot": 178}, {"capacity": 0, "embedding_len": 16, "field": 137, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 180, "size": 100001, "slot": 178}, {"capacity": 0, "embedding_len": 16, "field": 139, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 182, "size": 100001, "slot": 178}, {"capacity": 100001, "embedding_len": 16, "field": 211, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 339, "size": 100001, "slot": 339}, {"capacity": 100001, "embedding_len": 16, "field": 205, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 333, "size": 100001, "slot": 333}, {"capacity": 100001, "embedding_len": 16, "field": 292, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 633, "size": 100001, "slot": 633}, {"capacity": 100001, "embedding_len": 16, "field": 144, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 187, "size": 100001, "slot": 187}, {"capacity": 100001, "embedding_len": 16, "field": 109, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 145, "size": 100001, "slot": 145}, {"capacity": 100001, "embedding_len": 16, "field": 251, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 722, "size": 100001, "slot": 722}, {"capacity": 100001, "embedding_len": 16, "field": 116, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 152, "size": 100001, "slot": 152}, {"capacity": 100001, "embedding_len": 16, "field": 7, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 9, "size": 100001, "slot": 9}, {"capacity": 100001, "embedding_len": 16, "field": 111, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 147, "size": 100001, "slot": 147}, {"capacity": 100001, "embedding_len": 16, "field": 26, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 29, "size": 100001, "slot": 29}, {"capacity": 100001, "embedding_len": 16, "field": 124, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 160, "size": 100001, "slot": 160}, {"capacity": 100001, "embedding_len": 16, "field": 29, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 32, "size": 100001, "slot": 32}, {"capacity": 100001, "embedding_len": 16, "field": 143, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 186, "size": 100001, "slot": 186}, {"capacity": 100001, "embedding_len": 16, "field": 145, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 188, "size": 100001, "slot": 188}, {"capacity": 100001, "embedding_len": 16, "field": 134, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 177, "size": 100001, "slot": 177}, {"capacity": 0, "embedding_len": 16, "field": 136, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 179, "size": 100001, "slot": 177}, {"capacity": 0, "embedding_len": 16, "field": 138, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 181, "size": 100001, "slot": 177}, {"capacity": 100001, "embedding_len": 16, "field": 87, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 121, "size": 100001, "slot": 121}, {"capacity": 100001, "embedding_len": 16, "field": 78, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 96, "size": 100001, "slot": 96}, {"capacity": 100001, "embedding_len": 16, "field": 147, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 190, "size": 100001, "slot": 190}, {"capacity": 100001, "embedding_len": 16, "field": 98, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 134, "size": 100001, "slot": 134}, {"capacity": 100001, "embedding_len": 16, "field": 202, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 701, "size": 100001, "slot": 701}, {"capacity": 100000, "embedding_len": 16, "field": 284, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 496, "size": 100000, "slot": 496}, {"capacity": 10001, "embedding_len": 32, "field": 298, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 852, "size": 10001, "slot": 852}, {"capacity": 10001, "embedding_len": 16, "field": 228, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 712, "size": 10001, "slot": 712}, {"capacity": 10001, "embedding_len": 16, "field": 230, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 713, "size": 10001, "slot": 713}, {"capacity": 10001, "embedding_len": 16, "field": 226, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 711, "size": 10001, "slot": 711}, {"capacity": 10001, "embedding_len": 16, "field": 184, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 700, "size": 10001, "slot": 700}, {"capacity": 10001, "embedding_len": 16, "field": 231, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 428, "size": 10001, "slot": 428}, {"capacity": 10001, "embedding_len": 16, "field": 59, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 70, "size": 10001, "slot": 70}, {"capacity": 10001, "embedding_len": 16, "field": 227, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 424, "size": 10001, "slot": 424}, {"capacity": 3001, "embedding_len": 16, "field": 64, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 76, "size": 3001, "slot": 76}], "1": [{"capacity": 40000001, "embedding_len": 64, "field": 414, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 729, "size": 40000001, "slot": 729}, {"capacity": 100000001, "embedding_len": 16, "field": 450, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 126, "size": 100000001, "slot": 133}, {"capacity": 100000001, "embedding_len": 16, "field": 285, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 623, "size": 100000001, "slot": 623}, {"capacity": 0, "embedding_len": 16, "field": 286, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 624, "size": 100000001, "slot": 623}, {"capacity": 100000001, "embedding_len": 16, "field": 448, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 116, "size": 100000001, "slot": 131}, {"capacity": 100000001, "embedding_len": 16, "field": 453, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 164, "size": 100000001, "slot": 139}, {"capacity": 10000001, "embedding_len": 32, "field": 301, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 855, "size": 10000001, "slot": 855}, {"capacity": 10000001, "embedding_len": 16, "field": 258, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 447, "size": 10000001, "slot": 447}, {"capacity": 0, "embedding_len": 16, "field": 265, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 459, "size": 10000001, "slot": 447}, {"capacity": 0, "embedding_len": 16, "field": 405, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 471, "size": 10000001, "slot": 447}, {"capacity": 10000001, "embedding_len": 16, "field": 257, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 446, "size": 10000001, "slot": 446}, {"capacity": 0, "embedding_len": 16, "field": 264, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 458, "size": 10000001, "slot": 446}, {"capacity": 0, "embedding_len": 16, "field": 404, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 470, "size": 10000001, "slot": 446}, {"capacity": 10000001, "embedding_len": 16, "field": 256, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 445, "size": 10000001, "slot": 445}, {"capacity": 0, "embedding_len": 16, "field": 263, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 457, "size": 10000001, "slot": 445}, {"capacity": 0, "embedding_len": 16, "field": 403, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 469, "size": 10000001, "slot": 445}, {"capacity": 5999984, "embedding_len": 16, "field": 105, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 141, "size": 5999984, "slot": 141}, {"capacity": 1000001, "embedding_len": 32, "field": 287, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 628, "size": 1000001, "slot": 628}, {"capacity": 2000001, "embedding_len": 16, "field": 36, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 40, "size": 2000001, "slot": 40}, {"capacity": 1000001, "embedding_len": 16, "field": 52, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 59, "size": 1000001, "slot": 59}, {"capacity": 1000001, "embedding_len": 16, "field": 364, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 316, "size": 1000001, "slot": 316}, {"capacity": 1000001, "embedding_len": 16, "field": 372, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 396, "size": 1000001, "slot": 396}, {"capacity": 1000001, "embedding_len": 16, "field": 382, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 406, "size": 1000001, "slot": 406}, {"capacity": 1000001, "embedding_len": 16, "field": 232, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 429, "size": 1000001, "slot": 429}, {"capacity": 1000001, "embedding_len": 16, "field": 201, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 327, "size": 1000001, "slot": 327}, {"capacity": 1000001, "embedding_len": 16, "field": 120, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 156, "size": 1000001, "slot": 156}, {"capacity": 1000001, "embedding_len": 16, "field": 373, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 397, "size": 1000001, "slot": 397}, {"capacity": 1000001, "embedding_len": 16, "field": 368, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 392, "size": 1000001, "slot": 392}, {"capacity": 1000001, "embedding_len": 16, "field": 56, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 67, "size": 1000001, "slot": 67}, {"capacity": 1000001, "embedding_len": 16, "field": 198, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 324, "size": 1000001, "slot": 324}, {"capacity": 1000001, "embedding_len": 16, "field": 363, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 315, "size": 1000001, "slot": 315}, {"capacity": 1000001, "embedding_len": 16, "field": 37, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 42, "size": 1000001, "slot": 42}, {"capacity": 1000001, "embedding_len": 16, "field": 57, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 68, "size": 1000001, "slot": 68}, {"capacity": 1000001, "embedding_len": 16, "field": 68, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 82, "size": 1000001, "slot": 82}, {"capacity": 1000001, "embedding_len": 16, "field": 103, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 139, "size": 1000001, "slot": 140}, {"capacity": 0, "embedding_len": 16, "field": 104, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 140, "size": 1000001, "slot": 140}, {"capacity": 1000001, "embedding_len": 16, "field": 31, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 34, "size": 1000001, "slot": 34}, {"capacity": 1000001, "embedding_len": 16, "field": 399, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 387, "size": 1000001, "slot": 387}, {"capacity": 1000001, "embedding_len": 16, "field": 370, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 394, "size": 1000001, "slot": 394}, {"capacity": 1000001, "embedding_len": 16, "field": 394, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 726, "size": 1000001, "slot": 726}, {"capacity": 1000001, "embedding_len": 16, "field": 34, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 37, "size": 1000001, "slot": 37}, {"capacity": 1000001, "embedding_len": 16, "field": 222, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 370, "size": 1000001, "slot": 370}, {"capacity": 1000001, "embedding_len": 16, "field": 389, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 725, "size": 1000001, "slot": 725}, {"capacity": 1000001, "embedding_len": 16, "field": 337, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 723, "size": 1000001, "slot": 723}, {"capacity": 1000001, "embedding_len": 16, "field": 234, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 375, "size": 1000001, "slot": 375}, {"capacity": 0, "embedding_len": 16, "field": 235, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 376, "size": 1000001, "slot": 375}, {"capacity": 0, "embedding_len": 16, "field": 236, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 377, "size": 1000001, "slot": 375}, {"capacity": 0, "embedding_len": 16, "field": 237, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 378, "size": 1000001, "slot": 375}, {"capacity": 0, "embedding_len": 16, "field": 238, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 379, "size": 1000001, "slot": 375}, {"capacity": 0, "embedding_len": 16, "field": 239, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 385, "size": 1000001, "slot": 375}, {"capacity": 0, "embedding_len": 16, "field": 240, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 386, "size": 1000001, "slot": 375}, {"capacity": 1000000, "embedding_len": 16, "field": 280, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 492, "size": 1000000, "slot": 492}, {"capacity": 500001, "embedding_len": 16, "field": 35, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 38, "size": 500001, "slot": 38}, {"capacity": 500001, "embedding_len": 16, "field": 46, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 51, "size": 500001, "slot": 51}, {"capacity": 500001, "embedding_len": 16, "field": 55, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 66, "size": 500001, "slot": 66}, {"capacity": 100001, "embedding_len": 64, "field": 415, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 730, "size": 100001, "slot": 730}, {"capacity": 300001, "embedding_len": 16, "field": 11, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 14, "size": 300001, "slot": 14}, {"capacity": 300001, "embedding_len": 16, "field": 84, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 108, "size": 300001, "slot": 108}, {"capacity": 300001, "embedding_len": 16, "field": 85, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 109, "size": 300001, "slot": 109}, {"capacity": 300001, "embedding_len": 16, "field": 23, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 26, "size": 300001, "slot": 26}, {"capacity": 200001, "embedding_len": 16, "field": 45, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 50, "size": 200001, "slot": 50}, {"capacity": 100001, "embedding_len": 16, "field": 107, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 143, "size": 100001, "slot": 143}, {"capacity": 100001, "embedding_len": 16, "field": 396, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 420, "size": 100001, "slot": 420}, {"capacity": 100001, "embedding_len": 16, "field": 101, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 137, "size": 100001, "slot": 137}, {"capacity": 0, "embedding_len": 16, "field": 102, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 138, "size": 100001, "slot": 137}, {"capacity": 100001, "embedding_len": 16, "field": 110, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 146, "size": 100001, "slot": 146}, {"capacity": 100001, "embedding_len": 16, "field": 247, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 718, "size": 100001, "slot": 718}, {"capacity": 100001, "embedding_len": 16, "field": 248, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 719, "size": 100001, "slot": 719}, {"capacity": 100001, "embedding_len": 16, "field": 5, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 7, "size": 100001, "slot": 7}, {"capacity": 100001, "embedding_len": 16, "field": 141, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 184, "size": 100001, "slot": 184}, {"capacity": 100001, "embedding_len": 16, "field": 245, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 435, "size": 100001, "slot": 435}, {"capacity": 100001, "embedding_len": 16, "field": 409, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 625, "size": 100001, "slot": 625}, {"capacity": 100001, "embedding_len": 16, "field": 118, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 154, "size": 100001, "slot": 154}, {"capacity": 100001, "embedding_len": 16, "field": 267, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 478, "size": 100001, "slot": 478}, {"capacity": 0, "embedding_len": 16, "field": 268, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 479, "size": 100001, "slot": 478}, {"capacity": 0, "embedding_len": 16, "field": 269, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 480, "size": 100001, "slot": 478}, {"capacity": 0, "embedding_len": 16, "field": 270, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 481, "size": 100001, "slot": 478}, {"capacity": 0, "embedding_len": 16, "field": 271, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 482, "size": 100001, "slot": 478}, {"capacity": 0, "embedding_len": 16, "field": 272, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 483, "size": 100001, "slot": 478}, {"capacity": 0, "embedding_len": 16, "field": 273, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 484, "size": 100001, "slot": 478}, {"capacity": 0, "embedding_len": 16, "field": 407, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 491, "size": 100001, "slot": 478}, {"capacity": 100001, "embedding_len": 16, "field": 88, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 122, "size": 100001, "slot": 122}, {"capacity": 0, "embedding_len": 16, "field": 89, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 123, "size": 100001, "slot": 122}, {"capacity": 100001, "embedding_len": 16, "field": 106, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 142, "size": 100001, "slot": 142}, {"capacity": 100001, "embedding_len": 16, "field": 142, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 185, "size": 100001, "slot": 185}, {"capacity": 100001, "embedding_len": 16, "field": 289, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 630, "size": 100001, "slot": 630}, {"capacity": 100001, "embedding_len": 16, "field": 77, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 93, "size": 100001, "slot": 93}, {"capacity": 100001, "embedding_len": 16, "field": 395, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 419, "size": 100001, "slot": 419}, {"capacity": 100001, "embedding_len": 16, "field": 140, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 183, "size": 100001, "slot": 183}, {"capacity": 100001, "embedding_len": 16, "field": 108, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 144, "size": 100001, "slot": 144}, {"capacity": 100001, "embedding_len": 16, "field": 246, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 717, "size": 100001, "slot": 717}, {"capacity": 100001, "embedding_len": 16, "field": 208, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 704, "size": 100001, "slot": 704}, {"capacity": 100001, "embedding_len": 16, "field": 250, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 721, "size": 100001, "slot": 721}, {"capacity": 100001, "embedding_len": 16, "field": 76, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 91, "size": 100001, "slot": 91}, {"capacity": 100001, "embedding_len": 16, "field": 216, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 708, "size": 100001, "slot": 708}, {"capacity": 100001, "embedding_len": 16, "field": 393, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 417, "size": 100001, "slot": 417}, {"capacity": 100001, "embedding_len": 16, "field": 51, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 56, "size": 100001, "slot": 56}, {"capacity": 100001, "embedding_len": 16, "field": 203, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 331, "size": 100001, "slot": 331}, {"capacity": 100001, "embedding_len": 16, "field": 218, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 709, "size": 100001, "slot": 709}, {"capacity": 100001, "embedding_len": 16, "field": 49, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 54, "size": 100001, "slot": 54}, {"capacity": 100001, "embedding_len": 16, "field": 410, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 626, "size": 100001, "slot": 626}, {"capacity": 100001, "embedding_len": 16, "field": 86, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 115, "size": 100001, "slot": 115}, {"capacity": 100001, "embedding_len": 16, "field": 150, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 247, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 154, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 251, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 158, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 255, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 162, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 259, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 166, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 263, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 170, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 267, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 174, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 271, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 178, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 275, "size": 100001, "slot": 247}, {"capacity": 0, "embedding_len": 16, "field": 182, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 279, "size": 100001, "slot": 247}, {"capacity": 100001, "embedding_len": 16, "field": 294, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 635, "size": 100001, "slot": 635}, {"capacity": 30001, "embedding_len": 16, "field": 48, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 53, "size": 30001, "slot": 53}, {"capacity": 20001, "embedding_len": 16, "field": 15, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 18, "size": 20001, "slot": 18}, {"capacity": 10001, "embedding_len": 16, "field": 192, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 112, "size": 10001, "slot": 128}, {"capacity": 10001, "embedding_len": 16, "field": 191, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 111, "size": 10001, "slot": 127}, {"capacity": 10001, "embedding_len": 16, "field": 233, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 714, "size": 10001, "slot": 714}, {"capacity": 10001, "embedding_len": 16, "field": 186, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 101, "size": 10001, "slot": 120}, {"capacity": 10001, "embedding_len": 16, "field": 187, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 102, "size": 10001, "slot": 123}, {"capacity": 10001, "embedding_len": 16, "field": 185, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 100, "size": 10001, "slot": 119}, {"capacity": 10001, "embedding_len": 16, "field": 193, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 113, "size": 10001, "slot": 179}], "2": [{"capacity": 40000001, "embedding_len": 64, "field": 412, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 727, "size": 40000001, "slot": 727}, {"capacity": 100000001, "embedding_len": 16, "field": 451, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 129, "size": 100000001, "slot": 136}, {"capacity": 100000001, "embedding_len": 16, "field": 449, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 117, "size": 100000001, "slot": 132}, {"capacity": 100000001, "embedding_len": 16, "field": 447, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 114, "size": 100000001, "slot": 130}, {"capacity": 100000001, "embedding_len": 16, "field": 455, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 166, "size": 100000001, "slot": 164}, {"capacity": 10000001, "embedding_len": 16, "field": 259, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 452, "size": 10000001, "slot": 452}, {"capacity": 0, "embedding_len": 16, "field": 266, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 464, "size": 10000001, "slot": 452}, {"capacity": 0, "embedding_len": 16, "field": 406, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 476, "size": 10000001, "slot": 452}, {"capacity": 10000001, "embedding_len": 16, "field": 24, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 27, "size": 10000001, "slot": 27}, {"capacity": 10000001, "embedding_len": 16, "field": 288, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 629, "size": 10000001, "slot": 629}, {"capacity": 10000001, "embedding_len": 16, "field": 254, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 443, "size": 10000001, "slot": 443}, {"capacity": 0, "embedding_len": 16, "field": 261, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 455, "size": 10000001, "slot": 443}, {"capacity": 0, "embedding_len": 16, "field": 401, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 467, "size": 10000001, "slot": 443}, {"capacity": 2000001, "embedding_len": 64, "field": 296, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 656, "size": 2000001, "slot": 656}, {"capacity": 3000001, "embedding_len": 16, "field": 10, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 13, "size": 3000001, "slot": 13}, {"capacity": 2000001, "embedding_len": 16, "field": 32, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 35, "size": 2000001, "slot": 35}, {"capacity": 1000001, "embedding_len": 16, "field": 374, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 398, "size": 1000001, "slot": 398}, {"capacity": 1000001, "embedding_len": 16, "field": 131, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 171, "size": 1000001, "slot": 171}, {"capacity": 1000001, "embedding_len": 16, "field": 367, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 391, "size": 1000001, "slot": 391}, {"capacity": 1000001, "embedding_len": 16, "field": 375, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 399, "size": 1000001, "slot": 399}, {"capacity": 1000001, "embedding_len": 16, "field": 197, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 323, "size": 1000001, "slot": 323}, {"capacity": 1000001, "embedding_len": 16, "field": 128, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 168, "size": 1000001, "slot": 168}, {"capacity": 1000001, "embedding_len": 16, "field": 133, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 173, "size": 1000001, "slot": 173}, {"capacity": 1000001, "embedding_len": 16, "field": 41, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 46, "size": 1000001, "slot": 46}, {"capacity": 1000001, "embedding_len": 16, "field": 199, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 325, "size": 1000001, "slot": 325}, {"capacity": 1000001, "embedding_len": 16, "field": 378, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 402, "size": 1000001, "slot": 402}, {"capacity": 1000001, "embedding_len": 16, "field": 39, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 44, "size": 1000001, "slot": 44}, {"capacity": 1000001, "embedding_len": 16, "field": 61, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 72, "size": 1000001, "slot": 72}, {"capacity": 1000001, "embedding_len": 16, "field": 385, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 409, "size": 1000001, "slot": 409}, {"capacity": 1000001, "embedding_len": 16, "field": 384, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 408, "size": 1000001, "slot": 408}, {"capacity": 1000001, "embedding_len": 16, "field": 27, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 30, "size": 1000001, "slot": 30}, {"capacity": 1000001, "embedding_len": 16, "field": 67, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 81, "size": 1000001, "slot": 81}, {"capacity": 1000001, "embedding_len": 16, "field": 360, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 312, "size": 1000001, "slot": 312}, {"capacity": 1000001, "embedding_len": 16, "field": 62, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 73, "size": 1000001, "slot": 73}, {"capacity": 1000001, "embedding_len": 16, "field": 19, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 22, "size": 1000001, "slot": 22}, {"capacity": 1000001, "embedding_len": 16, "field": 380, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 404, "size": 1000001, "slot": 404}, {"capacity": 1000001, "embedding_len": 16, "field": 381, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 405, "size": 1000001, "slot": 405}, {"capacity": 1000001, "embedding_len": 16, "field": 127, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 167, "size": 1000001, "slot": 167}, {"capacity": 1000001, "embedding_len": 16, "field": 366, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 390, "size": 1000001, "slot": 390}, {"capacity": 1000001, "embedding_len": 16, "field": 223, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 372, "size": 1000001, "slot": 372}, {"capacity": 1000001, "embedding_len": 16, "field": 376, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 400, "size": 1000001, "slot": 400}, {"capacity": 1000000, "embedding_len": 16, "field": 281, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 493, "size": 1000000, "slot": 493}, {"capacity": 1000000, "embedding_len": 16, "field": 283, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 495, "size": 1000000, "slot": 495}, {"capacity": 500001, "embedding_len": 16, "field": 30, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 33, "size": 500001, "slot": 33}, {"capacity": 500001, "embedding_len": 16, "field": 71, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 85, "size": 500001, "slot": 85}, {"capacity": 500001, "embedding_len": 16, "field": 69, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 83, "size": 500001, "slot": 83}, {"capacity": 300001, "embedding_len": 16, "field": 42, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 47, "size": 300001, "slot": 47}, {"capacity": 300001, "embedding_len": 16, "field": 70, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 84, "size": 300001, "slot": 84}, {"capacity": 300001, "embedding_len": 16, "field": 81, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 105, "size": 300001, "slot": 105}, {"capacity": 300001, "embedding_len": 16, "field": 28, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 31, "size": 300001, "slot": 31}, {"capacity": 100001, "embedding_len": 32, "field": 300, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 854, "size": 100001, "slot": 854}, {"capacity": 200001, "embedding_len": 16, "field": 40, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 45, "size": 200001, "slot": 45}, {"capacity": 100001, "embedding_len": 16, "field": 123, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 159, "size": 100001, "slot": 159}, {"capacity": 100001, "embedding_len": 16, "field": 117, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 153, "size": 100001, "slot": 153}, {"capacity": 100001, "embedding_len": 16, "field": 25, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 28, "size": 100001, "slot": 28}, {"capacity": 100001, "embedding_len": 16, "field": 220, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 710, "size": 100001, "slot": 710}, {"capacity": 100001, "embedding_len": 16, "field": 212, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 706, "size": 100001, "slot": 706}, {"capacity": 100001, "embedding_len": 16, "field": 243, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 433, "size": 100001, "slot": 433}, {"capacity": 100001, "embedding_len": 16, "field": 122, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 158, "size": 100001, "slot": 158}, {"capacity": 100001, "embedding_len": 16, "field": 206, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 703, "size": 100001, "slot": 703}, {"capacity": 100001, "embedding_len": 16, "field": 219, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 357, "size": 100001, "slot": 357}, {"capacity": 100001, "embedding_len": 16, "field": 79, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 97, "size": 100001, "slot": 97}, {"capacity": 100001, "embedding_len": 16, "field": 114, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 150, "size": 100001, "slot": 150}, {"capacity": 100001, "embedding_len": 16, "field": 392, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 416, "size": 100001, "slot": 416}, {"capacity": 100001, "embedding_len": 16, "field": 249, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 720, "size": 100001, "slot": 720}, {"capacity": 100001, "embedding_len": 16, "field": 38, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 43, "size": 100001, "slot": 43}, {"capacity": 100001, "embedding_len": 16, "field": 210, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 705, "size": 100001, "slot": 705}, {"capacity": 100001, "embedding_len": 16, "field": 397, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 421, "size": 100001, "slot": 421}, {"capacity": 100001, "embedding_len": 16, "field": 209, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 337, "size": 100001, "slot": 337}, {"capacity": 100001, "embedding_len": 16, "field": 214, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 707, "size": 100001, "slot": 707}, {"capacity": 100001, "embedding_len": 16, "field": 291, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 632, "size": 100001, "slot": 632}, {"capacity": 100001, "embedding_len": 16, "field": 213, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 351, "size": 100001, "slot": 351}, {"capacity": 100001, "embedding_len": 16, "field": 119, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 155, "size": 100001, "slot": 155}, {"capacity": 100001, "embedding_len": 16, "field": 6, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 8, "size": 100001, "slot": 8}, {"capacity": 100001, "embedding_len": 16, "field": 126, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 162, "size": 100001, "slot": 162}, {"capacity": 100001, "embedding_len": 16, "field": 146, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 189, "size": 100001, "slot": 189}, {"capacity": 100001, "embedding_len": 16, "field": 242, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 715, "size": 100001, "slot": 715}, {"capacity": 100001, "embedding_len": 16, "field": 151, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 248, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 155, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 252, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 159, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 256, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 163, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 260, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 167, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 264, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 171, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 268, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 175, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 272, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 179, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 276, "size": 100001, "slot": 248}, {"capacity": 0, "embedding_len": 16, "field": 183, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 280, "size": 100001, "slot": 248}, {"capacity": 100001, "embedding_len": 16, "field": 398, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 422, "size": 100001, "slot": 422}, {"capacity": 100001, "embedding_len": 16, "field": 112, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 148, "size": 100001, "slot": 148}, {"capacity": 100001, "embedding_len": 16, "field": 99, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 135, "size": 100001, "slot": 135}, {"capacity": 0, "embedding_len": 16, "field": 100, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 136, "size": 100001, "slot": 135}, {"capacity": 100001, "embedding_len": 16, "field": 244, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 716, "size": 100001, "slot": 716}, {"capacity": 100001, "embedding_len": 16, "field": 293, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 634, "size": 100001, "slot": 634}, {"capacity": 100001, "embedding_len": 16, "field": 115, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 151, "size": 100001, "slot": 151}, {"capacity": 100001, "embedding_len": 16, "field": 113, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 149, "size": 100001, "slot": 149}, {"capacity": 30001, "embedding_len": 16, "field": 2, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 3, "size": 30001, "slot": 3}, {"capacity": 10001, "embedding_len": 32, "field": 299, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 853, "size": 10001, "slot": 853}, {"capacity": 10001, "embedding_len": 16, "field": 188, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 103, "size": 10001, "slot": 124}, {"capacity": 10001, "embedding_len": 16, "field": 190, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 110, "size": 10001, "slot": 126}, {"capacity": 10001, "embedding_len": 16, "field": 13, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 16, "size": 10001, "slot": 16}, {"capacity": 10001, "embedding_len": 16, "field": 229, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 426, "size": 10001, "slot": 426}, {"capacity": 10001, "embedding_len": 16, "field": 18, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 21, "size": 10001, "slot": 21}, {"capacity": 10001, "embedding_len": 16, "field": 189, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 104, "size": 10001, "slot": 125}, {"capacity": 10001, "embedding_len": 16, "field": 60, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 71, "size": 10001, "slot": 71}], "emp_capacity_gb": 39, "emp_shard_count": 3, "local": [{"capacity": 100000001, "embedding_len": 16, "field": 91, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 125, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 92, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 127, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 94, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 130, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 97, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 133, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 149, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 246, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 153, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 250, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 157, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 254, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 161, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 258, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 165, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 262, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 169, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 266, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 173, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 270, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 177, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 274, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 181, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 278, "size": 100000001, "slot": 347}, {"capacity": 0, "embedding_len": 16, "field": 324, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 207, "size": 100000001, "slot": 347}, {"capacity": 0, "concat_len": 1600, "embedding_len": 16, "field": 457, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 381, "size": 100000001, "slot": 347}, {"capacity": 100000001, "embedding_len": 16, "field": 90, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 124, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 93, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 128, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 95, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 131, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 96, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 132, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 148, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 245, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 152, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 249, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 156, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 253, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 160, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 257, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 164, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 261, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 168, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 265, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 172, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 269, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 176, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 273, "size": 100000001, "slot": 346}, {"capacity": 0, "embedding_len": 16, "field": 180, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 277, "size": 100000001, "slot": 346}, {"capacity": 0, "concat_len": 1600, "embedding_len": 16, "field": 456, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 380, "size": 100000001, "slot": 346}, {"capacity": 10000001, "concat_len": 800, "embedding_len": 16, "field": 303, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1017, "size": 10000001, "slot": 1017}, {"capacity": 10000001, "concat_len": 400, "embedding_len": 16, "field": 307, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1022, "size": 10000001, "slot": 1022}, {"capacity": 10000001, "concat_len": 160, "embedding_len": 8, "field": 461, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 371, "size": 10000001, "slot": 371}, {"capacity": 3000001, "concat_len": 1600, "embedding_len": 16, "field": 471, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 894, "size": 3000001, "slot": 894}, {"capacity": 5000001, "concat_len": 800, "embedding_len": 8, "field": 458, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 382, "size": 5000001, "slot": 348}, {"capacity": 1000001, "concat_len": 1600, "embedding_len": 16, "field": 275, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 486, "size": 1000001, "slot": 486}, {"capacity": 1000001, "concat_len": 1600, "embedding_len": 16, "field": 279, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 490, "size": 1000001, "slot": 490}, {"capacity": 1000001, "concat_len": 1600, "embedding_len": 16, "field": 276, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 487, "size": 1000001, "slot": 487}, {"capacity": 1000001, "concat_len": 1600, "embedding_len": 16, "field": 277, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 488, "size": 1000001, "slot": 488}, {"capacity": 1000001, "concat_len": 1600, "embedding_len": 16, "field": 278, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 489, "size": 1000001, "slot": 489}, {"capacity": 1000001, "concat_len": 1600, "embedding_len": 16, "field": 274, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 485, "size": 1000001, "slot": 485}, {"capacity": 1000001, "concat_len": 1600, "embedding_len": 16, "field": 470, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 891, "size": 1000001, "slot": 891}, {"capacity": 100001, "concat_len": 800, "embedding_len": 8, "field": 467, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 885, "size": 100001, "slot": 885}, {"capacity": 10001, "concat_len": 400, "embedding_len": 4, "field": 305, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1019, "size": 10001, "slot": 1019}, {"capacity": 2001, "concat_len": 800, "embedding_len": 8, "field": 459, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 383, "size": 2001, "slot": 349}, {"capacity": 2001, "concat_len": 800, "embedding_len": 8, "field": 469, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 889, "size": 2001, "slot": 889}, {"capacity": 1001, "concat_len": 800, "embedding_len": 8, "field": 460, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 384, "size": 1001, "slot": 350}, {"capacity": 310, "concat_len": 800, "embedding_len": 8, "field": 466, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 883, "size": 310, "slot": 883}, {"capacity": 101, "concat_len": 1600, "embedding_len": 4, "field": 306, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1020, "size": 101, "slot": 1020}, {"capacity": 101, "concat_len": 800, "embedding_len": 4, "field": 302, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1014, "size": 101, "slot": 1014}, {"capacity": 101, "concat_len": 800, "embedding_len": 4, "field": 304, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 1018, "size": 101, "slot": 1018}, {"capacity": 10, "concat_len": 400, "embedding_len": 4, "field": 468, "mio_bucket_size": 0, "op": "CONCAT", "prefix": 0, "remap_slot": 888, "size": 10, "slot": 888}, {"capacity": 1000001, "embedding_len": 16, "field": 338, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 39, "size": 1000001, "slot": 39}, {"capacity": 10001, "embedding_len": 16, "field": 339, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 41, "size": 10001, "slot": 41}, {"capacity": 100001, "embedding_len": 16, "field": 340, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 63, "size": 100001, "slot": 63}, {"capacity": 1001, "embedding_len": 16, "field": 341, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 64, "size": 1001, "slot": 64}, {"capacity": 1001, "embedding_len": 16, "field": 342, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 65, "size": 1001, "slot": 65}, {"capacity": 1001, "embedding_len": 16, "field": 343, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 77, "size": 1001, "slot": 77}, {"capacity": 1001, "embedding_len": 16, "field": 344, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 78, "size": 1001, "slot": 78}, {"capacity": 1001, "embedding_len": 16, "field": 346, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 94, "size": 1001, "slot": 94}, {"capacity": 1001, "embedding_len": 16, "field": 347, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 95, "size": 1001, "slot": 95}, {"capacity": 1001, "embedding_len": 16, "field": 345, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 92, "size": 1001, "slot": 129}, {"capacity": 100001, "embedding_len": 16, "field": 308, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 191, "size": 100001, "slot": 191}, {"capacity": 100001, "embedding_len": 16, "field": 309, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 192, "size": 100001, "slot": 192}, {"capacity": 1001, "embedding_len": 16, "field": 311, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 194, "size": 1001, "slot": 194}, {"capacity": 50001, "embedding_len": 16, "field": 312, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 195, "size": 50001, "slot": 195}, {"capacity": 1001, "embedding_len": 16, "field": 313, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 196, "size": 1001, "slot": 196}, {"capacity": 100001, "embedding_len": 16, "field": 314, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 197, "size": 100001, "slot": 197}, {"capacity": 100001, "embedding_len": 16, "field": 315, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 198, "size": 100001, "slot": 198}, {"capacity": 10001, "embedding_len": 16, "field": 316, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 199, "size": 10001, "slot": 199}, {"capacity": 100001, "embedding_len": 16, "field": 317, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 200, "size": 100001, "slot": 200}, {"capacity": 300001, "embedding_len": 16, "field": 318, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 201, "size": 300001, "slot": 201}, {"capacity": 300001, "embedding_len": 16, "field": 319, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 202, "size": 300001, "slot": 202}, {"capacity": 10001, "embedding_len": 16, "field": 320, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 203, "size": 10001, "slot": 203}, {"capacity": 300001, "embedding_len": 16, "field": 321, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 204, "size": 300001, "slot": 204}, {"capacity": 10001, "embedding_len": 16, "field": 322, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 205, "size": 10001, "slot": 205}, {"capacity": 100001, "embedding_len": 16, "field": 323, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 206, "size": 100001, "slot": 206}, {"capacity": 100001, "embedding_len": 16, "field": 325, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 208, "size": 100001, "slot": 208}, {"capacity": 101, "embedding_len": 16, "field": 326, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 209, "size": 101, "slot": 209}, {"capacity": 500001, "embedding_len": 16, "field": 327, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 210, "size": 500001, "slot": 210}, {"capacity": 10001, "embedding_len": 16, "field": 328, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 211, "size": 10001, "slot": 211}, {"capacity": 100001, "embedding_len": 16, "field": 329, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 212, "size": 100001, "slot": 212}, {"capacity": 101, "embedding_len": 16, "field": 330, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 213, "size": 101, "slot": 213}, {"capacity": 100001, "embedding_len": 16, "field": 331, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 218, "size": 100001, "slot": 218}, {"capacity": 100002, "embedding_len": 16, "field": 332, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 219, "size": 100002, "slot": 219}, {"capacity": 1001, "embedding_len": 16, "field": 333, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 221, "size": 1001, "slot": 221}, {"capacity": 100001, "embedding_len": 16, "field": 334, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 222, "size": 100001, "slot": 222}, {"capacity": 100001, "embedding_len": 16, "field": 335, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 223, "size": 100001, "slot": 223}, {"capacity": 100001, "embedding_len": 16, "field": 336, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 224, "size": 100001, "slot": 224}, {"capacity": 500001, "embedding_len": 16, "field": 416, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 226, "size": 500001, "slot": 226}, {"capacity": 100001, "embedding_len": 16, "field": 417, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 227, "size": 100001, "slot": 227}, {"capacity": 1000001, "embedding_len": 16, "field": 418, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 228, "size": 1000001, "slot": 228}, {"capacity": 1000001, "embedding_len": 16, "field": 419, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 229, "size": 1000001, "slot": 229}, {"capacity": 1000001, "embedding_len": 16, "field": 420, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 230, "size": 1000001, "slot": 230}, {"capacity": 1000001, "embedding_len": 16, "field": 421, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 231, "size": 1000001, "slot": 231}, {"capacity": 1000001, "embedding_len": 16, "field": 422, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 232, "size": 1000001, "slot": 232}, {"capacity": 1000001, "embedding_len": 16, "field": 423, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 233, "size": 1000001, "slot": 233}, {"capacity": 1000001, "embedding_len": 16, "field": 424, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 234, "size": 1000001, "slot": 234}, {"capacity": 1000001, "embedding_len": 16, "field": 425, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 235, "size": 1000001, "slot": 235}, {"capacity": 1000001, "embedding_len": 16, "field": 426, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 236, "size": 1000001, "slot": 236}, {"capacity": 1000001, "embedding_len": 16, "field": 427, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 237, "size": 1000001, "slot": 237}, {"capacity": 1000001, "embedding_len": 16, "field": 428, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 238, "size": 1000001, "slot": 238}, {"capacity": 1000001, "embedding_len": 16, "field": 429, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 239, "size": 1000001, "slot": 239}, {"capacity": 100001, "embedding_len": 16, "field": 430, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 240, "size": 100001, "slot": 240}, {"capacity": 100001, "embedding_len": 16, "field": 431, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 241, "size": 100001, "slot": 241}, {"capacity": 1000001, "embedding_len": 16, "field": 432, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 242, "size": 1000001, "slot": 242}, {"capacity": 1000001, "embedding_len": 16, "field": 433, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 243, "size": 1000001, "slot": 243}, {"capacity": 500003, "embedding_len": 16, "field": 434, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 244, "size": 500003, "slot": 244}, {"capacity": 10000001, "embedding_len": 16, "field": 435, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 281, "size": 10000001, "slot": 281}, {"capacity": 0, "embedding_len": 16, "field": 439, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 285, "size": 10000001, "slot": 281}, {"capacity": 0, "embedding_len": 16, "field": 443, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 289, "size": 10000001, "slot": 281}, {"capacity": 10000001, "embedding_len": 16, "field": 436, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 282, "size": 10000001, "slot": 282}, {"capacity": 0, "embedding_len": 16, "field": 440, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 286, "size": 10000001, "slot": 282}, {"capacity": 0, "embedding_len": 16, "field": 444, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 290, "size": 10000001, "slot": 282}, {"capacity": 10000001, "embedding_len": 16, "field": 437, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 283, "size": 10000001, "slot": 283}, {"capacity": 0, "embedding_len": 16, "field": 441, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 287, "size": 10000001, "slot": 283}, {"capacity": 0, "embedding_len": 16, "field": 445, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 291, "size": 10000001, "slot": 283}, {"capacity": 10000001, "embedding_len": 16, "field": 438, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 284, "size": 10000001, "slot": 284}, {"capacity": 0, "embedding_len": 16, "field": 442, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 288, "size": 10000001, "slot": 284}, {"capacity": 0, "embedding_len": 16, "field": 446, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 292, "size": 10000001, "slot": 284}, {"capacity": 10000001, "embedding_len": 16, "field": 348, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 300, "size": 10000001, "slot": 300}, {"capacity": 10001, "embedding_len": 16, "field": 349, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 301, "size": 10001, "slot": 301}, {"capacity": 5000001, "embedding_len": 16, "field": 350, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 302, "size": 5000001, "slot": 302}, {"capacity": 1001, "embedding_len": 16, "field": 351, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 303, "size": 1001, "slot": 303}, {"capacity": 50001, "embedding_len": 16, "field": 352, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 304, "size": 50001, "slot": 304}, {"capacity": 100001, "embedding_len": 16, "field": 353, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 305, "size": 100001, "slot": 305}, {"capacity": 11, "embedding_len": 16, "field": 354, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 306, "size": 11, "slot": 306}, {"capacity": 101, "embedding_len": 16, "field": 355, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 307, "size": 101, "slot": 307}, {"capacity": 100001, "embedding_len": 16, "field": 356, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 308, "size": 100001, "slot": 308}, {"capacity": 1000001, "embedding_len": 16, "field": 357, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 309, "size": 1000001, "slot": 309}, {"capacity": 1000001, "embedding_len": 16, "field": 358, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 310, "size": 1000001, "slot": 310}, {"capacity": 100001, "embedding_len": 16, "field": 387, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 411, "size": 100001, "slot": 411}, {"capacity": 100001, "embedding_len": 16, "field": 388, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 412, "size": 100001, "slot": 412}, {"capacity": 100001, "embedding_len": 16, "field": 390, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 414, "size": 100001, "slot": 414}, {"capacity": 100001, "embedding_len": 16, "field": 391, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 415, "size": 100001, "slot": 415}, {"capacity": 11, "embedding_len": 16, "field": 408, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 500, "size": 11, "slot": 500}, {"capacity": 10001, "embedding_len": 16, "field": 411, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 627, "size": 10001, "slot": 627}, {"capacity": 1001, "embedding_len": 16, "field": 472, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 637, "size": 1001, "slot": 637}, {"capacity": 1001, "embedding_len": 16, "field": 473, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 638, "size": 1001, "slot": 638}, {"capacity": 1001, "embedding_len": 16, "field": 474, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 639, "size": 1001, "slot": 639}, {"capacity": 1001, "embedding_len": 16, "field": 475, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 640, "size": 1001, "slot": 640}, {"capacity": 1001, "embedding_len": 16, "field": 476, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 641, "size": 1001, "slot": 641}, {"capacity": 1001, "embedding_len": 16, "field": 477, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 642, "size": 1001, "slot": 642}, {"capacity": 1001, "embedding_len": 16, "field": 478, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 643, "size": 1001, "slot": 643}, {"capacity": 1001, "embedding_len": 16, "field": 479, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 644, "size": 1001, "slot": 644}, {"capacity": 1001, "embedding_len": 16, "field": 480, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 645, "size": 1001, "slot": 645}, {"capacity": 1001, "embedding_len": 16, "field": 481, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 646, "size": 1001, "slot": 646}, {"capacity": 1001, "embedding_len": 16, "field": 482, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 647, "size": 1001, "slot": 647}, {"capacity": 1001, "embedding_len": 16, "field": 483, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 648, "size": 1001, "slot": 648}, {"capacity": 1001, "embedding_len": 16, "field": 484, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 649, "size": 1001, "slot": 649}, {"capacity": 1001, "embedding_len": 16, "field": 485, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 650, "size": 1001, "slot": 650}, {"capacity": 1001, "embedding_len": 16, "field": 486, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 651, "size": 1001, "slot": 651}, {"capacity": 1001, "embedding_len": 16, "field": 487, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 652, "size": 1001, "slot": 652}, {"capacity": 1001, "embedding_len": 16, "field": 488, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 653, "size": 1001, "slot": 653}, {"capacity": 1001, "embedding_len": 16, "field": 489, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 654, "size": 1001, "slot": 654}, {"capacity": 1001, "embedding_len": 16, "field": 490, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 655, "size": 1001, "slot": 655}, {"capacity": 10001, "embedding_len": 32, "field": 465, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 859, "size": 10001, "slot": 859}, {"capacity": 1001, "embedding_len": 16, "field": 1, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 2, "size": 1001, "slot": 2}, {"capacity": 10001, "embedding_len": 16, "field": 3, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 4, "size": 10001, "slot": 4}, {"capacity": 10001, "embedding_len": 16, "field": 4, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 6, "size": 10001, "slot": 6}, {"capacity": 1001, "embedding_len": 16, "field": 9, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 11, "size": 1001, "slot": 11}, {"capacity": 1001, "embedding_len": 16, "field": 16, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 19, "size": 1001, "slot": 19}, {"capacity": 101, "embedding_len": 16, "field": 17, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 20, "size": 101, "slot": 20}, {"capacity": 101, "embedding_len": 16, "field": 22, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 25, "size": 101, "slot": 25}, {"capacity": 1001, "embedding_len": 16, "field": 58, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 69, "size": 1001, "slot": 69}, {"capacity": 1001, "embedding_len": 16, "field": 225, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 374, "size": 1001, "slot": 374}, {"capacity": 11, "embedding_len": 16, "field": 252, "mio_bucket_size": 0, "prefix": 0, "remap_slot": 388, "size": 11, "slot": 388}], "ps_capacity_gb": 19}}