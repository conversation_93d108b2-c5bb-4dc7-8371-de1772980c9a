import json
import pandas as pd
from collections import Counter
import numpy as np
from typing import List, Dict, Any, <PERSON><PERSON>

def jaccard_similarity(list1: List, list2: List) -> float:
    """
    计算两个列表的Jaccard相似度（交集/并集）
    """
    # 处理None值
    if list1 is None:
        list1 = []
    if list2 is None:
        list2 = []

    # 过滤掉None元素
    list1 = [x for x in list1 if x is not None]
    list2 = [x for x in list2 if x is not None]

    set1, set2 = set(list1), set(list2)
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    return intersection / union if union > 0 else 0.0

def cosine_similarity(list1: List, list2: List) -> float:
    """
    基于元素频次计算余弦相似度
    """
    # 处理None值
    if list1 is None:
        list1 = []
    if list2 is None:
        list2 = []

    # 过滤掉None元素
    list1 = [x for x in list1 if x is not None]
    list2 = [x for x in list2 if x is not None]

    counter1 = Counter(list1)
    counter2 = Counter(list2)

    # 获取所有唯一元素
    all_elements = set(counter1.keys()) | set(counter2.keys())

    if not all_elements:
        return 0.0

    # 构建向量
    vec1 = [counter1.get(elem, 0) for elem in all_elements]
    vec2 = [counter2.get(elem, 0) for elem in all_elements]

    # 计算余弦相似度
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    norm1 = sum(a * a for a in vec1) ** 0.5
    norm2 = sum(b * b for b in vec2) ** 0.5

    return dot_product / (norm1 * norm2) if norm1 > 0 and norm2 > 0 else 0.0

def overlap_coefficient(list1: List, list2: List) -> float:
    """
    计算重叠系数（交集/较小集合的大小）
    """
    # 处理None值
    if list1 is None:
        list1 = []
    if list2 is None:
        list2 = []

    # 过滤掉None元素
    list1 = [x for x in list1 if x is not None]
    list2 = [x for x in list2 if x is not None]

    set1, set2 = set(list1), set(list2)
    intersection = len(set1.intersection(set2))
    min_size = min(len(set1), len(set2))
    return intersection / min_size if min_size > 0 else 0.0

def sequence_similarity(list1: List, list2: List) -> float:
    """
    计算序列相似度（考虑顺序的相似度）
    使用最长公共子序列的思想
    """
    # 处理None值
    if list1 is None:
        list1 = []
    if list2 is None:
        list2 = []

    # 过滤掉None元素
    list1 = [x for x in list1 if x is not None]
    list2 = [x for x in list2 if x is not None]

    if not list1 or not list2:
        return 0.0

    # 简化版本：计算相同位置相同元素的比例
    min_len = min(len(list1), len(list2))
    max_len = max(len(list1), len(list2))

    same_position_count = sum(1 for i in range(min_len) if list1[i] == list2[i])
    return same_position_count / max_len

def calculate_basic_stats(list1: List, list2: List) -> Dict[str, Any]:
    """
    计算基本统计信息
    """
    # 处理None值
    if list1 is None:
        list1 = []
    if list2 is None:
        list2 = []

    # 过滤掉None元素
    list1 = [x for x in list1 if x is not None]
    list2 = [x for x in list2 if x is not None]

    return {
        'list1_length': len(list1),
        'list2_length': len(list2),
        'list1_unique': len(set(list1)),
        'list2_unique': len(set(list2)),
        'common_elements': len(set(list1).intersection(set(list2))),
        'total_unique_elements': len(set(list1).union(set(list2)))
    }

def analyze_field_similarity(list1: List, list2: List, field_name: str) -> Dict[str, Any]:
    """
    分析单个字段的两个列表的相似程度
    """
    # 基本统计
    stats = calculate_basic_stats(list1, list2)
    
    # 相似度指标
    similarities = {
        'jaccard_similarity': jaccard_similarity(list1, list2),
        'cosine_similarity': cosine_similarity(list1, list2),
        'overlap_coefficient': overlap_coefficient(list1, list2),
        'sequence_similarity': sequence_similarity(list1, list2)
    }
    
    # 合并结果
    result = {
        'field_name': field_name,
        **stats,
        **similarities
    }
    
    return result

def analyze_json_similarity(json_file_path: str, output_csv_path: str = None) -> pd.DataFrame:
    """
    分析JSON文件中每个字段对应两个列表的相似程度
    """
    print(f"正在分析文件: {json_file_path}")
    
    # 读取JSON文件
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    all_results = []
    
    # 遍历每个对象
    for obj_idx, obj in enumerate(data):
        print(f"\n=== 分析对象 {obj_idx + 1} ===")
        
        # 遍历每个字段
        for field_name, field_data in obj.items():
            try:
                if isinstance(field_data, list) and len(field_data) == 2:
                    list1, list2 = field_data[0], field_data[1]

                    # 处理None值
                    if list1 is None:
                        list1 = []
                    if list2 is None:
                        list2 = []

                    print(f"分析字段: {field_name}")
                    print(f"  列表1长度: {len(list1) if list1 is not None else 0}, 列表2长度: {len(list2) if list2 is not None else 0}")

                    # 分析相似度
                    result = analyze_field_similarity(list1, list2, field_name)
                    result['object_index'] = obj_idx + 1

                    all_results.append(result)

                    # 打印主要指标
                    print(f"  Jaccard相似度: {result['jaccard_similarity']:.4f}")
                    print(f"  余弦相似度: {result['cosine_similarity']:.4f}")
                    print(f"  重叠系数: {result['overlap_coefficient']:.4f}")
                    print(f"  序列相似度: {result['sequence_similarity']:.4f}")
                else:
                    print(f"跳过字段 {field_name}: 不是包含两个列表的结构")
            except Exception as e:
                print(f"处理字段 {field_name} 时出错: {e}")
                continue
    
    # 转换为DataFrame
    df = pd.DataFrame(all_results)
    
    # 重新排列列的顺序
    column_order = [
        'object_index', 'field_name', 
        'list1_length', 'list2_length', 'list1_unique', 'list2_unique',
        'common_elements', 'total_unique_elements',
        'jaccard_similarity', 'cosine_similarity', 'overlap_coefficient', 'sequence_similarity'
    ]
    df = df[column_order]
    
    # 保存到CSV
    if output_csv_path:
        df.to_csv(output_csv_path, index=False, encoding='utf-8')
        print(f"\n结果已保存到: {output_csv_path}")
    
    return df

def generate_summary_report(df: pd.DataFrame) -> None:
    """
    生成汇总报告
    """
    print("\n" + "="*60)
    print("相似度分析汇总报告")
    print("="*60)
    
    # 按字段类型分组统计
    print("\n各字段平均相似度:")
    similarity_cols = ['jaccard_similarity', 'cosine_similarity', 'overlap_coefficient', 'sequence_similarity']
    
    field_summary = df.groupby('field_name')[similarity_cols].mean()
    print(field_summary.round(4))
    
    # 整体统计
    print(f"\n整体统计:")
    print(f"总共分析了 {len(df)} 个字段对")
    print(f"平均Jaccard相似度: {df['jaccard_similarity'].mean():.4f}")
    print(f"平均余弦相似度: {df['cosine_similarity'].mean():.4f}")
    print(f"平均重叠系数: {df['overlap_coefficient'].mean():.4f}")
    print(f"平均序列相似度: {df['sequence_similarity'].mean():.4f}")
    
    # 找出最相似和最不相似的字段
    print(f"\nJaccard相似度最高的字段:")
    top_similar = df.nlargest(3, 'jaccard_similarity')[['object_index', 'field_name', 'jaccard_similarity']]
    for _, row in top_similar.iterrows():
        print(f"  对象{row['object_index']} - {row['field_name']}: {row['jaccard_similarity']:.4f}")
    
    print(f"\nJaccard相似度最低的字段:")
    low_similar = df.nsmallest(3, 'jaccard_similarity')[['object_index', 'field_name', 'jaccard_similarity']]
    for _, row in low_similar.iterrows():
        print(f"  对象{row['object_index']} - {row['field_name']}: {row['jaccard_similarity']:.4f}")

def main():
    """
    主函数
    """
    # 文件路径
    json_file = 'cate2cate对比.json'
    output_csv = 'similarity_analysis_results.csv'
    
    try:
        # 分析相似度
        df = analyze_json_similarity(json_file, output_csv)
        
        # 生成汇总报告
        generate_summary_report(df)
        
        # 显示前几行结果
        print(f"\n前5行详细结果:")
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        print(df.head().to_string(index=False))
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file}")
    except Exception as e:
        print(f"分析过程中出现错误: {e}")

if __name__ == "__main__":
    main()
