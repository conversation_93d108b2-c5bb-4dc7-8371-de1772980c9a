{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                   name   origin_shape  current_shape    type\n", "0   QUEUE_SOFT/share_bottom_layer_0/w:0   (7821, 1024)   (8909, 1024)  change\n", "1              rocket_upper_layer_2/w:0     (560, 128)    (1040, 128)  change\n", "2          share_bottom_layer_new_0/w:0  (13835, 1024)  (14923, 1024)  change\n", "3                   LayerNorm_35/beta:0     (13835, 1)           None  delete\n", "4                  LayerNorm_35/gamma:0     (13835, 1)           None  delete\n", "5                   LayerNorm_36/beta:0     (13835, 1)           None  delete\n", "6                  LayerNorm_36/gamma:0     (13835, 1)           None  delete\n", "7                                 b_0:0     (13835, 1)           None  delete\n", "8                                 b_1:0     (13835, 1)           None  delete\n", "9                                 u_0:0    (13835, 64)           None  delete\n", "10                                u_1:0    (13835, 64)           None  delete\n", "11                                v_0:0    (64, 13835)           None  delete\n", "12                                v_1:0    (64, 13835)           None  delete\n", "13       ExtractUserDenseGrpoFea/beta:0           None      (1024, 1)     add\n", "14      ExtractUserDenseGrpoFea/gamma:0           None      (1024, 1)     add\n", "15         dcn_layer_0/LayerNorm/beta:0           None     (14923, 1)     add\n", "16        dcn_layer_0/LayerNorm/gamma:0           None     (14923, 1)     add\n", "17                      dcn_layer_0/b:0           None     (14923, 1)     add\n", "18                      dcn_layer_0/u:0           None    (14923, 64)     add\n", "19                      dcn_layer_0/v:0           None    (64, 14923)     add\n", "20         dcn_layer_1/LayerNorm/beta:0           None     (14923, 1)     add\n", "21        dcn_layer_1/LayerNorm/gamma:0           None     (14923, 1)     add\n", "22                      dcn_layer_1/b:0           None     (14923, 1)     add\n", "23                      dcn_layer_1/u:0           None    (14923, 64)     add\n", "24                      dcn_layer_1/v:0           None    (64, 14923)     add\n", "25      first_stage_ctcvr_sparse/beta:0           None        (16, 1)     add\n", "26     first_stage_ctcvr_sparse/gamma:0           None        (16, 1)     add\n", "27                   grpo_sparse/beta:0           None        (64, 1)     add\n", "28                  grpo_sparse/gamma:0           None        (64, 1)     add\n", "29        inner_order_num_sparse/beta:0           None        (16, 1)     add\n", "30       inner_order_num_sparse/gamma:0           None        (16, 1)     add\n", "31     rank_context_index_sparse/beta:0           None       (352, 1)     add\n", "32    rank_context_index_sparse/gamma:0           None       (352, 1)     add\n", "33           rank_context_sparse/beta:0           None        (80, 1)     add\n", "34          rank_context_sparse/gamma:0           None        (80, 1)     add\n", "35                 ue_num_sparse/beta:0           None        (16, 1)     add\n", "36                ue_num_sparse/gamma:0           None        (16, 1)     add\n", "Dense Extra Diff: \n", "                                   name origin_shape current_shape    type\n", "0   QUEUE_SOFT/share_bottom_layer_0/w:0   (8008704,)    (9122816,)  change\n", "1              rocket_upper_layer_2/w:0     (71680,)     (133120,)  change\n", "2          share_bottom_layer_new_0/w:0  (14167040,)   (15281152,)  change\n", "3                   LayerNorm_35/beta:0     (13835,)          None  delete\n", "4                  LayerNorm_35/gamma:0     (13835,)          None  delete\n", "5                   LayerNorm_36/beta:0     (13835,)          None  delete\n", "6                  LayerNorm_36/gamma:0     (13835,)          None  delete\n", "7                                 b_0:0     (13835,)          None  delete\n", "8                                 b_1:0     (13835,)          None  delete\n", "9                                 u_0:0    (885440,)          None  delete\n", "10                                u_1:0    (885440,)          None  delete\n", "11                                v_0:0    (885440,)          None  delete\n", "12                                v_1:0    (885440,)          None  delete\n", "13       ExtractUserDenseGrpoFea/beta:0         None       (1024,)     add\n", "14      ExtractUserDenseGrpoFea/gamma:0         None       (1024,)     add\n", "15         dcn_layer_0/LayerNorm/beta:0         None      (14923,)     add\n", "16        dcn_layer_0/LayerNorm/gamma:0         None      (14923,)     add\n", "17                      dcn_layer_0/b:0         None      (14923,)     add\n", "18                      dcn_layer_0/u:0         None     (955072,)     add\n", "19                      dcn_layer_0/v:0         None     (955072,)     add\n", "20         dcn_layer_1/LayerNorm/beta:0         None      (14923,)     add\n", "21        dcn_layer_1/LayerNorm/gamma:0         None      (14923,)     add\n", "22                      dcn_layer_1/b:0         None      (14923,)     add\n", "23                      dcn_layer_1/u:0         None     (955072,)     add\n", "24                      dcn_layer_1/v:0         None     (955072,)     add\n", "25      first_stage_ctcvr_sparse/beta:0         None         (16,)     add\n", "26     first_stage_ctcvr_sparse/gamma:0         None         (16,)     add\n", "27                   grpo_sparse/beta:0         None         (64,)     add\n", "28                  grpo_sparse/gamma:0         None         (64,)     add\n", "29        inner_order_num_sparse/beta:0         None         (16,)     add\n", "30       inner_order_num_sparse/gamma:0         None         (16,)     add\n", "31     rank_context_index_sparse/beta:0         None        (352,)     add\n", "32    rank_context_index_sparse/gamma:0         None        (352,)     add\n", "33           rank_context_sparse/beta:0         None         (80,)     add\n", "34          rank_context_sparse/gamma:0         None         (80,)     add\n", "35                 ue_num_sparse/beta:0         None         (16,)     add\n", "36                ue_num_sparse/gamma:0         None         (16,)     add\n", "\n", "粘贴到my_load_dense_func开头的origin_weight处\n", "{'QUEUE_SOFT/share_bottom_layer_0/w:0': (7821, 1024), 'rocket_upper_layer_2/w:0': (560, 128), 'share_bottom_layer_new_0/w:0': (13835, 1024)}\n"]}], "source": ["old_yaml = 'suntianyu06_dsp_sty_ue_cross.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_context_re_grpo_v1 (1).yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)\n", "\n", "weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}\n", "extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}\n", "print()\n", "print('粘贴到my_load_dense_func开头的origin_weight处')\n", "print(weight_diff_change)\n", "# print(extra_diff_change)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # type==change，原来的小尺寸、现在变大的weight和它的原始shape\n", "    origin_weight = {'QUEUE_SOFT/share_bottom_layer_0/w:0': (7821, 1024), 'rocket_upper_layer_2/w:0': (560, 128), 'share_bottom_layer_new_0/w:0': (13835, 1024)}\n", "    ln_map = {\"dcn_layer_0/LayerNorm/beta:0\":\"LayerNorm_35/beta:0\",\"dcn_layer_0/LayerNorm/gamma:0\":\"LayerNorm_35/gamma:0\",\"dcn_layer_1/LayerNorm/beta:0\":\"LayerNorm_36/beta:0\",\"dcn_layer_1/LayerNorm/gamma:0\":\"LayerNorm_36/gamma:0\"}\n", "    ub_map = {\"dcn_layer_0/u:0\":\"u_0:0\",\"dcn_layer_1/u:0\":\"u_1:0\",\"dcn_layer_0/b:0\":\"b_0:0\",\"dcn_layer_1/b:0\":\"b_1:0\"}\n", "    v_map = {\"dcn_layer_0/v:0\":\"v_0:0\",\"dcn_layer_1/v:0\":\"v_1:0\"}\n", "\n", "    dcn_weight_1 = {'LayerNorm_35/beta:0': (13835, 1), 'LayerNorm_35/gamma:0': (13835, 1), 'LayerNorm_36/beta:0': (13835, 1), 'LayerNorm_36/gamma:0': (13835, 1), }\n", "    dcn_weight_2 = {'b_0:0': (13835, 1), 'b_1:0': (13835, 1),  'u_0:0': (13835, 64), 'u_1:0': (13835, 64),}\n", "    dcn_weight_3 = {'v_0:0': (64, 13835), 'v_1:0': (64, 13835)}\n", "\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "\n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name in origin_weight:\n", "            origin_size = origin_weight[var_name][0]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name in ln_map:\n", "            old_name = ln_map[var_name]\n", "            origin_size = dcn_weight_1[old_name][0]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[old_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[old_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name in ub_map:\n", "            old_name = ub_map[var_name]\n", "            origin_size = dcn_weight_2[old_name][0]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[old_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[old_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name in v_map:\n", "            old_name = v_map[var_name]\n", "            origin_size = dcn_weight_3[old_name][1]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[old_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[old_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:, :origin_size] = ori_weight\n", "            new_extra[:, :origin_size, :] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(ExtractUserDenseGrpoFea/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractUserDenseGrpoFea/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(grpo_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(grpo_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(inner_order_num_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(inner_order_num_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ue_num_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ue_num_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(first_stage_ctcvr_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(first_stage_ctcvr_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(rank_context_index_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(rank_context_index_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(rank_context_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(rank_context_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(dcn_layer_0/u:0) 不存在，其值由dcn_layer_0/u:0初始化, size is 955072 and 955072\n", "加载的 dense variable(dcn_layer_0/v:0) 不存在，其值由dcn_layer_0/v:0初始化, size is 955072 and 955072\n", "加载的 dense variable(dcn_layer_0/b:0) 不存在，其值由dcn_layer_0/b:0初始化, size is 14923 and 14923\n", "加载的 dense variable(dcn_layer_0/LayerNorm/beta:0) 不存在，其值由dcn_layer_0/LayerNorm/beta:0初始化, size is 14923 and 14923\n", "加载的 dense variable(dcn_layer_0/LayerNorm/gamma:0) 不存在，其值由dcn_layer_0/LayerNorm/gamma:0初始化, size is 14923 and 14923\n", "加载的 dense variable(dcn_layer_1/u:0) 不存在，其值由dcn_layer_1/u:0初始化, size is 955072 and 955072\n", "加载的 dense variable(dcn_layer_1/v:0) 不存在，其值由dcn_layer_1/v:0初始化, size is 955072 and 955072\n", "加载的 dense variable(dcn_layer_1/b:0) 不存在，其值由dcn_layer_1/b:0初始化, size is 14923 and 14923\n", "加载的 dense variable(dcn_layer_1/LayerNorm/beta:0) 不存在，其值由dcn_layer_1/LayerNorm/beta:0初始化, size is 14923 and 14923\n", "加载的 dense variable(dcn_layer_1/LayerNorm/gamma:0) 不存在，其值由dcn_layer_1/LayerNorm/gamma:0初始化, size is 14923 and 14923\n", "加载的 dense variable(QUEUE_SOFT/share_bottom_layer_0/w:0) 不存在，其值由QUEUE_SOFT/share_bottom_layer_0/w:0初始化, size is 9122816 and 9122816\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) 不存在，其值由share_bottom_layer_new_0/w:0初始化, size is 15281152 and 15281152\n", "加载的 dense variable(rocket_upper_layer_2/w:0) 不存在，其值由rocket_upper_layer_2/w:0初始化, size is 133120 and 133120\n", "加载的 dense variable(u_0:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(v_0:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(b_0:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(LayerNorm_35/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(LayerNorm_35/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(u_1:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(v_1:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(b_1:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(LayerNorm_36/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(LayerNorm_36/gamma:0) 在运行时不存在，其值被忽略。\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}