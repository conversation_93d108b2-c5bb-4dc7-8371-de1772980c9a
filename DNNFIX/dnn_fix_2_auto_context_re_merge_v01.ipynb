{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                                 name   origin_shape  current_shape    type\n", "0                        dcn_layer_0/LayerNorm/beta:0     (14923, 1)     (16654, 1)  change\n", "1                       dcn_layer_0/LayerNorm/gamma:0     (14923, 1)     (16654, 1)  change\n", "2                                     dcn_layer_0/b:0     (14923, 1)     (16654, 1)  change\n", "3                                     dcn_layer_0/u:0    (14923, 64)    (16654, 64)  change\n", "4                                     dcn_layer_0/v:0    (64, 14923)    (64, 16654)  change\n", "5                        dcn_layer_1/LayerNorm/beta:0     (14923, 1)     (16654, 1)  change\n", "6                       dcn_layer_1/LayerNorm/gamma:0     (14923, 1)     (16654, 1)  change\n", "7                                     dcn_layer_1/b:0     (14923, 1)     (16654, 1)  change\n", "8                                     dcn_layer_1/u:0    (14923, 64)    (16654, 64)  change\n", "9                                     dcn_layer_1/v:0    (64, 14923)    (64, 16654)  change\n", "10                           rocket_upper_layer_2/w:0    (1008, 128)     (864, 128)  change\n", "11                       share_bottom_layer_new_0/w:0  (14923, 1024)  (16654, 1024)  change\n", "12                    first_stage_ctcvr_sparse/beta:0        (16, 1)           None  delete\n", "13                   first_stage_ctcvr_sparse/gamma:0        (16, 1)           None  delete\n", "14                      inner_order_num_sparse/beta:0        (16, 1)           None  delete\n", "15                     inner_order_num_sparse/gamma:0        (16, 1)           None  delete\n", "16                   rank_context_index_sparse/beta:0       (320, 1)           None  delete\n", "17                  rank_context_index_sparse/gamma:0       (320, 1)           None  delete\n", "18                         rank_context_sparse/beta:0        (80, 1)           None  delete\n", "19                        rank_context_sparse/gamma:0        (80, 1)           None  delete\n", "20                               ue_num_sparse/beta:0        (16, 1)           None  delete\n", "21                              ue_num_sparse/gamma:0        (16, 1)           None  delete\n", "22            ExtractDenseUserLlmDpskValidUser/beta:0           None       (256, 1)     add\n", "23           ExtractDenseUserLlmDpskValidUser/gamma:0           None       (256, 1)     add\n", "24                         ExtractGoodsHetuEmb/beta:0           None       (128, 1)     add\n", "25                        ExtractGoodsHetuEmb/gamma:0           None       (128, 1)     add\n", "26                   ExtractPhotoTextEmbedding/beta:0           None       (256, 1)     add\n", "27                  ExtractPhotoTextEmbedding/gamma:0           None       (256, 1)     add\n", "28                           ExtractPidHetuEmb/beta:0           None       (128, 1)     add\n", "29                          ExtractPidHetuEmb/gamma:0           None       (128, 1)     add\n", "30                            ExtractPidSim128/beta:0           None         (1, 1)     add\n", "31                           ExtractPidSim128/gamma:0           None         (1, 1)     add\n", "32                             ExtractPidSim1k/beta:0           None         (1, 1)     add\n", "33                            ExtractPidSim1k/gamma:0           None         (1, 1)     add\n", "34                            ExtractPidSim512/beta:0           None         (1, 1)     add\n", "35                           ExtractPidSim512/gamma:0           None         (1, 1)     add\n", "36    attention_layer/long_recent_attk_trans_matrix:0           None       (56, 32)     add\n", "37    attention_layer/long_recent_attq_trans_matrix:0           None       (48, 32)     add\n", "38    attention_layer/long_recent_attv_trans_matrix:0           None       (56, 32)     add\n", "39                      gc_recent_attk_trans_matrix:0           None       (60, 32)     add\n", "40                      gc_recent_attq_trans_matrix:0           None       (48, 32)     add\n", "41                      gc_recent_attv_trans_matrix:0           None       (60, 32)     add\n", "42           gc_self_attention_layer/LayerNorm/beta:0           None        (64, 1)     add\n", "43          gc_self_attention_layer/LayerNorm/gamma:0           None        (64, 1)     add\n", "44                        gc_self_attention_layer/b:0           None        (64, 1)     add\n", "45  gc_self_attention_layer/gc_self_attnk_trans_ma...           None       (60, 32)     add\n", "46  gc_self_attention_layer/gc_self_attnq_trans_ma...           None       (60, 32)     add\n", "47  gc_self_attention_layer/gc_self_attnv_trans_ma...           None       (60, 32)     add\n", "48                        gc_self_attention_layer/w:0           None     (1600, 64)     add\n", "49         long_self_attention_layer/LayerNorm/beta:0           None        (64, 1)     add\n", "50        long_self_attention_layer/LayerNorm/gamma:0           None        (64, 1)     add\n", "51                      long_self_attention_layer/b:0           None        (64, 1)     add\n", "52  long_self_attention_layer/long_self_attnk_tran...           None       (56, 32)     add\n", "53  long_self_attention_layer/long_self_attnq_tran...           None       (56, 32)     add\n", "54  long_self_attention_layer/long_self_attnv_tran...           None       (56, 32)     add\n", "55                      long_self_attention_layer/w:0           None     (3200, 64)     add\n", "56                               photo_content/beta:0           None       (176, 1)     add\n", "57                              photo_content/gamma:0           None       (176, 1)     add\n", "58                                user_predict/beta:0           None        (32, 1)     add\n", "59                               user_predict/gamma:0           None        (32, 1)     add\n", "Dense Extra Diff: \n", "                                                 name origin_shape current_shape    type\n", "0                        dcn_layer_0/LayerNorm/beta:0     (14923,)      (16654,)  change\n", "1                       dcn_layer_0/LayerNorm/gamma:0     (14923,)      (16654,)  change\n", "2                                     dcn_layer_0/b:0     (14923,)      (16654,)  change\n", "3                                     dcn_layer_0/u:0    (955072,)    (1065856,)  change\n", "4                                     dcn_layer_0/v:0    (955072,)    (1065856,)  change\n", "5                        dcn_layer_1/LayerNorm/beta:0     (14923,)      (16654,)  change\n", "6                       dcn_layer_1/LayerNorm/gamma:0     (14923,)      (16654,)  change\n", "7                                     dcn_layer_1/b:0     (14923,)      (16654,)  change\n", "8                                     dcn_layer_1/u:0    (955072,)    (1065856,)  change\n", "9                                     dcn_layer_1/v:0    (955072,)    (1065856,)  change\n", "10                           rocket_upper_layer_2/w:0    (129024,)     (110592,)  change\n", "11                       share_bottom_layer_new_0/w:0  (15281152,)   (17053696,)  change\n", "12                    first_stage_ctcvr_sparse/beta:0        (16,)          None  delete\n", "13                   first_stage_ctcvr_sparse/gamma:0        (16,)          None  delete\n", "14                      inner_order_num_sparse/beta:0        (16,)          None  delete\n", "15                     inner_order_num_sparse/gamma:0        (16,)          None  delete\n", "16                   rank_context_index_sparse/beta:0       (320,)          None  delete\n", "17                  rank_context_index_sparse/gamma:0       (320,)          None  delete\n", "18                         rank_context_sparse/beta:0        (80,)          None  delete\n", "19                        rank_context_sparse/gamma:0        (80,)          None  delete\n", "20                               ue_num_sparse/beta:0        (16,)          None  delete\n", "21                              ue_num_sparse/gamma:0        (16,)          None  delete\n", "22            ExtractDenseUserLlmDpskValidUser/beta:0         None        (256,)     add\n", "23           ExtractDenseUserLlmDpskValidUser/gamma:0         None        (256,)     add\n", "24                         ExtractGoodsHetuEmb/beta:0         None        (128,)     add\n", "25                        ExtractGoodsHetuEmb/gamma:0         None        (128,)     add\n", "26                   ExtractPhotoTextEmbedding/beta:0         None        (256,)     add\n", "27                  ExtractPhotoTextEmbedding/gamma:0         None        (256,)     add\n", "28                           ExtractPidHetuEmb/beta:0         None        (128,)     add\n", "29                          ExtractPidHetuEmb/gamma:0         None        (128,)     add\n", "30                            ExtractPidSim128/beta:0         None          (1,)     add\n", "31                           ExtractPidSim128/gamma:0         None          (1,)     add\n", "32                             ExtractPidSim1k/beta:0         None          (1,)     add\n", "33                            ExtractPidSim1k/gamma:0         None          (1,)     add\n", "34                            ExtractPidSim512/beta:0         None          (1,)     add\n", "35                           ExtractPidSim512/gamma:0         None          (1,)     add\n", "36    attention_layer/long_recent_attk_trans_matrix:0         None       (1792,)     add\n", "37    attention_layer/long_recent_attq_trans_matrix:0         None       (1536,)     add\n", "38    attention_layer/long_recent_attv_trans_matrix:0         None       (1792,)     add\n", "39                      gc_recent_attk_trans_matrix:0         None       (1920,)     add\n", "40                      gc_recent_attq_trans_matrix:0         None       (1536,)     add\n", "41                      gc_recent_attv_trans_matrix:0         None       (1920,)     add\n", "42           gc_self_attention_layer/LayerNorm/beta:0         None         (64,)     add\n", "43          gc_self_attention_layer/LayerNorm/gamma:0         None         (64,)     add\n", "44                        gc_self_attention_layer/b:0         None         (64,)     add\n", "45  gc_self_attention_layer/gc_self_attnk_trans_ma...         None       (1920,)     add\n", "46  gc_self_attention_layer/gc_self_attnq_trans_ma...         None       (1920,)     add\n", "47  gc_self_attention_layer/gc_self_attnv_trans_ma...         None       (1920,)     add\n", "48                        gc_self_attention_layer/w:0         None     (102400,)     add\n", "49         long_self_attention_layer/LayerNorm/beta:0         None         (64,)     add\n", "50        long_self_attention_layer/LayerNorm/gamma:0         None         (64,)     add\n", "51                      long_self_attention_layer/b:0         None         (64,)     add\n", "52  long_self_attention_layer/long_self_attnk_tran...         None       (1792,)     add\n", "53  long_self_attention_layer/long_self_attnq_tran...         None       (1792,)     add\n", "54  long_self_attention_layer/long_self_attnv_tran...         None       (1792,)     add\n", "55                      long_self_attention_layer/w:0         None     (204800,)     add\n", "56                               photo_content/beta:0         None        (176,)     add\n", "57                              photo_content/gamma:0         None        (176,)     add\n", "58                                user_predict/beta:0         None         (32,)     add\n", "59                               user_predict/gamma:0         None         (32,)     add\n", "\n", "粘贴到my_load_dense_func开头的origin_weight处\n", "{'dcn_layer_0/LayerNorm/beta:0': (14923, 1), 'dcn_layer_0/LayerNorm/gamma:0': (14923, 1), 'dcn_layer_0/b:0': (14923, 1), 'dcn_layer_0/u:0': (14923, 64), 'dcn_layer_0/v:0': (64, 14923), 'dcn_layer_1/LayerNorm/beta:0': (14923, 1), 'dcn_layer_1/LayerNorm/gamma:0': (14923, 1), 'dcn_layer_1/b:0': (14923, 1), 'dcn_layer_1/u:0': (14923, 64), 'dcn_layer_1/v:0': (64, 14923), 'rocket_upper_layer_2/w:0': (1008, 128), 'share_bottom_layer_new_0/w:0': (14923, 1024)}\n"]}], "source": ["old_yaml = 'xiatian06_dsp_lps_sv_xt_context_re_grpo_v2.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_context_re_merge_v01.yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)\n", "\n", "weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}\n", "extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}\n", "print()\n", "print('粘贴到my_load_dense_func开头的origin_weight处')\n", "print(weight_diff_change)\n", "# print(extra_diff_change)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["(['dcn_layer_0/u:0',\n", "  'dcn_layer_0/b:0',\n", "  'dcn_layer_0/LayerNorm/beta:0',\n", "  'dcn_layer_0/LayerNorm/gamma:0',\n", "  'dcn_layer_1/u:0',\n", "  'dcn_layer_1/b:0',\n", "  'dcn_layer_1/LayerNorm/beta:0',\n", "  'dcn_layer_1/LayerNorm/gamma:0'],\n", " ['dcn_layer_0/v:0', 'dcn_layer_1/v:0'])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["list_1 = []\n", "list_2 = []\n", "for i in dense_new.var_name_list:\n", "    if 'dcn_layer' in i:\n", "        if 'v' in i:\n", "            list_2.append(i)\n", "        else:\n", "            list_1.append(i)\n", "list_1,list_2"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # type==change，原来的小尺寸、现在变大的weight和它的原始shape\n", "    list_1 = ['dcn_layer_0/u:0',\n", "                'dcn_layer_0/b:0',\n", "                'dcn_layer_0/LayerNorm/beta:0',\n", "                'dcn_layer_0/LayerNorm/gamma:0',\n", "                'dcn_layer_1/u:0',\n", "                'dcn_layer_1/b:0',\n", "                'dcn_layer_1/LayerNorm/beta:0',\n", "                'dcn_layer_1/LayerNorm/gamma:0']\n", "    list_2 = ['dcn_layer_0/v:0', 'dcn_layer_1/v:0']\n", "\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = tf_weight\n", "    extra = ps_extra\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 这里是旧权重尺寸小的情况，只加载DCN\n", "    for var_name in list(tf_weight): \n", "        if var_name in list_1:\n", "            # 原来的 weight\n", "            origin_size = 14923\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            if len(new_weight.shape) == 1:\n", "                new_weight[:origin_size] = ori_weight\n", "                new_extra[:origin_size, :] = ori_extra\n", "            else:\n", "                new_weight[:origin_size, :] = ori_weight\n", "                new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            tf_weight[var_name]  = new_weight\n", "            ps_extra[var_name] = get_extra(new_extra)\n", "        elif var_name in list_2:\n", "            origin_size = 14923\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            if len(new_weight.shape) == 1:\n", "                new_weight[:origin_size] = ori_weight\n", "                new_extra[:origin_size, :] = ori_extra\n", "            else:\n", "                new_weight[:, :origin_size] = ori_weight\n", "                new_extra[:, :origin_size, :] = ori_extra\n", "            # 回填\n", "            tf_weight[var_name]  = new_weight\n", "            ps_extra[var_name] = get_extra(new_extra)\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}