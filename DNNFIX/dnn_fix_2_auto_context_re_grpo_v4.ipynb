{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                     name origin_shape current_shape    type\n", "0                rocket_upper_layer_2/w:0  (1008, 128)    (864, 128)  change\n", "1            dcn_layer_1/LayerNorm/beta:0   (14923, 1)          None  delete\n", "2           dcn_layer_1/LayerNorm/gamma:0   (14923, 1)          None  delete\n", "3         first_stage_ctcvr_sparse/beta:0      (16, 1)          None  delete\n", "4        first_stage_ctcvr_sparse/gamma:0      (16, 1)          None  delete\n", "5           inner_order_num_sparse/beta:0      (16, 1)          None  delete\n", "6          inner_order_num_sparse/gamma:0      (16, 1)          None  delete\n", "7        rank_context_index_sparse/beta:0     (320, 1)          None  delete\n", "8       rank_context_index_sparse/gamma:0     (320, 1)          None  delete\n", "9              rank_context_sparse/beta:0      (80, 1)          None  delete\n", "10            rank_context_sparse/gamma:0      (80, 1)          None  delete\n", "11                   ue_num_sparse/beta:0      (16, 1)          None  delete\n", "12                  ue_num_sparse/gamma:0      (16, 1)          None  delete\n", "13   inner_order_rank_index_sparse/beta:0         None       (16, 1)     add\n", "14  inner_order_rank_index_sparse/gamma:0         None       (16, 1)     add\n", "15            ue_rank_index_sparse/beta:0         None      (288, 1)     add\n", "16           ue_rank_index_sparse/gamma:0         None      (288, 1)     add\n", "Dense Extra Diff: \n", "                                     name origin_shape current_shape    type\n", "0                rocket_upper_layer_2/w:0    (129024,)     (110592,)  change\n", "1            dcn_layer_1/LayerNorm/beta:0     (14923,)          None  delete\n", "2           dcn_layer_1/LayerNorm/gamma:0     (14923,)          None  delete\n", "3         first_stage_ctcvr_sparse/beta:0        (16,)          None  delete\n", "4        first_stage_ctcvr_sparse/gamma:0        (16,)          None  delete\n", "5           inner_order_num_sparse/beta:0        (16,)          None  delete\n", "6          inner_order_num_sparse/gamma:0        (16,)          None  delete\n", "7        rank_context_index_sparse/beta:0       (320,)          None  delete\n", "8       rank_context_index_sparse/gamma:0       (320,)          None  delete\n", "9              rank_context_sparse/beta:0        (80,)          None  delete\n", "10            rank_context_sparse/gamma:0        (80,)          None  delete\n", "11                   ue_num_sparse/beta:0        (16,)          None  delete\n", "12                  ue_num_sparse/gamma:0        (16,)          None  delete\n", "13   inner_order_rank_index_sparse/beta:0         None         (16,)     add\n", "14  inner_order_rank_index_sparse/gamma:0         None         (16,)     add\n", "15            ue_rank_index_sparse/beta:0         None        (288,)     add\n", "16           ue_rank_index_sparse/gamma:0         None        (288,)     add\n", "\n", "粘贴到my_load_dense_func开头的origin_weight处\n", "{'rocket_upper_layer_2/w:0': (1008, 128)}\n"]}], "source": ["old_yaml = 'xiatian06_dsp_lps_sv_xt_context_re_grpo_v2_finalfix.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_context_re_grpo_v4.yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)\n", "\n", "weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}\n", "extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}\n", "print()\n", "print('粘贴到my_load_dense_func开头的origin_weight处')\n", "print(weight_diff_change)\n", "# print(extra_diff_change)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DenseTable: xiatian06_dsp_lps_sv_xt_context_re_grpo_v4.yaml \n", "Weight: \n", "                                                  name          shape\n", "0                                       u_p_input0/w:0       (32, 32)\n", "1                                       u_p_input0/b:0        (32, 1)\n", "2                                       u_p_input1/w:0        (32, 2)\n", "3                                       u_p_input1/b:0         (2, 1)\n", "4                                       u_i_input0/w:0       (32, 32)\n", "5                                       u_i_input0/b:0        (32, 1)\n", "6                                       u_i_input1/w:0        (32, 2)\n", "7                                       u_i_input1/b:0         (2, 1)\n", "8                                 context_jrc_loss/w:0        (32, 1)\n", "9                                 context_jrc_loss/b:0         (1, 1)\n", "10                                    LayerNorm/beta:0     (11057, 1)\n", "11                                   LayerNorm/gamma:0     (11057, 1)\n", "12                             generalize_layer/beta:0       (240, 1)\n", "13                            generalize_layer/gamma:0       (240, 1)\n", "14                                    akg_layer/beta:0       (160, 1)\n", "15                                   akg_layer/gamma:0       (160, 1)\n", "16                                      new_fea/beta:0       (272, 1)\n", "17                                     new_fea/gamma:0       (272, 1)\n", "18                            search_feat_layer/beta:0       (128, 1)\n", "19                           search_feat_layer/gamma:0       (128, 1)\n", "20                             goods_feat_layer/beta:0       (288, 1)\n", "21                            goods_feat_layer/gamma:0       (288, 1)\n", "22                          item_add_feas_layer/beta:0       (320, 1)\n", "23                         item_add_feas_layer/gamma:0       (320, 1)\n", "24                              shark_emb_layer/beta:0       (128, 1)\n", "25                             shark_emb_layer/gamma:0       (128, 1)\n", "26                      llm_keywords_feas_layer/beta:0       (128, 1)\n", "27                     llm_keywords_feas_layer/gamma:0       (128, 1)\n", "28                           cid_add_feas_layer/beta:0       (320, 1)\n", "29                          cid_add_feas_layer/gamma:0       (320, 1)\n", "30                            add_new_fea_layer/beta:0       (280, 1)\n", "31                           add_new_fea_layer/gamma:0       (280, 1)\n", "32                            add_cpg_fea_layer/beta:0       (208, 1)\n", "33                           add_cpg_fea_layer/gamma:0       (208, 1)\n", "34                            add_adx_fea_layer/beta:0       (128, 1)\n", "35                           add_adx_fea_layer/gamma:0       (128, 1)\n", "36                      pinnerformer_user_layer/beta:0        (64, 1)\n", "37                     pinnerformer_user_layer/gamma:0        (64, 1)\n", "38                      pinnerformer_item_layer/beta:0        (64, 1)\n", "39                     pinnerformer_item_layer/gamma:0        (64, 1)\n", "40                            add_rta_fea_layer/beta:0       (176, 1)\n", "41                           add_rta_fea_layer/gamma:0       (176, 1)\n", "42                               reco_dnn_input/beta:0      (4888, 1)\n", "43                              reco_dnn_input/gamma:0      (4888, 1)\n", "44                              utype_fea_layer/beta:0        (16, 1)\n", "45                             utype_fea_layer/gamma:0        (16, 1)\n", "46                             cot_sparse_layer/beta:0       (336, 1)\n", "47                            cot_sparse_layer/gamma:0       (336, 1)\n", "48                              cot_dense_layer/beta:0       (384, 1)\n", "49                             cot_dense_layer/gamma:0       (384, 1)\n", "50                                 entity_layer/beta:0       (128, 1)\n", "51                                entity_layer/gamma:0       (128, 1)\n", "52                              is_ai_tag_layer/beta:0        (16, 1)\n", "53                             is_ai_tag_layer/gamma:0        (16, 1)\n", "54                                    sft_layer/beta:0       (512, 1)\n", "55                                   sft_layer/gamma:0       (512, 1)\n", "56                               reco_sft_layer/beta:0       (512, 1)\n", "57                              reco_sft_layer/gamma:0       (512, 1)\n", "58                             multimodal_layer/beta:0       (128, 1)\n", "59                            multimodal_layer/gamma:0       (128, 1)\n", "60                                 coupon_layer/beta:0        (80, 1)\n", "61                                coupon_layer/gamma:0        (80, 1)\n", "62                           user_ecom_rq_layer/beta:0       (288, 1)\n", "63                          user_ecom_rq_layer/gamma:0       (288, 1)\n", "64                          match_0_dense_layer/beta:0         (5, 1)\n", "65                         match_0_dense_layer/gamma:0         (5, 1)\n", "66                          match_1_dense_layer/beta:0         (5, 1)\n", "67                         match_1_dense_layer/gamma:0         (5, 1)\n", "68                          match_2_dense_layer/beta:0         (5, 1)\n", "69                         match_2_dense_layer/gamma:0         (5, 1)\n", "70                          match_3_dense_layer/beta:0         (5, 1)\n", "71                         match_3_dense_layer/gamma:0         (5, 1)\n", "72                          match_4_dense_layer/beta:0         (5, 1)\n", "73                         match_4_dense_layer/gamma:0         (5, 1)\n", "74                        ecom_multimodal_layer/beta:0       (128, 1)\n", "75                       ecom_multimodal_layer/gamma:0       (128, 1)\n", "76                             ec_detaile_layer/beta:0        (80, 1)\n", "77                            ec_detaile_layer/gamma:0        (80, 1)\n", "78                           commentstats_layer/beta:0         (5, 1)\n", "79                          commentstats_layer/gamma:0         (5, 1)\n", "80                                          dpo/beta:0        (32, 1)\n", "81                                         dpo/gamma:0        (32, 1)\n", "82                                    dense_dpo/beta:0       (128, 1)\n", "83                                   dense_dpo/gamma:0       (128, 1)\n", "84               good_click_cate2cate_dnn_layer/beta:0      (3000, 1)\n", "85              good_click_cate2cate_dnn_layer/gamma:0      (3000, 1)\n", "86                     good_show_user_embedding/beta:0      (4800, 1)\n", "87                    good_show_user_embedding/gamma:0      (4800, 1)\n", "88                                     eshop_ad/beta:0       (128, 1)\n", "89                                    eshop_ad/gamma:0       (128, 1)\n", "90                              ue_score_sparse/beta:0       (304, 1)\n", "91                             ue_score_sparse/gamma:0       (304, 1)\n", "92                      ExtractUserDenseGrpoFea/beta:0      (1024, 1)\n", "93                     ExtractUserDenseGrpoFea/gamma:0      (1024, 1)\n", "94                                  grpo_sparse/beta:0        (64, 1)\n", "95                                 grpo_sparse/gamma:0        (64, 1)\n", "96                         ue_rank_index_sparse/beta:0       (288, 1)\n", "97                        ue_rank_index_sparse/gamma:0       (288, 1)\n", "98                inner_order_rank_index_sparse/beta:0        (16, 1)\n", "99               inner_order_rank_index_sparse/gamma:0        (16, 1)\n", "100                 user_videoclip_emb_ln_layer/beta:0       (384, 1)\n", "101                user_videoclip_emb_ln_layer/gamma:0       (384, 1)\n", "102             ConcatKeywordVideoClip_ln_layer/beta:0        (64, 1)\n", "103            ConcatKeywordVideoClip_ln_layer/gamma:0        (64, 1)\n", "104               video_clip_attentionq_trans_matrix:0       (64, 64)\n", "105               video_clip_attentionk_trans_matrix:0       (64, 64)\n", "106               video_clip_attentionv_trans_matrix:0       (64, 64)\n", "107                 video_atten_output_ln_layer/beta:0        (64, 1)\n", "108                video_atten_output_ln_layer/gamma:0        (64, 1)\n", "109                      pinnerformer_moe_gate_net/w:0        (64, 2)\n", "110                      pinnerformer_moe_gate_net/b:0         (2, 1)\n", "111                     pinnerformer_moe_0_expert0/w:0      (64, 128)\n", "112                     pinnerformer_moe_0_expert0/b:0       (128, 1)\n", "113                     pinnerformer_moe_0_expert1/w:0      (128, 32)\n", "114                     pinnerformer_moe_0_expert1/b:0        (32, 1)\n", "115                     pinnerformer_moe_1_expert0/w:0      (64, 128)\n", "116                     pinnerformer_moe_1_expert0/b:0       (128, 1)\n", "117                     pinnerformer_moe_1_expert1/w:0      (128, 32)\n", "118                     pinnerformer_moe_1_expert1/b:0        (32, 1)\n", "119                     videoclip_emb_moe_gate_net/w:0        (64, 2)\n", "120                     videoclip_emb_moe_gate_net/b:0         (2, 1)\n", "121                    videoclip_emb_moe_0_expert0/w:0      (64, 128)\n", "122                    videoclip_emb_moe_0_expert0/b:0       (128, 1)\n", "123                    videoclip_emb_moe_0_expert1/w:0      (128, 32)\n", "124                    videoclip_emb_moe_0_expert1/b:0        (32, 1)\n", "125                    videoclip_emb_moe_1_expert0/w:0      (64, 128)\n", "126                    videoclip_emb_moe_1_expert0/b:0       (128, 1)\n", "127                    videoclip_emb_moe_1_expert1/w:0      (128, 32)\n", "128                    videoclip_emb_moe_1_expert1/b:0        (32, 1)\n", "129                         cot_dense_moe_gate_net/w:0       (384, 2)\n", "130                         cot_dense_moe_gate_net/b:0         (2, 1)\n", "131                        cot_dense_moe_0_expert0/w:0     (384, 128)\n", "132                        cot_dense_moe_0_expert0/b:0       (128, 1)\n", "133                        cot_dense_moe_0_expert1/w:0      (128, 32)\n", "134                        cot_dense_moe_0_expert1/b:0        (32, 1)\n", "135                        cot_dense_moe_1_expert0/w:0     (384, 128)\n", "136                        cot_dense_moe_1_expert0/b:0       (128, 1)\n", "137                        cot_dense_moe_1_expert1/w:0      (128, 32)\n", "138                        cot_dense_moe_1_expert1/b:0        (32, 1)\n", "139                         sft_dense_moe_gate_net/w:0       (512, 2)\n", "140                         sft_dense_moe_gate_net/b:0         (2, 1)\n", "141                        sft_dense_moe_0_expert0/w:0     (512, 128)\n", "142                        sft_dense_moe_0_expert0/b:0       (128, 1)\n", "143                        sft_dense_moe_0_expert1/w:0      (128, 32)\n", "144                        sft_dense_moe_0_expert1/b:0        (32, 1)\n", "145                        sft_dense_moe_1_expert0/w:0     (512, 128)\n", "146                        sft_dense_moe_1_expert0/b:0       (128, 1)\n", "147                        sft_dense_moe_1_expert1/w:0      (128, 32)\n", "148                        sft_dense_moe_1_expert1/b:0        (32, 1)\n", "149                            pinnerformer_input0/w:0     (128, 512)\n", "150                            pinnerformer_input0/b:0       (512, 1)\n", "151                            pinnerformer_input1/w:0     (512, 128)\n", "152                            pinnerformer_input1/b:0       (128, 1)\n", "153                            pinnerformer_input2/w:0       (128, 2)\n", "154                            pinnerformer_input2/b:0         (2, 1)\n", "155                               llm_other_input0/w:0    (2005, 512)\n", "156                               llm_other_input0/b:0       (512, 1)\n", "157                               llm_other_input1/w:0     (512, 128)\n", "158                               llm_other_input1/b:0       (128, 1)\n", "159                               llm_other_input2/w:0       (128, 2)\n", "160                               llm_other_input2/b:0         (2, 1)\n", "161                            llm_keywords_input0/w:0      (128, 64)\n", "162                            llm_keywords_input0/b:0        (64, 1)\n", "163                            llm_keywords_input1/w:0        (64, 2)\n", "164                            llm_keywords_input1/b:0         (2, 1)\n", "165                           shark_refine_layer_0/w:0     (128, 512)\n", "166                           shark_refine_layer_0/b:0       (512, 1)\n", "167              shark_refine_layer_0/LayerNorm/beta:0       (512, 1)\n", "168             shark_refine_layer_0/LayerNorm/gamma:0       (512, 1)\n", "169                           shark_refine_layer_1/w:0     (512, 256)\n", "170                           shark_refine_layer_1/b:0       (256, 1)\n", "171                             shark_head_layer_0/w:0     (256, 128)\n", "172                             shark_head_layer_0/b:0       (128, 1)\n", "173                shark_head_layer_0/LayerNorm/beta:0       (128, 1)\n", "174               shark_head_layer_0/LayerNorm/gamma:0       (128, 1)\n", "175                             shark_head_layer_1/w:0       (128, 2)\n", "176                             shark_head_layer_1/b:0         (2, 1)\n", "177                           long_term_pids_fake0/w:0      (800, 64)\n", "178                           long_term_pids_fake0/b:0        (64, 1)\n", "179                           long_term_pids_fake1/w:0       (64, 32)\n", "180                           long_term_pids_fake1/b:0        (32, 1)\n", "181                                 LayerNorm_1/beta:0        (32, 1)\n", "182                                LayerNorm_1/gamma:0        (32, 1)\n", "183                                 LayerNorm_2/beta:0        (32, 1)\n", "184                                LayerNorm_2/gamma:0        (32, 1)\n", "185                           long_term_aids_fake0/w:0      (800, 64)\n", "186                           long_term_aids_fake0/b:0        (64, 1)\n", "187                           long_term_aids_fake1/w:0       (64, 32)\n", "188                           long_term_aids_fake1/b:0        (32, 1)\n", "189                                 LayerNorm_3/beta:0        (32, 1)\n", "190                                LayerNorm_3/gamma:0        (32, 1)\n", "191                                 LayerNorm_4/beta:0        (32, 1)\n", "192                                LayerNorm_4/gamma:0        (32, 1)\n", "193                           long_term_play_fake0/w:0      (400, 64)\n", "194                           long_term_play_fake0/b:0        (64, 1)\n", "195                           long_term_play_fake1/w:0       (64, 32)\n", "196                           long_term_play_fake1/b:0        (32, 1)\n", "197                                 LayerNorm_5/beta:0        (32, 1)\n", "198                                LayerNorm_5/gamma:0        (32, 1)\n", "199                                 LayerNorm_6/beta:0        (32, 1)\n", "200                                LayerNorm_6/gamma:0        (32, 1)\n", "201                           long_term_tags_fake0/w:0      (400, 64)\n", "202                           long_term_tags_fake0/b:0        (64, 1)\n", "203                           long_term_tags_fake1/w:0       (64, 32)\n", "204                           long_term_tags_fake1/b:0        (32, 1)\n", "205                                 LayerNorm_7/beta:0        (32, 1)\n", "206                                LayerNorm_7/gamma:0        (32, 1)\n", "207                                 LayerNorm_8/beta:0        (32, 1)\n", "208                                LayerNorm_8/gamma:0        (32, 1)\n", "209                          long_term_times_fake0/w:0      (400, 64)\n", "210                          long_term_times_fake0/b:0        (64, 1)\n", "211                          long_term_times_fake1/w:0       (64, 32)\n", "212                          long_term_times_fake1/b:0        (32, 1)\n", "213                                 LayerNorm_9/beta:0        (32, 1)\n", "214                                LayerNorm_9/gamma:0        (32, 1)\n", "215                                LayerNorm_10/beta:0        (32, 1)\n", "216                               LayerNorm_10/gamma:0        (32, 1)\n", "217               colossus_rs_count_index_list_cl0/w:0      (200, 64)\n", "218               colossus_rs_count_index_list_cl0/b:0        (64, 1)\n", "219               colossus_rs_count_index_list_cl1/w:0       (64, 32)\n", "220               colossus_rs_count_index_list_cl1/b:0        (32, 1)\n", "221                                LayerNorm_11/beta:0        (32, 1)\n", "222                               LayerNorm_11/gamma:0        (32, 1)\n", "223                                LayerNorm_12/beta:0        (32, 1)\n", "224                               LayerNorm_12/gamma:0        (32, 1)\n", "225                   colossus_rs_item_id_list_cl0/w:0      (800, 64)\n", "226                   colossus_rs_item_id_list_cl0/b:0        (64, 1)\n", "227                   colossus_rs_item_id_list_cl1/w:0       (64, 32)\n", "228                   colossus_rs_item_id_list_cl1/b:0        (32, 1)\n", "229                                LayerNorm_13/beta:0        (32, 1)\n", "230                               LayerNorm_13/gamma:0        (32, 1)\n", "231                                LayerNorm_14/beta:0        (32, 1)\n", "232                               LayerNorm_14/gamma:0        (32, 1)\n", "233                     colossus_rs_lagV1_list_cl0/w:0      (200, 64)\n", "234                     colossus_rs_lagV1_list_cl0/b:0        (64, 1)\n", "235                     colossus_rs_lagV1_list_cl1/w:0       (64, 32)\n", "236                     colossus_rs_lagV1_list_cl1/b:0        (32, 1)\n", "237                                LayerNorm_15/beta:0        (32, 1)\n", "238                               LayerNorm_15/gamma:0        (32, 1)\n", "239                                LayerNorm_16/beta:0        (32, 1)\n", "240                               LayerNorm_16/gamma:0        (32, 1)\n", "241                        colossus_rs_lagV2_list0/w:0      (200, 64)\n", "242                        colossus_rs_lagV2_list0/b:0        (64, 1)\n", "243                        colossus_rs_lagV2_list1/w:0       (64, 32)\n", "244                        colossus_rs_lagV2_list1/b:0        (32, 1)\n", "245                                LayerNorm_17/beta:0        (32, 1)\n", "246                               LayerNorm_17/gamma:0        (32, 1)\n", "247                                LayerNorm_18/beta:0        (32, 1)\n", "248                               LayerNorm_18/gamma:0        (32, 1)\n", "249               colossus_rs_pagecode_id_list_cl0/w:0      (200, 64)\n", "250               colossus_rs_pagecode_id_list_cl0/b:0        (64, 1)\n", "251               colossus_rs_pagecode_id_list_cl1/w:0       (64, 32)\n", "252               colossus_rs_pagecode_id_list_cl1/b:0        (32, 1)\n", "253                                LayerNorm_19/beta:0        (32, 1)\n", "254                               LayerNorm_19/gamma:0        (32, 1)\n", "255                                LayerNorm_20/beta:0        (32, 1)\n", "256                               LayerNorm_20/gamma:0        (32, 1)\n", "257            colossus_rs_uniform_spu_id_list_cl0/w:0      (800, 64)\n", "258            colossus_rs_uniform_spu_id_list_cl0/b:0        (64, 1)\n", "259            colossus_rs_uniform_spu_id_list_cl1/w:0       (64, 32)\n", "260            colossus_rs_uniform_spu_id_list_cl1/b:0        (32, 1)\n", "261                                LayerNorm_21/beta:0        (32, 1)\n", "262                               LayerNorm_21/gamma:0        (32, 1)\n", "263                                LayerNorm_22/beta:0        (32, 1)\n", "264                               LayerNorm_22/gamma:0        (32, 1)\n", "265                 SENET_layer/excitation_layer_1/w:0      (255, 45)\n", "266                 SENET_layer/excitation_layer_1/b:0        (45, 1)\n", "267    SENET_layer/excitation_layer_1/LayerNorm/beta:0        (45, 1)\n", "268   SENET_layer/excitation_layer_1/LayerNorm/gamma:0        (45, 1)\n", "269                 SENET_layer/excitation_layer_2/w:0      (45, 255)\n", "270                 SENET_layer/excitation_layer_2/b:0       (255, 1)\n", "271                                candidate_layer/w:0    (4080, 256)\n", "272                                candidate_layer/b:0       (256, 1)\n", "273            candidate_layer/rc_seq_q_trans_matrix:0      (256, 32)\n", "274            candidate_layer/rc_seq_k_trans_matrix:0       (96, 32)\n", "275            candidate_layer/rc_seq_v_trans_matrix:0       (96, 32)\n", "276                                attention_layer/w:0   (4080, 1024)\n", "277                                attention_layer/b:0      (1024, 1)\n", "278         attention_layer/long_term_q_trans_matrix:0     (1024, 32)\n", "279         attention_layer/long_term_k_trans_matrix:0       (56, 32)\n", "280         attention_layer/long_term_v_trans_matrix:0       (56, 32)\n", "281      attention_layer/long_seq_suffix_pooling_0/w:0    (1080, 256)\n", "282      attention_layer/long_seq_suffix_pooling_0/b:0       (256, 1)\n", "283  attention_layer/long_seq_suffix_pooling_0/Laye...       (256, 1)\n", "284  attention_layer/long_seq_suffix_pooling_0/Laye...       (256, 1)\n", "285      attention_layer/long_seq_suffix_pooling_1/w:0      (256, 32)\n", "286      attention_layer/long_seq_suffix_pooling_1/b:0        (32, 1)\n", "287  attention_layer/good_show_user_embedding_pooli...     (336, 256)\n", "288  attention_layer/good_show_user_embedding_pooli...       (256, 1)\n", "289  attention_layer/good_show_user_embedding_pooli...       (256, 1)\n", "290  attention_layer/good_show_user_embedding_pooli...       (256, 1)\n", "291  attention_layer/good_show_user_embedding_pooli...      (256, 32)\n", "292  attention_layer/good_show_user_embedding_pooli...        (32, 1)\n", "293  attention_layer/good_show_user_embedding_atten...      (288, 32)\n", "294  attention_layer/good_show_user_embedding_atten...       (48, 32)\n", "295  attention_layer/good_show_user_embedding_atten...       (48, 32)\n", "296          good_click_cate2cate_real_price_fake0/w:0      (400, 64)\n", "297          good_click_cate2cate_real_price_fake0/b:0        (64, 1)\n", "298          good_click_cate2cate_real_price_fake1/w:0       (64, 32)\n", "299          good_click_cate2cate_real_price_fake1/b:0        (32, 1)\n", "300                                LayerNorm_23/beta:0        (32, 1)\n", "301                               LayerNorm_23/gamma:0        (32, 1)\n", "302                                LayerNorm_24/beta:0        (32, 1)\n", "303                               LayerNorm_24/gamma:0        (32, 1)\n", "304            good_click_cate2cate_category_fake0/w:0      (400, 64)\n", "305            good_click_cate2cate_category_fake0/b:0        (64, 1)\n", "306            good_click_cate2cate_category_fake1/w:0       (64, 32)\n", "307            good_click_cate2cate_category_fake1/b:0        (32, 1)\n", "308                                LayerNorm_25/beta:0        (32, 1)\n", "309                               LayerNorm_25/gamma:0        (32, 1)\n", "310                                LayerNorm_26/beta:0        (32, 1)\n", "311                               LayerNorm_26/gamma:0        (32, 1)\n", "312          good_click_cate2cate_carry_type_fake0/w:0      (200, 64)\n", "313          good_click_cate2cate_carry_type_fake0/b:0        (64, 1)\n", "314          good_click_cate2cate_carry_type_fake1/w:0       (64, 32)\n", "315          good_click_cate2cate_carry_type_fake1/b:0        (32, 1)\n", "316                                LayerNorm_27/beta:0        (32, 1)\n", "317                               LayerNorm_27/gamma:0        (32, 1)\n", "318                                LayerNorm_28/beta:0        (32, 1)\n", "319                               LayerNorm_28/gamma:0        (32, 1)\n", "320                 good_click_cate2cate_lag_fake0/w:0      (400, 64)\n", "321                 good_click_cate2cate_lag_fake0/b:0        (64, 1)\n", "322                 good_click_cate2cate_lag_fake1/w:0       (64, 32)\n", "323                 good_click_cate2cate_lag_fake1/b:0        (32, 1)\n", "324                                LayerNorm_29/beta:0        (32, 1)\n", "325                               LayerNorm_29/gamma:0        (32, 1)\n", "326                                LayerNorm_30/beta:0        (32, 1)\n", "327                               LayerNorm_30/gamma:0        (32, 1)\n", "328             good_click_cate2cate_item_id_fake0/w:0      (800, 64)\n", "329             good_click_cate2cate_item_id_fake0/b:0        (64, 1)\n", "330             good_click_cate2cate_item_id_fake1/w:0       (64, 32)\n", "331             good_click_cate2cate_item_id_fake1/b:0        (32, 1)\n", "332                                LayerNorm_31/beta:0        (32, 1)\n", "333                               LayerNorm_31/gamma:0        (32, 1)\n", "334                                LayerNorm_32/beta:0        (32, 1)\n", "335                               LayerNorm_32/gamma:0        (32, 1)\n", "336           good_click_cate2cate_seller_id_fake0/w:0      (800, 64)\n", "337           good_click_cate2cate_seller_id_fake0/b:0        (64, 1)\n", "338           good_click_cate2cate_seller_id_fake1/w:0       (64, 32)\n", "339           good_click_cate2cate_seller_id_fake1/b:0        (32, 1)\n", "340                                LayerNorm_33/beta:0        (32, 1)\n", "341                               LayerNorm_33/gamma:0        (32, 1)\n", "342                                LayerNorm_34/beta:0        (32, 1)\n", "343                               LayerNorm_34/gamma:0        (32, 1)\n", "344               good_click_cate2cate_layer_new_0/w:0      (60, 128)\n", "345               good_click_cate2cate_layer_new_0/b:0       (128, 1)\n", "346  good_click_cate2cate_layer_new_0/LayerNorm/beta:0       (128, 1)\n", "347  good_click_cate2cate_layer_new_0/LayerNorm/gam...       (128, 1)\n", "348               good_click_cate2cate_layer_new_1/w:0      (128, 64)\n", "349               good_click_cate2cate_layer_new_1/b:0        (64, 1)\n", "350  good_click_cate2cate_layer_new_1/LayerNorm/beta:0        (64, 1)\n", "351  good_click_cate2cate_layer_new_1/LayerNorm/gam...        (64, 1)\n", "352               good_click_cate2cate_layer_new_2/w:0       (64, 32)\n", "353               good_click_cate2cate_layer_new_2/b:0        (32, 1)\n", "354                            Cross_layer_sub/w_sub:0      (4, 5820)\n", "355                                    dcn_layer_0/u:0    (14923, 64)\n", "356                                    dcn_layer_0/v:0    (64, 14923)\n", "357                                    dcn_layer_0/b:0     (14923, 1)\n", "358                       dcn_layer_0/LayerNorm/beta:0     (14923, 1)\n", "359                      dcn_layer_0/LayerNorm/gamma:0     (14923, 1)\n", "360                                    dcn_layer_1/u:0    (14923, 64)\n", "361                                    dcn_layer_1/v:0    (64, 14923)\n", "362                                    dcn_layer_1/b:0     (14923, 1)\n", "363                QUEUE_SOFT/share_bottom_layer_0/w:0   (8909, 1024)\n", "364                QUEUE_SOFT/share_bottom_layer_0/b:0      (1024, 1)\n", "365   QUEUE_SOFT/share_bottom_layer_0/LayerNorm/beta:0      (1024, 1)\n", "366  QUEUE_SOFT/share_bottom_layer_0/LayerNorm/gamma:0      (1024, 1)\n", "367                         QUEUE_SOFT/pxr_layer_1/w:0    (1024, 256)\n", "368                         QUEUE_SOFT/pxr_layer_1/b:0       (256, 1)\n", "369            QUEUE_SOFT/pxr_layer_1/LayerNorm/beta:0       (256, 1)\n", "370           QUEUE_SOFT/pxr_layer_1/LayerNorm/gamma:0       (256, 1)\n", "371                         QUEUE_SOFT/pxr_layer_2/w:0     (256, 128)\n", "372                         QUEUE_SOFT/pxr_layer_2/b:0       (128, 1)\n", "373            QUEUE_SOFT/pxr_layer_2/LayerNorm/beta:0       (128, 1)\n", "374           QUEUE_SOFT/pxr_layer_2/LayerNorm/gamma:0       (128, 1)\n", "375                         QUEUE_SOFT/pxr_layer_3/w:0       (128, 2)\n", "376                         QUEUE_SOFT/pxr_layer_3/b:0         (2, 1)\n", "377                         QUEUE_SOFT/ped_layer_1/w:0    (1024, 256)\n", "378                         QUEUE_SOFT/ped_layer_1/b:0       (256, 1)\n", "379            QUEUE_SOFT/ped_layer_1/LayerNorm/beta:0       (256, 1)\n", "380           QUEUE_SOFT/ped_layer_1/LayerNorm/gamma:0       (256, 1)\n", "381                         QUEUE_SOFT/ped_layer_2/w:0     (256, 128)\n", "382                         QUEUE_SOFT/ped_layer_2/b:0       (128, 1)\n", "383            QUEUE_SOFT/ped_layer_2/LayerNorm/beta:0       (128, 1)\n", "384           QUEUE_SOFT/ped_layer_2/LayerNorm/gamma:0       (128, 1)\n", "385                         QUEUE_SOFT/ped_layer_3/w:0       (128, 2)\n", "386                         QUEUE_SOFT/ped_layer_3/b:0         (2, 1)\n", "387                   QUEUE_SOFT/ctr_upper_layer_1/w:0    (1024, 256)\n", "388                   QUEUE_SOFT/ctr_upper_layer_1/b:0       (256, 1)\n", "389      QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "390     QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "391                   QUEUE_SOFT/ctr_upper_layer_2/w:0     (256, 128)\n", "392                   QUEUE_SOFT/ctr_upper_layer_2/b:0       (128, 1)\n", "393      QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "394     QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "395                   QUEUE_SOFT/ctr_upper_layer_3/w:0       (128, 2)\n", "396                   QUEUE_SOFT/ctr_upper_layer_3/b:0         (2, 1)\n", "397                       QUEUE_SOFT/upper_layer_1/w:0    (1024, 256)\n", "398                       QUEUE_SOFT/upper_layer_1/b:0       (256, 1)\n", "399          QUEUE_SOFT/upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "400         QUEUE_SOFT/upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "401                       QUEUE_SOFT/upper_layer_2/w:0     (256, 128)\n", "402                       QUEUE_SOFT/upper_layer_2/b:0       (128, 1)\n", "403          QUEUE_SOFT/upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "404         QUEUE_SOFT/upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "405                       QUEUE_SOFT/upper_layer_3/w:0       (128, 2)\n", "406                       QUEUE_SOFT/upper_layer_3/b:0         (2, 1)\n", "407                               QUEUE_SOFT/w_ctcvr:0         (1, 1)\n", "408                       share_bottom_layer_new_0/w:0  (14923, 1024)\n", "409                       share_bottom_layer_new_0/b:0      (1024, 1)\n", "410          share_bottom_layer_new_0/LayerNorm/beta:0      (1024, 1)\n", "411         share_bottom_layer_new_0/LayerNorm/gamma:0      (1024, 1)\n", "412                             generalize_layer_0/w:0     (240, 128)\n", "413                             generalize_layer_0/b:0       (128, 1)\n", "414                generalize_layer_0/LayerNorm/beta:0       (128, 1)\n", "415               generalize_layer_0/LayerNorm/gamma:0       (128, 1)\n", "416                  reco_share_bottom_layer_new_0/w:0   (5400, 1024)\n", "417                  reco_share_bottom_layer_new_0/b:0      (1024, 1)\n", "418     reco_share_bottom_layer_new_0/LayerNorm/beta:0      (1024, 1)\n", "419    reco_share_bottom_layer_new_0/LayerNorm/gamma:0      (1024, 1)\n", "420                             generalize_layer_1/w:0      (128, 64)\n", "421                             generalize_layer_1/b:0        (64, 1)\n", "422                generalize_layer_1/LayerNorm/beta:0        (64, 1)\n", "423               generalize_layer_1/LayerNorm/gamma:0        (64, 1)\n", "424                             generalize_layer_2/w:0       (64, 32)\n", "425                             generalize_layer_2/b:0        (32, 1)\n", "426                generalize_layer_2/LayerNorm/beta:0        (32, 1)\n", "427               generalize_layer_2/LayerNorm/gamma:0        (32, 1)\n", "428                             generalize_layer_3/w:0        (32, 2)\n", "429                             generalize_layer_3/b:0         (2, 1)\n", "430                                    pxr_layer_1/w:0    (1024, 256)\n", "431                                    pxr_layer_1/b:0       (256, 1)\n", "432                       pxr_layer_1/LayerNorm/beta:0       (256, 1)\n", "433                      pxr_layer_1/LayerNorm/gamma:0       (256, 1)\n", "434                                    pxr_layer_2/w:0     (256, 128)\n", "435                                    pxr_layer_2/b:0       (128, 1)\n", "436                       pxr_layer_2/LayerNorm/beta:0       (128, 1)\n", "437                      pxr_layer_2/LayerNorm/gamma:0       (128, 1)\n", "438                                    pxr_layer_3/w:0       (128, 2)\n", "439                                    pxr_layer_3/b:0         (2, 1)\n", "440                                    ped_layer_1/w:0    (1024, 256)\n", "441                                    ped_layer_1/b:0       (256, 1)\n", "442                       ped_layer_1/LayerNorm/beta:0       (256, 1)\n", "443                      ped_layer_1/LayerNorm/gamma:0       (256, 1)\n", "444                                    ped_layer_2/w:0     (256, 128)\n", "445                                    ped_layer_2/b:0       (128, 1)\n", "446                       ped_layer_2/LayerNorm/beta:0       (128, 1)\n", "447                      ped_layer_2/LayerNorm/gamma:0       (128, 1)\n", "448                                    ped_layer_3/w:0       (128, 2)\n", "449                                    ped_layer_3/b:0         (2, 1)\n", "450                              ctr_upper_layer_1/w:0    (1024, 256)\n", "451                              ctr_upper_layer_1/b:0       (256, 1)\n", "452                 ctr_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "453                ctr_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "454                              ctr_upper_layer_2/w:0     (256, 128)\n", "455                              ctr_upper_layer_2/b:0       (128, 1)\n", "456                 ctr_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "457                ctr_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "458                              ctr_upper_layer_3/w:0       (128, 2)\n", "459                              ctr_upper_layer_3/b:0         (2, 1)\n", "460                          assist1_upper_layer_1/w:0    (1024, 256)\n", "461                          assist1_upper_layer_1/b:0       (256, 1)\n", "462             assist1_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "463            assist1_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "464                          assist1_upper_layer_2/w:0     (256, 128)\n", "465                          assist1_upper_layer_2/b:0       (128, 1)\n", "466             assist1_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "467            assist1_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "468                          assist1_upper_layer_3/w:0       (128, 2)\n", "469                          assist1_upper_layer_3/b:0         (2, 1)\n", "470                          assist2_upper_layer_1/w:0    (1024, 256)\n", "471                          assist2_upper_layer_1/b:0       (256, 1)\n", "472             assist2_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "473            assist2_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "474                          assist2_upper_layer_2/w:0     (256, 128)\n", "475                          assist2_upper_layer_2/b:0       (128, 1)\n", "476             assist2_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "477            assist2_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "478                          assist2_upper_layer_3/w:0       (128, 2)\n", "479                          assist2_upper_layer_3/b:0         (2, 1)\n", "480                                    cid_layer_1/w:0    (1024, 256)\n", "481                                    cid_layer_1/b:0       (256, 1)\n", "482                       cid_layer_1/LayerNorm/beta:0       (256, 1)\n", "483                      cid_layer_1/LayerNorm/gamma:0       (256, 1)\n", "484                                    cid_layer_2/w:0     (256, 128)\n", "485                                    cid_layer_2/b:0       (128, 1)\n", "486                       cid_layer_2/LayerNorm/beta:0       (128, 1)\n", "487                      cid_layer_2/LayerNorm/gamma:0       (128, 1)\n", "488                                    cid_layer_3/w:0       (128, 2)\n", "489                                    cid_layer_3/b:0         (2, 1)\n", "490                                  upper_layer_1/w:0    (1153, 256)\n", "491                                  upper_layer_1/b:0       (256, 1)\n", "492                    upper_layer_1/layer_norm/beta:0       (256, 1)\n", "493                   upper_layer_1/layer_norm/gamma:0       (256, 1)\n", "494                                  upper_layer_2/w:0     (256, 128)\n", "495                                  upper_layer_2/b:0       (128, 1)\n", "496                    upper_layer_2/layer_norm/beta:0       (128, 1)\n", "497                   upper_layer_2/layer_norm/gamma:0       (128, 1)\n", "498                                  upper_layer_3/w:0       (128, 2)\n", "499                                  upper_layer_3/b:0         (2, 1)\n", "500                           rocket_upper_layer_2/w:0     (864, 128)\n", "501                           rocket_upper_layer_2/b:0       (128, 1)\n", "502              rocket_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "503             rocket_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "504                           rocket_upper_layer_3/w:0       (128, 2)\n", "505                           rocket_upper_layer_3/b:0         (2, 1)\n", "506                                 global_layer_1/w:0    (1024, 256)\n", "507                                 global_layer_1/b:0       (256, 1)\n", "508                    global_layer_1/LayerNorm/beta:0       (256, 1)\n", "509                   global_layer_1/LayerNorm/gamma:0       (256, 1)\n", "510                                 global_layer_2/w:0     (256, 128)\n", "511                                 global_layer_2/b:0       (128, 1)\n", "512                    global_layer_2/LayerNorm/beta:0       (128, 1)\n", "513                   global_layer_2/LayerNorm/gamma:0       (128, 1)\n", "514                                 global_layer_3/w:0       (128, 2)\n", "515                                 global_layer_3/b:0         (2, 1)\n", "516                                 direct_layer_1/w:0    (1024, 256)\n", "517                                 direct_layer_1/b:0       (256, 1)\n", "518                    direct_layer_1/LayerNorm/beta:0       (256, 1)\n", "519                   direct_layer_1/LayerNorm/gamma:0       (256, 1)\n", "520                                 direct_layer_2/w:0     (256, 128)\n", "521                                 direct_layer_2/b:0       (128, 1)\n", "522                    direct_layer_2/LayerNorm/beta:0       (128, 1)\n", "523                   direct_layer_2/LayerNorm/gamma:0       (128, 1)\n", "524                                 direct_layer_3/w:0       (128, 2)\n", "525                                 direct_layer_3/b:0         (2, 1)\n", "526                      pic_calibration/cali_lambda:0         (1, 1)\n", "527               Pic_calibration/cali_weight_lambda:0         (7, 1)\n", "528                                    gmv_layer_0/w:0    (1024, 256)\n", "529                                    gmv_layer_0/b:0       (256, 1)\n", "530                       gmv_layer_0/LayerNorm/beta:0       (256, 1)\n", "531                      gmv_layer_0/LayerNorm/gamma:0       (256, 1)\n", "532                                    gmv_layer_1/w:0     (256, 128)\n", "533                                    gmv_layer_1/b:0       (128, 1)\n", "534                       gmv_layer_1/LayerNorm/beta:0       (128, 1)\n", "535                      gmv_layer_1/LayerNorm/gamma:0       (128, 1)\n", "536                                    gmv_layer_2/w:0       (128, 2)\n", "537                                    gmv_layer_2/b:0         (2, 1)\n", "538                             reco_upper_layer_1/w:0    (1024, 256)\n", "539                             reco_upper_layer_1/b:0       (256, 1)\n", "540                reco_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "541               reco_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "542                             reco_upper_layer_2/w:0     (256, 128)\n", "543                             reco_upper_layer_2/b:0       (128, 1)\n", "544                reco_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "545               reco_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "546                             reco_upper_layer_3/w:0       (128, 2)\n", "547                             reco_upper_layer_3/b:0         (2, 1)\n", "548                                          w_ctcvr:0         (1, 1)\n", "549                                            w_ctr:0         (1, 1)\n", "550                                            w_p3s:0         (1, 1)\n", "551                                            w_ped:0         (1, 1)\n", "552                                     w_reco_ctcvr:0         (1, 1)\n", "553                                     w_global_pay:0         (1, 1)\n", "554                                     w_direct_pay:0         (1, 1)\n", "555                                           w_ast2:0         (1, 1)\n", "556                                       w_ue_ctcvr:0         (1, 1)\n", "Extra: \n", "                                                  name        shape  opt_type\n", "0                                       u_p_input0/w:0      (1024,)  AdagradW\n", "1                                       u_p_input0/b:0        (32,)  AdagradW\n", "2                                       u_p_input1/w:0        (64,)  AdagradW\n", "3                                       u_p_input1/b:0         (2,)  AdagradW\n", "4                                       u_i_input0/w:0      (1024,)  AdagradW\n", "5                                       u_i_input0/b:0        (32,)  AdagradW\n", "6                                       u_i_input1/w:0        (64,)  AdagradW\n", "7                                       u_i_input1/b:0         (2,)  AdagradW\n", "8                                 context_jrc_loss/w:0        (32,)  AdagradW\n", "9                                 context_jrc_loss/b:0         (1,)  AdagradW\n", "10                                    LayerNorm/beta:0     (11057,)  AdagradW\n", "11                                   LayerNorm/gamma:0     (11057,)  AdagradW\n", "12                             generalize_layer/beta:0       (240,)  AdagradW\n", "13                            generalize_layer/gamma:0       (240,)  AdagradW\n", "14                                    akg_layer/beta:0       (160,)  AdagradW\n", "15                                   akg_layer/gamma:0       (160,)  AdagradW\n", "16                                      new_fea/beta:0       (272,)  AdagradW\n", "17                                     new_fea/gamma:0       (272,)  AdagradW\n", "18                            search_feat_layer/beta:0       (128,)  AdagradW\n", "19                           search_feat_layer/gamma:0       (128,)  AdagradW\n", "20                             goods_feat_layer/beta:0       (288,)  AdagradW\n", "21                            goods_feat_layer/gamma:0       (288,)  AdagradW\n", "22                          item_add_feas_layer/beta:0       (320,)  AdagradW\n", "23                         item_add_feas_layer/gamma:0       (320,)  AdagradW\n", "24                              shark_emb_layer/beta:0       (128,)  AdagradW\n", "25                             shark_emb_layer/gamma:0       (128,)  AdagradW\n", "26                      llm_keywords_feas_layer/beta:0       (128,)  AdagradW\n", "27                     llm_keywords_feas_layer/gamma:0       (128,)  AdagradW\n", "28                           cid_add_feas_layer/beta:0       (320,)  AdagradW\n", "29                          cid_add_feas_layer/gamma:0       (320,)  AdagradW\n", "30                            add_new_fea_layer/beta:0       (280,)  AdagradW\n", "31                           add_new_fea_layer/gamma:0       (280,)  AdagradW\n", "32                            add_cpg_fea_layer/beta:0       (208,)  AdagradW\n", "33                           add_cpg_fea_layer/gamma:0       (208,)  AdagradW\n", "34                            add_adx_fea_layer/beta:0       (128,)  AdagradW\n", "35                           add_adx_fea_layer/gamma:0       (128,)  AdagradW\n", "36                      pinnerformer_user_layer/beta:0        (64,)  AdagradW\n", "37                     pinnerformer_user_layer/gamma:0        (64,)  AdagradW\n", "38                      pinnerformer_item_layer/beta:0        (64,)  AdagradW\n", "39                     pinnerformer_item_layer/gamma:0        (64,)  AdagradW\n", "40                            add_rta_fea_layer/beta:0       (176,)  AdagradW\n", "41                           add_rta_fea_layer/gamma:0       (176,)  AdagradW\n", "42                               reco_dnn_input/beta:0      (4888,)  AdagradW\n", "43                              reco_dnn_input/gamma:0      (4888,)  AdagradW\n", "44                              utype_fea_layer/beta:0        (16,)  AdagradW\n", "45                             utype_fea_layer/gamma:0        (16,)  AdagradW\n", "46                             cot_sparse_layer/beta:0       (336,)  AdagradW\n", "47                            cot_sparse_layer/gamma:0       (336,)  AdagradW\n", "48                              cot_dense_layer/beta:0       (384,)  AdagradW\n", "49                             cot_dense_layer/gamma:0       (384,)  AdagradW\n", "50                                 entity_layer/beta:0       (128,)  AdagradW\n", "51                                entity_layer/gamma:0       (128,)  AdagradW\n", "52                              is_ai_tag_layer/beta:0        (16,)  AdagradW\n", "53                             is_ai_tag_layer/gamma:0        (16,)  AdagradW\n", "54                                    sft_layer/beta:0       (512,)  AdagradW\n", "55                                   sft_layer/gamma:0       (512,)  AdagradW\n", "56                               reco_sft_layer/beta:0       (512,)  AdagradW\n", "57                              reco_sft_layer/gamma:0       (512,)  AdagradW\n", "58                             multimodal_layer/beta:0       (128,)  AdagradW\n", "59                            multimodal_layer/gamma:0       (128,)  AdagradW\n", "60                                 coupon_layer/beta:0        (80,)  AdagradW\n", "61                                coupon_layer/gamma:0        (80,)  AdagradW\n", "62                           user_ecom_rq_layer/beta:0       (288,)  AdagradW\n", "63                          user_ecom_rq_layer/gamma:0       (288,)  AdagradW\n", "64                          match_0_dense_layer/beta:0         (5,)  AdagradW\n", "65                         match_0_dense_layer/gamma:0         (5,)  AdagradW\n", "66                          match_1_dense_layer/beta:0         (5,)  AdagradW\n", "67                         match_1_dense_layer/gamma:0         (5,)  AdagradW\n", "68                          match_2_dense_layer/beta:0         (5,)  AdagradW\n", "69                         match_2_dense_layer/gamma:0         (5,)  AdagradW\n", "70                          match_3_dense_layer/beta:0         (5,)  AdagradW\n", "71                         match_3_dense_layer/gamma:0         (5,)  AdagradW\n", "72                          match_4_dense_layer/beta:0         (5,)  AdagradW\n", "73                         match_4_dense_layer/gamma:0         (5,)  AdagradW\n", "74                        ecom_multimodal_layer/beta:0       (128,)  AdagradW\n", "75                       ecom_multimodal_layer/gamma:0       (128,)  AdagradW\n", "76                             ec_detaile_layer/beta:0        (80,)  AdagradW\n", "77                            ec_detaile_layer/gamma:0        (80,)  AdagradW\n", "78                           commentstats_layer/beta:0         (5,)  AdagradW\n", "79                          commentstats_layer/gamma:0         (5,)  AdagradW\n", "80                                          dpo/beta:0        (32,)  AdagradW\n", "81                                         dpo/gamma:0        (32,)  AdagradW\n", "82                                    dense_dpo/beta:0       (128,)  AdagradW\n", "83                                   dense_dpo/gamma:0       (128,)  AdagradW\n", "84               good_click_cate2cate_dnn_layer/beta:0      (3000,)  AdagradW\n", "85              good_click_cate2cate_dnn_layer/gamma:0      (3000,)  AdagradW\n", "86                     good_show_user_embedding/beta:0      (4800,)  AdagradW\n", "87                    good_show_user_embedding/gamma:0      (4800,)  AdagradW\n", "88                                     eshop_ad/beta:0       (128,)  AdagradW\n", "89                                    eshop_ad/gamma:0       (128,)  AdagradW\n", "90                              ue_score_sparse/beta:0       (304,)  AdagradW\n", "91                             ue_score_sparse/gamma:0       (304,)  AdagradW\n", "92                      ExtractUserDenseGrpoFea/beta:0      (1024,)  AdagradW\n", "93                     ExtractUserDenseGrpoFea/gamma:0      (1024,)  AdagradW\n", "94                                  grpo_sparse/beta:0        (64,)  AdagradW\n", "95                                 grpo_sparse/gamma:0        (64,)  AdagradW\n", "96                         ue_rank_index_sparse/beta:0       (288,)  AdagradW\n", "97                        ue_rank_index_sparse/gamma:0       (288,)  AdagradW\n", "98                inner_order_rank_index_sparse/beta:0        (16,)  AdagradW\n", "99               inner_order_rank_index_sparse/gamma:0        (16,)  AdagradW\n", "100                 user_videoclip_emb_ln_layer/beta:0       (384,)  AdagradW\n", "101                user_videoclip_emb_ln_layer/gamma:0       (384,)  AdagradW\n", "102             ConcatKeywordVideoClip_ln_layer/beta:0        (64,)  AdagradW\n", "103            ConcatKeywordVideoClip_ln_layer/gamma:0        (64,)  AdagradW\n", "104               video_clip_attentionq_trans_matrix:0      (4096,)  AdagradW\n", "105               video_clip_attentionk_trans_matrix:0      (4096,)  AdagradW\n", "106               video_clip_attentionv_trans_matrix:0      (4096,)  AdagradW\n", "107                 video_atten_output_ln_layer/beta:0        (64,)  AdagradW\n", "108                video_atten_output_ln_layer/gamma:0        (64,)  AdagradW\n", "109                      pinnerformer_moe_gate_net/w:0       (128,)  AdagradW\n", "110                      pinnerformer_moe_gate_net/b:0         (2,)  AdagradW\n", "111                     pinnerformer_moe_0_expert0/w:0      (8192,)  AdagradW\n", "112                     pinnerformer_moe_0_expert0/b:0       (128,)  AdagradW\n", "113                     pinnerformer_moe_0_expert1/w:0      (4096,)  AdagradW\n", "114                     pinnerformer_moe_0_expert1/b:0        (32,)  AdagradW\n", "115                     pinnerformer_moe_1_expert0/w:0      (8192,)  AdagradW\n", "116                     pinnerformer_moe_1_expert0/b:0       (128,)  AdagradW\n", "117                     pinnerformer_moe_1_expert1/w:0      (4096,)  AdagradW\n", "118                     pinnerformer_moe_1_expert1/b:0        (32,)  AdagradW\n", "119                     videoclip_emb_moe_gate_net/w:0       (128,)  AdagradW\n", "120                     videoclip_emb_moe_gate_net/b:0         (2,)  AdagradW\n", "121                    videoclip_emb_moe_0_expert0/w:0      (8192,)  AdagradW\n", "122                    videoclip_emb_moe_0_expert0/b:0       (128,)  AdagradW\n", "123                    videoclip_emb_moe_0_expert1/w:0      (4096,)  AdagradW\n", "124                    videoclip_emb_moe_0_expert1/b:0        (32,)  AdagradW\n", "125                    videoclip_emb_moe_1_expert0/w:0      (8192,)  AdagradW\n", "126                    videoclip_emb_moe_1_expert0/b:0       (128,)  AdagradW\n", "127                    videoclip_emb_moe_1_expert1/w:0      (4096,)  AdagradW\n", "128                    videoclip_emb_moe_1_expert1/b:0        (32,)  AdagradW\n", "129                         cot_dense_moe_gate_net/w:0       (768,)  AdagradW\n", "130                         cot_dense_moe_gate_net/b:0         (2,)  AdagradW\n", "131                        cot_dense_moe_0_expert0/w:0     (49152,)  AdagradW\n", "132                        cot_dense_moe_0_expert0/b:0       (128,)  AdagradW\n", "133                        cot_dense_moe_0_expert1/w:0      (4096,)  AdagradW\n", "134                        cot_dense_moe_0_expert1/b:0        (32,)  AdagradW\n", "135                        cot_dense_moe_1_expert0/w:0     (49152,)  AdagradW\n", "136                        cot_dense_moe_1_expert0/b:0       (128,)  AdagradW\n", "137                        cot_dense_moe_1_expert1/w:0      (4096,)  AdagradW\n", "138                        cot_dense_moe_1_expert1/b:0        (32,)  AdagradW\n", "139                         sft_dense_moe_gate_net/w:0      (1024,)  AdagradW\n", "140                         sft_dense_moe_gate_net/b:0         (2,)  AdagradW\n", "141                        sft_dense_moe_0_expert0/w:0     (65536,)  AdagradW\n", "142                        sft_dense_moe_0_expert0/b:0       (128,)  AdagradW\n", "143                        sft_dense_moe_0_expert1/w:0      (4096,)  AdagradW\n", "144                        sft_dense_moe_0_expert1/b:0        (32,)  AdagradW\n", "145                        sft_dense_moe_1_expert0/w:0     (65536,)  AdagradW\n", "146                        sft_dense_moe_1_expert0/b:0       (128,)  AdagradW\n", "147                        sft_dense_moe_1_expert1/w:0      (4096,)  AdagradW\n", "148                        sft_dense_moe_1_expert1/b:0        (32,)  AdagradW\n", "149                            pinnerformer_input0/w:0     (65536,)  AdagradW\n", "150                            pinnerformer_input0/b:0       (512,)  AdagradW\n", "151                            pinnerformer_input1/w:0     (65536,)  AdagradW\n", "152                            pinnerformer_input1/b:0       (128,)  AdagradW\n", "153                            pinnerformer_input2/w:0       (256,)  AdagradW\n", "154                            pinnerformer_input2/b:0         (2,)  AdagradW\n", "155                               llm_other_input0/w:0   (1026560,)  AdagradW\n", "156                               llm_other_input0/b:0       (512,)  AdagradW\n", "157                               llm_other_input1/w:0     (65536,)  AdagradW\n", "158                               llm_other_input1/b:0       (128,)  AdagradW\n", "159                               llm_other_input2/w:0       (256,)  AdagradW\n", "160                               llm_other_input2/b:0         (2,)  AdagradW\n", "161                            llm_keywords_input0/w:0      (8192,)  AdagradW\n", "162                            llm_keywords_input0/b:0        (64,)  AdagradW\n", "163                            llm_keywords_input1/w:0       (128,)  AdagradW\n", "164                            llm_keywords_input1/b:0         (2,)  AdagradW\n", "165                           shark_refine_layer_0/w:0     (65536,)  AdagradW\n", "166                           shark_refine_layer_0/b:0       (512,)  AdagradW\n", "167              shark_refine_layer_0/LayerNorm/beta:0       (512,)  AdagradW\n", "168             shark_refine_layer_0/LayerNorm/gamma:0       (512,)  AdagradW\n", "169                           shark_refine_layer_1/w:0    (131072,)  AdagradW\n", "170                           shark_refine_layer_1/b:0       (256,)  AdagradW\n", "171                             shark_head_layer_0/w:0     (32768,)  AdagradW\n", "172                             shark_head_layer_0/b:0       (128,)  AdagradW\n", "173                shark_head_layer_0/LayerNorm/beta:0       (128,)  AdagradW\n", "174               shark_head_layer_0/LayerNorm/gamma:0       (128,)  AdagradW\n", "175                             shark_head_layer_1/w:0       (256,)  AdagradW\n", "176                             shark_head_layer_1/b:0         (2,)  AdagradW\n", "177                           long_term_pids_fake0/w:0     (51200,)  AdagradW\n", "178                           long_term_pids_fake0/b:0        (64,)  AdagradW\n", "179                           long_term_pids_fake1/w:0      (2048,)  AdagradW\n", "180                           long_term_pids_fake1/b:0        (32,)  AdagradW\n", "181                                 LayerNorm_1/beta:0        (32,)  AdagradW\n", "182                                LayerNorm_1/gamma:0        (32,)  AdagradW\n", "183                                 LayerNorm_2/beta:0        (32,)  AdagradW\n", "184                                LayerNorm_2/gamma:0        (32,)  AdagradW\n", "185                           long_term_aids_fake0/w:0     (51200,)  AdagradW\n", "186                           long_term_aids_fake0/b:0        (64,)  AdagradW\n", "187                           long_term_aids_fake1/w:0      (2048,)  AdagradW\n", "188                           long_term_aids_fake1/b:0        (32,)  AdagradW\n", "189                                 LayerNorm_3/beta:0        (32,)  AdagradW\n", "190                                LayerNorm_3/gamma:0        (32,)  AdagradW\n", "191                                 LayerNorm_4/beta:0        (32,)  AdagradW\n", "192                                LayerNorm_4/gamma:0        (32,)  AdagradW\n", "193                           long_term_play_fake0/w:0     (25600,)  AdagradW\n", "194                           long_term_play_fake0/b:0        (64,)  AdagradW\n", "195                           long_term_play_fake1/w:0      (2048,)  AdagradW\n", "196                           long_term_play_fake1/b:0        (32,)  AdagradW\n", "197                                 LayerNorm_5/beta:0        (32,)  AdagradW\n", "198                                LayerNorm_5/gamma:0        (32,)  AdagradW\n", "199                                 LayerNorm_6/beta:0        (32,)  AdagradW\n", "200                                LayerNorm_6/gamma:0        (32,)  AdagradW\n", "201                           long_term_tags_fake0/w:0     (25600,)  AdagradW\n", "202                           long_term_tags_fake0/b:0        (64,)  AdagradW\n", "203                           long_term_tags_fake1/w:0      (2048,)  AdagradW\n", "204                           long_term_tags_fake1/b:0        (32,)  AdagradW\n", "205                                 LayerNorm_7/beta:0        (32,)  AdagradW\n", "206                                LayerNorm_7/gamma:0        (32,)  AdagradW\n", "207                                 LayerNorm_8/beta:0        (32,)  AdagradW\n", "208                                LayerNorm_8/gamma:0        (32,)  AdagradW\n", "209                          long_term_times_fake0/w:0     (25600,)  AdagradW\n", "210                          long_term_times_fake0/b:0        (64,)  AdagradW\n", "211                          long_term_times_fake1/w:0      (2048,)  AdagradW\n", "212                          long_term_times_fake1/b:0        (32,)  AdagradW\n", "213                                 LayerNorm_9/beta:0        (32,)  AdagradW\n", "214                                LayerNorm_9/gamma:0        (32,)  AdagradW\n", "215                                LayerNorm_10/beta:0        (32,)  AdagradW\n", "216                               LayerNorm_10/gamma:0        (32,)  AdagradW\n", "217               colossus_rs_count_index_list_cl0/w:0     (12800,)  AdagradW\n", "218               colossus_rs_count_index_list_cl0/b:0        (64,)  AdagradW\n", "219               colossus_rs_count_index_list_cl1/w:0      (2048,)  AdagradW\n", "220               colossus_rs_count_index_list_cl1/b:0        (32,)  AdagradW\n", "221                                LayerNorm_11/beta:0        (32,)  AdagradW\n", "222                               LayerNorm_11/gamma:0        (32,)  AdagradW\n", "223                                LayerNorm_12/beta:0        (32,)  AdagradW\n", "224                               LayerNorm_12/gamma:0        (32,)  AdagradW\n", "225                   colossus_rs_item_id_list_cl0/w:0     (51200,)  AdagradW\n", "226                   colossus_rs_item_id_list_cl0/b:0        (64,)  AdagradW\n", "227                   colossus_rs_item_id_list_cl1/w:0      (2048,)  AdagradW\n", "228                   colossus_rs_item_id_list_cl1/b:0        (32,)  AdagradW\n", "229                                LayerNorm_13/beta:0        (32,)  AdagradW\n", "230                               LayerNorm_13/gamma:0        (32,)  AdagradW\n", "231                                LayerNorm_14/beta:0        (32,)  AdagradW\n", "232                               LayerNorm_14/gamma:0        (32,)  AdagradW\n", "233                     colossus_rs_lagV1_list_cl0/w:0     (12800,)  AdagradW\n", "234                     colossus_rs_lagV1_list_cl0/b:0        (64,)  AdagradW\n", "235                     colossus_rs_lagV1_list_cl1/w:0      (2048,)  AdagradW\n", "236                     colossus_rs_lagV1_list_cl1/b:0        (32,)  AdagradW\n", "237                                LayerNorm_15/beta:0        (32,)  AdagradW\n", "238                               LayerNorm_15/gamma:0        (32,)  AdagradW\n", "239                                LayerNorm_16/beta:0        (32,)  AdagradW\n", "240                               LayerNorm_16/gamma:0        (32,)  AdagradW\n", "241                        colossus_rs_lagV2_list0/w:0     (12800,)  AdagradW\n", "242                        colossus_rs_lagV2_list0/b:0        (64,)  AdagradW\n", "243                        colossus_rs_lagV2_list1/w:0      (2048,)  AdagradW\n", "244                        colossus_rs_lagV2_list1/b:0        (32,)  AdagradW\n", "245                                LayerNorm_17/beta:0        (32,)  AdagradW\n", "246                               LayerNorm_17/gamma:0        (32,)  AdagradW\n", "247                                LayerNorm_18/beta:0        (32,)  AdagradW\n", "248                               LayerNorm_18/gamma:0        (32,)  AdagradW\n", "249               colossus_rs_pagecode_id_list_cl0/w:0     (12800,)  AdagradW\n", "250               colossus_rs_pagecode_id_list_cl0/b:0        (64,)  AdagradW\n", "251               colossus_rs_pagecode_id_list_cl1/w:0      (2048,)  AdagradW\n", "252               colossus_rs_pagecode_id_list_cl1/b:0        (32,)  AdagradW\n", "253                                LayerNorm_19/beta:0        (32,)  AdagradW\n", "254                               LayerNorm_19/gamma:0        (32,)  AdagradW\n", "255                                LayerNorm_20/beta:0        (32,)  AdagradW\n", "256                               LayerNorm_20/gamma:0        (32,)  AdagradW\n", "257            colossus_rs_uniform_spu_id_list_cl0/w:0     (51200,)  AdagradW\n", "258            colossus_rs_uniform_spu_id_list_cl0/b:0        (64,)  AdagradW\n", "259            colossus_rs_uniform_spu_id_list_cl1/w:0      (2048,)  AdagradW\n", "260            colossus_rs_uniform_spu_id_list_cl1/b:0        (32,)  AdagradW\n", "261                                LayerNorm_21/beta:0        (32,)  AdagradW\n", "262                               LayerNorm_21/gamma:0        (32,)  AdagradW\n", "263                                LayerNorm_22/beta:0        (32,)  AdagradW\n", "264                               LayerNorm_22/gamma:0        (32,)  AdagradW\n", "265                 SENET_layer/excitation_layer_1/w:0     (11475,)  AdagradW\n", "266                 SENET_layer/excitation_layer_1/b:0        (45,)  AdagradW\n", "267    SENET_layer/excitation_layer_1/LayerNorm/beta:0        (45,)  AdagradW\n", "268   SENET_layer/excitation_layer_1/LayerNorm/gamma:0        (45,)  AdagradW\n", "269                 SENET_layer/excitation_layer_2/w:0     (11475,)  AdagradW\n", "270                 SENET_layer/excitation_layer_2/b:0       (255,)  AdagradW\n", "271                                candidate_layer/w:0   (1044480,)  AdagradW\n", "272                                candidate_layer/b:0       (256,)  AdagradW\n", "273            candidate_layer/rc_seq_q_trans_matrix:0      (8192,)  AdagradW\n", "274            candidate_layer/rc_seq_k_trans_matrix:0      (3072,)  AdagradW\n", "275            candidate_layer/rc_seq_v_trans_matrix:0      (3072,)  AdagradW\n", "276                                attention_layer/w:0   (4177920,)  AdagradW\n", "277                                attention_layer/b:0      (1024,)  AdagradW\n", "278         attention_layer/long_term_q_trans_matrix:0     (32768,)  AdagradW\n", "279         attention_layer/long_term_k_trans_matrix:0      (1792,)  AdagradW\n", "280         attention_layer/long_term_v_trans_matrix:0      (1792,)  AdagradW\n", "281      attention_layer/long_seq_suffix_pooling_0/w:0    (276480,)  AdagradW\n", "282      attention_layer/long_seq_suffix_pooling_0/b:0       (256,)  AdagradW\n", "283  attention_layer/long_seq_suffix_pooling_0/Laye...       (256,)  AdagradW\n", "284  attention_layer/long_seq_suffix_pooling_0/Laye...       (256,)  AdagradW\n", "285      attention_layer/long_seq_suffix_pooling_1/w:0      (8192,)  AdagradW\n", "286      attention_layer/long_seq_suffix_pooling_1/b:0        (32,)  AdagradW\n", "287  attention_layer/good_show_user_embedding_pooli...     (86016,)  AdagradW\n", "288  attention_layer/good_show_user_embedding_pooli...       (256,)  AdagradW\n", "289  attention_layer/good_show_user_embedding_pooli...       (256,)  AdagradW\n", "290  attention_layer/good_show_user_embedding_pooli...       (256,)  AdagradW\n", "291  attention_layer/good_show_user_embedding_pooli...      (8192,)  AdagradW\n", "292  attention_layer/good_show_user_embedding_pooli...        (32,)  AdagradW\n", "293  attention_layer/good_show_user_embedding_atten...      (9216,)  AdagradW\n", "294  attention_layer/good_show_user_embedding_atten...      (1536,)  AdagradW\n", "295  attention_layer/good_show_user_embedding_atten...      (1536,)  AdagradW\n", "296          good_click_cate2cate_real_price_fake0/w:0     (25600,)  AdagradW\n", "297          good_click_cate2cate_real_price_fake0/b:0        (64,)  AdagradW\n", "298          good_click_cate2cate_real_price_fake1/w:0      (2048,)  AdagradW\n", "299          good_click_cate2cate_real_price_fake1/b:0        (32,)  AdagradW\n", "300                                LayerNorm_23/beta:0        (32,)  AdagradW\n", "301                               LayerNorm_23/gamma:0        (32,)  AdagradW\n", "302                                LayerNorm_24/beta:0        (32,)  AdagradW\n", "303                               LayerNorm_24/gamma:0        (32,)  AdagradW\n", "304            good_click_cate2cate_category_fake0/w:0     (25600,)  AdagradW\n", "305            good_click_cate2cate_category_fake0/b:0        (64,)  AdagradW\n", "306            good_click_cate2cate_category_fake1/w:0      (2048,)  AdagradW\n", "307            good_click_cate2cate_category_fake1/b:0        (32,)  AdagradW\n", "308                                LayerNorm_25/beta:0        (32,)  AdagradW\n", "309                               LayerNorm_25/gamma:0        (32,)  AdagradW\n", "310                                LayerNorm_26/beta:0        (32,)  AdagradW\n", "311                               LayerNorm_26/gamma:0        (32,)  AdagradW\n", "312          good_click_cate2cate_carry_type_fake0/w:0     (12800,)  AdagradW\n", "313          good_click_cate2cate_carry_type_fake0/b:0        (64,)  AdagradW\n", "314          good_click_cate2cate_carry_type_fake1/w:0      (2048,)  AdagradW\n", "315          good_click_cate2cate_carry_type_fake1/b:0        (32,)  AdagradW\n", "316                                LayerNorm_27/beta:0        (32,)  AdagradW\n", "317                               LayerNorm_27/gamma:0        (32,)  AdagradW\n", "318                                LayerNorm_28/beta:0        (32,)  AdagradW\n", "319                               LayerNorm_28/gamma:0        (32,)  AdagradW\n", "320                 good_click_cate2cate_lag_fake0/w:0     (25600,)  AdagradW\n", "321                 good_click_cate2cate_lag_fake0/b:0        (64,)  AdagradW\n", "322                 good_click_cate2cate_lag_fake1/w:0      (2048,)  AdagradW\n", "323                 good_click_cate2cate_lag_fake1/b:0        (32,)  AdagradW\n", "324                                LayerNorm_29/beta:0        (32,)  AdagradW\n", "325                               LayerNorm_29/gamma:0        (32,)  AdagradW\n", "326                                LayerNorm_30/beta:0        (32,)  AdagradW\n", "327                               LayerNorm_30/gamma:0        (32,)  AdagradW\n", "328             good_click_cate2cate_item_id_fake0/w:0     (51200,)  AdagradW\n", "329             good_click_cate2cate_item_id_fake0/b:0        (64,)  AdagradW\n", "330             good_click_cate2cate_item_id_fake1/w:0      (2048,)  AdagradW\n", "331             good_click_cate2cate_item_id_fake1/b:0        (32,)  AdagradW\n", "332                                LayerNorm_31/beta:0        (32,)  AdagradW\n", "333                               LayerNorm_31/gamma:0        (32,)  AdagradW\n", "334                                LayerNorm_32/beta:0        (32,)  AdagradW\n", "335                               LayerNorm_32/gamma:0        (32,)  AdagradW\n", "336           good_click_cate2cate_seller_id_fake0/w:0     (51200,)  AdagradW\n", "337           good_click_cate2cate_seller_id_fake0/b:0        (64,)  AdagradW\n", "338           good_click_cate2cate_seller_id_fake1/w:0      (2048,)  AdagradW\n", "339           good_click_cate2cate_seller_id_fake1/b:0        (32,)  AdagradW\n", "340                                LayerNorm_33/beta:0        (32,)  AdagradW\n", "341                               LayerNorm_33/gamma:0        (32,)  AdagradW\n", "342                                LayerNorm_34/beta:0        (32,)  AdagradW\n", "343                               LayerNorm_34/gamma:0        (32,)  AdagradW\n", "344               good_click_cate2cate_layer_new_0/w:0      (7680,)  AdagradW\n", "345               good_click_cate2cate_layer_new_0/b:0       (128,)  AdagradW\n", "346  good_click_cate2cate_layer_new_0/LayerNorm/beta:0       (128,)  AdagradW\n", "347  good_click_cate2cate_layer_new_0/LayerNorm/gam...       (128,)  AdagradW\n", "348               good_click_cate2cate_layer_new_1/w:0      (8192,)  AdagradW\n", "349               good_click_cate2cate_layer_new_1/b:0        (64,)  AdagradW\n", "350  good_click_cate2cate_layer_new_1/LayerNorm/beta:0        (64,)  AdagradW\n", "351  good_click_cate2cate_layer_new_1/LayerNorm/gam...        (64,)  AdagradW\n", "352               good_click_cate2cate_layer_new_2/w:0      (2048,)  AdagradW\n", "353               good_click_cate2cate_layer_new_2/b:0        (32,)  AdagradW\n", "354                            Cross_layer_sub/w_sub:0     (23280,)  AdagradW\n", "355                                    dcn_layer_0/u:0    (955072,)  AdagradW\n", "356                                    dcn_layer_0/v:0    (955072,)  AdagradW\n", "357                                    dcn_layer_0/b:0     (14923,)  AdagradW\n", "358                       dcn_layer_0/LayerNorm/beta:0     (14923,)  AdagradW\n", "359                      dcn_layer_0/LayerNorm/gamma:0     (14923,)  AdagradW\n", "360                                    dcn_layer_1/u:0    (955072,)  AdagradW\n", "361                                    dcn_layer_1/v:0    (955072,)  AdagradW\n", "362                                    dcn_layer_1/b:0     (14923,)  AdagradW\n", "363                QUEUE_SOFT/share_bottom_layer_0/w:0   (9122816,)  AdagradW\n", "364                QUEUE_SOFT/share_bottom_layer_0/b:0      (1024,)  AdagradW\n", "365   QUEUE_SOFT/share_bottom_layer_0/LayerNorm/beta:0      (1024,)  AdagradW\n", "366  QUEUE_SOFT/share_bottom_layer_0/LayerNorm/gamma:0      (1024,)  AdagradW\n", "367                         QUEUE_SOFT/pxr_layer_1/w:0    (262144,)  AdagradW\n", "368                         QUEUE_SOFT/pxr_layer_1/b:0       (256,)  AdagradW\n", "369            QUEUE_SOFT/pxr_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "370           QUEUE_SOFT/pxr_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "371                         QUEUE_SOFT/pxr_layer_2/w:0     (32768,)  AdagradW\n", "372                         QUEUE_SOFT/pxr_layer_2/b:0       (128,)  AdagradW\n", "373            QUEUE_SOFT/pxr_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "374           QUEUE_SOFT/pxr_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "375                         QUEUE_SOFT/pxr_layer_3/w:0       (256,)  AdagradW\n", "376                         QUEUE_SOFT/pxr_layer_3/b:0         (2,)  AdagradW\n", "377                         QUEUE_SOFT/ped_layer_1/w:0    (262144,)  AdagradW\n", "378                         QUEUE_SOFT/ped_layer_1/b:0       (256,)  AdagradW\n", "379            QUEUE_SOFT/ped_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "380           QUEUE_SOFT/ped_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "381                         QUEUE_SOFT/ped_layer_2/w:0     (32768,)  AdagradW\n", "382                         QUEUE_SOFT/ped_layer_2/b:0       (128,)  AdagradW\n", "383            QUEUE_SOFT/ped_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "384           QUEUE_SOFT/ped_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "385                         QUEUE_SOFT/ped_layer_3/w:0       (256,)  AdagradW\n", "386                         QUEUE_SOFT/ped_layer_3/b:0         (2,)  AdagradW\n", "387                   QUEUE_SOFT/ctr_upper_layer_1/w:0    (262144,)  AdagradW\n", "388                   QUEUE_SOFT/ctr_upper_layer_1/b:0       (256,)  AdagradW\n", "389      QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "390     QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "391                   QUEUE_SOFT/ctr_upper_layer_2/w:0     (32768,)  AdagradW\n", "392                   QUEUE_SOFT/ctr_upper_layer_2/b:0       (128,)  AdagradW\n", "393      QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "394     QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "395                   QUEUE_SOFT/ctr_upper_layer_3/w:0       (256,)  AdagradW\n", "396                   QUEUE_SOFT/ctr_upper_layer_3/b:0         (2,)  AdagradW\n", "397                       QUEUE_SOFT/upper_layer_1/w:0    (262144,)  AdagradW\n", "398                       QUEUE_SOFT/upper_layer_1/b:0       (256,)  AdagradW\n", "399          QUEUE_SOFT/upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "400         QUEUE_SOFT/upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "401                       QUEUE_SOFT/upper_layer_2/w:0     (32768,)  AdagradW\n", "402                       QUEUE_SOFT/upper_layer_2/b:0       (128,)  AdagradW\n", "403          QUEUE_SOFT/upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "404         QUEUE_SOFT/upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "405                       QUEUE_SOFT/upper_layer_3/w:0       (256,)  AdagradW\n", "406                       QUEUE_SOFT/upper_layer_3/b:0         (2,)  AdagradW\n", "407                               QUEUE_SOFT/w_ctcvr:0         (1,)  AdagradW\n", "408                       share_bottom_layer_new_0/w:0  (15281152,)  AdagradW\n", "409                       share_bottom_layer_new_0/b:0      (1024,)  AdagradW\n", "410          share_bottom_layer_new_0/LayerNorm/beta:0      (1024,)  AdagradW\n", "411         share_bottom_layer_new_0/LayerNorm/gamma:0      (1024,)  AdagradW\n", "412                             generalize_layer_0/w:0     (30720,)  AdagradW\n", "413                             generalize_layer_0/b:0       (128,)  AdagradW\n", "414                generalize_layer_0/LayerNorm/beta:0       (128,)  AdagradW\n", "415               generalize_layer_0/LayerNorm/gamma:0       (128,)  AdagradW\n", "416                  reco_share_bottom_layer_new_0/w:0   (5529600,)  AdagradW\n", "417                  reco_share_bottom_layer_new_0/b:0      (1024,)  AdagradW\n", "418     reco_share_bottom_layer_new_0/LayerNorm/beta:0      (1024,)  AdagradW\n", "419    reco_share_bottom_layer_new_0/LayerNorm/gamma:0      (1024,)  AdagradW\n", "420                             generalize_layer_1/w:0      (8192,)  AdagradW\n", "421                             generalize_layer_1/b:0        (64,)  AdagradW\n", "422                generalize_layer_1/LayerNorm/beta:0        (64,)  AdagradW\n", "423               generalize_layer_1/LayerNorm/gamma:0        (64,)  AdagradW\n", "424                             generalize_layer_2/w:0      (2048,)  AdagradW\n", "425                             generalize_layer_2/b:0        (32,)  AdagradW\n", "426                generalize_layer_2/LayerNorm/beta:0        (32,)  AdagradW\n", "427               generalize_layer_2/LayerNorm/gamma:0        (32,)  AdagradW\n", "428                             generalize_layer_3/w:0        (64,)  AdagradW\n", "429                             generalize_layer_3/b:0         (2,)  AdagradW\n", "430                                    pxr_layer_1/w:0    (262144,)  AdagradW\n", "431                                    pxr_layer_1/b:0       (256,)  AdagradW\n", "432                       pxr_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "433                      pxr_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "434                                    pxr_layer_2/w:0     (32768,)  AdagradW\n", "435                                    pxr_layer_2/b:0       (128,)  AdagradW\n", "436                       pxr_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "437                      pxr_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "438                                    pxr_layer_3/w:0       (256,)  AdagradW\n", "439                                    pxr_layer_3/b:0         (2,)  AdagradW\n", "440                                    ped_layer_1/w:0    (262144,)  AdagradW\n", "441                                    ped_layer_1/b:0       (256,)  AdagradW\n", "442                       ped_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "443                      ped_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "444                                    ped_layer_2/w:0     (32768,)  AdagradW\n", "445                                    ped_layer_2/b:0       (128,)  AdagradW\n", "446                       ped_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "447                      ped_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "448                                    ped_layer_3/w:0       (256,)  AdagradW\n", "449                                    ped_layer_3/b:0         (2,)  AdagradW\n", "450                              ctr_upper_layer_1/w:0    (262144,)  AdagradW\n", "451                              ctr_upper_layer_1/b:0       (256,)  AdagradW\n", "452                 ctr_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "453                ctr_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "454                              ctr_upper_layer_2/w:0     (32768,)  AdagradW\n", "455                              ctr_upper_layer_2/b:0       (128,)  AdagradW\n", "456                 ctr_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "457                ctr_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "458                              ctr_upper_layer_3/w:0       (256,)  AdagradW\n", "459                              ctr_upper_layer_3/b:0         (2,)  AdagradW\n", "460                          assist1_upper_layer_1/w:0    (262144,)  AdagradW\n", "461                          assist1_upper_layer_1/b:0       (256,)  AdagradW\n", "462             assist1_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "463            assist1_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "464                          assist1_upper_layer_2/w:0     (32768,)  AdagradW\n", "465                          assist1_upper_layer_2/b:0       (128,)  AdagradW\n", "466             assist1_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "467            assist1_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "468                          assist1_upper_layer_3/w:0       (256,)  AdagradW\n", "469                          assist1_upper_layer_3/b:0         (2,)  AdagradW\n", "470                          assist2_upper_layer_1/w:0    (262144,)  AdagradW\n", "471                          assist2_upper_layer_1/b:0       (256,)  AdagradW\n", "472             assist2_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "473            assist2_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "474                          assist2_upper_layer_2/w:0     (32768,)  AdagradW\n", "475                          assist2_upper_layer_2/b:0       (128,)  AdagradW\n", "476             assist2_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "477            assist2_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "478                          assist2_upper_layer_3/w:0       (256,)  AdagradW\n", "479                          assist2_upper_layer_3/b:0         (2,)  AdagradW\n", "480                                    cid_layer_1/w:0    (262144,)  AdagradW\n", "481                                    cid_layer_1/b:0       (256,)  AdagradW\n", "482                       cid_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "483                      cid_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "484                                    cid_layer_2/w:0     (32768,)  AdagradW\n", "485                                    cid_layer_2/b:0       (128,)  AdagradW\n", "486                       cid_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "487                      cid_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "488                                    cid_layer_3/w:0       (256,)  AdagradW\n", "489                                    cid_layer_3/b:0         (2,)  AdagradW\n", "490                                  upper_layer_1/w:0    (295168,)  AdagradW\n", "491                                  upper_layer_1/b:0       (256,)  AdagradW\n", "492                    upper_layer_1/layer_norm/beta:0       (256,)  AdagradW\n", "493                   upper_layer_1/layer_norm/gamma:0       (256,)  AdagradW\n", "494                                  upper_layer_2/w:0     (32768,)  AdagradW\n", "495                                  upper_layer_2/b:0       (128,)  AdagradW\n", "496                    upper_layer_2/layer_norm/beta:0       (128,)  AdagradW\n", "497                   upper_layer_2/layer_norm/gamma:0       (128,)  AdagradW\n", "498                                  upper_layer_3/w:0       (256,)  AdagradW\n", "499                                  upper_layer_3/b:0         (2,)  AdagradW\n", "500                           rocket_upper_layer_2/w:0    (110592,)  AdagradW\n", "501                           rocket_upper_layer_2/b:0       (128,)  AdagradW\n", "502              rocket_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "503             rocket_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "504                           rocket_upper_layer_3/w:0       (256,)  AdagradW\n", "505                           rocket_upper_layer_3/b:0         (2,)  AdagradW\n", "506                                 global_layer_1/w:0    (262144,)  AdagradW\n", "507                                 global_layer_1/b:0       (256,)  AdagradW\n", "508                    global_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "509                   global_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "510                                 global_layer_2/w:0     (32768,)  AdagradW\n", "511                                 global_layer_2/b:0       (128,)  AdagradW\n", "512                    global_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "513                   global_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "514                                 global_layer_3/w:0       (256,)  AdagradW\n", "515                                 global_layer_3/b:0         (2,)  AdagradW\n", "516                                 direct_layer_1/w:0    (262144,)  AdagradW\n", "517                                 direct_layer_1/b:0       (256,)  AdagradW\n", "518                    direct_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "519                   direct_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "520                                 direct_layer_2/w:0     (32768,)  AdagradW\n", "521                                 direct_layer_2/b:0       (128,)  AdagradW\n", "522                    direct_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "523                   direct_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "524                                 direct_layer_3/w:0       (256,)  AdagradW\n", "525                                 direct_layer_3/b:0         (2,)  AdagradW\n", "526                      pic_calibration/cali_lambda:0         (1,)  AdagradW\n", "527               Pic_calibration/cali_weight_lambda:0         (7,)  AdagradW\n", "528                                    gmv_layer_0/w:0    (262144,)  AdagradW\n", "529                                    gmv_layer_0/b:0       (256,)  AdagradW\n", "530                       gmv_layer_0/LayerNorm/beta:0       (256,)  AdagradW\n", "531                      gmv_layer_0/LayerNorm/gamma:0       (256,)  AdagradW\n", "532                                    gmv_layer_1/w:0     (32768,)  AdagradW\n", "533                                    gmv_layer_1/b:0       (128,)  AdagradW\n", "534                       gmv_layer_1/LayerNorm/beta:0       (128,)  AdagradW\n", "535                      gmv_layer_1/LayerNorm/gamma:0       (128,)  AdagradW\n", "536                                    gmv_layer_2/w:0       (256,)  AdagradW\n", "537                                    gmv_layer_2/b:0         (2,)  AdagradW\n", "538                             reco_upper_layer_1/w:0    (262144,)  AdagradW\n", "539                             reco_upper_layer_1/b:0       (256,)  AdagradW\n", "540                reco_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "541               reco_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "542                             reco_upper_layer_2/w:0     (32768,)  AdagradW\n", "543                             reco_upper_layer_2/b:0       (128,)  AdagradW\n", "544                reco_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "545               reco_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "546                             reco_upper_layer_3/w:0       (256,)  AdagradW\n", "547                             reco_upper_layer_3/b:0         (2,)  AdagradW\n", "548                                          w_ctcvr:0         (1,)  AdagradW\n", "549                                            w_ctr:0         (1,)  AdagradW\n", "550                                            w_p3s:0         (1,)  AdagradW\n", "551                                            w_ped:0         (1,)  AdagradW\n", "552                                     w_reco_ctcvr:0         (1,)  AdagradW\n", "553                                     w_global_pay:0         (1,)  AdagradW\n", "554                                     w_direct_pay:0         (1,)  AdagradW\n", "555                                           w_ast2:0         (1,)  AdagradW\n", "556                                       w_ue_ctcvr:0         (1,)  AdagradW\n"]}], "source": ["print(dense_new)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # type==change，原来的小尺寸、现在变大的weight和它的原始shape\n", "\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "\n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name == 'rocket_upper_layer_2/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:560, :] = ori_weight[:560, :]\n", "            new_extra[:560, :,:] = ori_extra[:560, :,:]\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(ue_rank_index_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ue_rank_index_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(inner_order_rank_index_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(inner_order_rank_index_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(rocket_upper_layer_2/w:0) 不存在，其值由rocket_upper_layer_2/w:0初始化, size is 110592 and 110592\n", "加载的 dense variable(inner_order_num_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(inner_order_num_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(ue_num_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(ue_num_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(first_stage_ctcvr_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(first_stage_ctcvr_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_index_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_index_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(dcn_layer_1/LayerNorm/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(dcn_layer_1/LayerNorm/gamma:0) 在运行时不存在，其值被忽略。\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}