{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                                 name   origin_shape  current_shape    type\n", "0                        dcn_layer_0/LayerNorm/beta:0     (14923, 1)     (16654, 1)  change\n", "1                       dcn_layer_0/LayerNorm/gamma:0     (14923, 1)     (16654, 1)  change\n", "2                                     dcn_layer_0/b:0     (14923, 1)     (16654, 1)  change\n", "3                                     dcn_layer_0/u:0    (14923, 64)    (16654, 64)  change\n", "4                                     dcn_layer_0/v:0    (64, 14923)    (64, 16654)  change\n", "5                        dcn_layer_1/LayerNorm/beta:0     (14923, 1)     (16654, 1)  change\n", "6                       dcn_layer_1/LayerNorm/gamma:0     (14923, 1)     (16654, 1)  change\n", "7                                     dcn_layer_1/b:0     (14923, 1)     (16654, 1)  change\n", "8                                     dcn_layer_1/u:0    (14923, 64)    (16654, 64)  change\n", "9                                     dcn_layer_1/v:0    (64, 14923)    (64, 16654)  change\n", "10                           rocket_upper_layer_2/w:0    (1008, 128)     (864, 128)  change\n", "11                       share_bottom_layer_new_0/w:0  (14923, 1024)  (16654, 1024)  change\n", "12                    first_stage_ctcvr_sparse/beta:0        (16, 1)           None  delete\n", "13                   first_stage_ctcvr_sparse/gamma:0        (16, 1)           None  delete\n", "14                      inner_order_num_sparse/beta:0        (16, 1)           None  delete\n", "15                     inner_order_num_sparse/gamma:0        (16, 1)           None  delete\n", "16                   rank_context_index_sparse/beta:0       (320, 1)           None  delete\n", "17                  rank_context_index_sparse/gamma:0       (320, 1)           None  delete\n", "18                         rank_context_sparse/beta:0        (80, 1)           None  delete\n", "19                        rank_context_sparse/gamma:0        (80, 1)           None  delete\n", "20                               ue_num_sparse/beta:0        (16, 1)           None  delete\n", "21                              ue_num_sparse/gamma:0        (16, 1)           None  delete\n", "22            ExtractDenseUserLlmDpskValidUser/beta:0           None       (256, 1)     add\n", "23           ExtractDenseUserLlmDpskValidUser/gamma:0           None       (256, 1)     add\n", "24                         ExtractGoodsHetuEmb/beta:0           None       (128, 1)     add\n", "25                        ExtractGoodsHetuEmb/gamma:0           None       (128, 1)     add\n", "26                   ExtractPhotoTextEmbedding/beta:0           None       (256, 1)     add\n", "27                  ExtractPhotoTextEmbedding/gamma:0           None       (256, 1)     add\n", "28                           ExtractPidHetuEmb/beta:0           None       (128, 1)     add\n", "29                          ExtractPidHetuEmb/gamma:0           None       (128, 1)     add\n", "30                            ExtractPidSim128/beta:0           None         (1, 1)     add\n", "31                           ExtractPidSim128/gamma:0           None         (1, 1)     add\n", "32                             ExtractPidSim1k/beta:0           None         (1, 1)     add\n", "33                            ExtractPidSim1k/gamma:0           None         (1, 1)     add\n", "34                            ExtractPidSim512/beta:0           None         (1, 1)     add\n", "35                           ExtractPidSim512/gamma:0           None         (1, 1)     add\n", "36    attention_layer/long_recent_attk_trans_matrix:0           None       (56, 32)     add\n", "37    attention_layer/long_recent_attq_trans_matrix:0           None       (48, 32)     add\n", "38    attention_layer/long_recent_attv_trans_matrix:0           None       (56, 32)     add\n", "39                      gc_recent_attk_trans_matrix:0           None       (60, 32)     add\n", "40                      gc_recent_attq_trans_matrix:0           None       (48, 32)     add\n", "41                      gc_recent_attv_trans_matrix:0           None       (60, 32)     add\n", "42           gc_self_attention_layer/LayerNorm/beta:0           None        (64, 1)     add\n", "43          gc_self_attention_layer/LayerNorm/gamma:0           None        (64, 1)     add\n", "44                        gc_self_attention_layer/b:0           None        (64, 1)     add\n", "45  gc_self_attention_layer/gc_self_attnk_trans_ma...           None       (60, 32)     add\n", "46  gc_self_attention_layer/gc_self_attnq_trans_ma...           None       (60, 32)     add\n", "47  gc_self_attention_layer/gc_self_attnv_trans_ma...           None       (60, 32)     add\n", "48                        gc_self_attention_layer/w:0           None     (1600, 64)     add\n", "49         long_self_attention_layer/LayerNorm/beta:0           None        (64, 1)     add\n", "50        long_self_attention_layer/LayerNorm/gamma:0           None        (64, 1)     add\n", "51                      long_self_attention_layer/b:0           None        (64, 1)     add\n", "52  long_self_attention_layer/long_self_attnk_tran...           None       (56, 32)     add\n", "53  long_self_attention_layer/long_self_attnq_tran...           None       (56, 32)     add\n", "54  long_self_attention_layer/long_self_attnv_tran...           None       (56, 32)     add\n", "55                      long_self_attention_layer/w:0           None     (3200, 64)     add\n", "56                               photo_content/beta:0           None       (176, 1)     add\n", "57                              photo_content/gamma:0           None       (176, 1)     add\n", "58                                user_predict/beta:0           None        (32, 1)     add\n", "59                               user_predict/gamma:0           None        (32, 1)     add\n", "Dense Extra Diff: \n", "                                                 name origin_shape current_shape    type\n", "0                        dcn_layer_0/LayerNorm/beta:0     (14923,)      (16654,)  change\n", "1                       dcn_layer_0/LayerNorm/gamma:0     (14923,)      (16654,)  change\n", "2                                     dcn_layer_0/b:0     (14923,)      (16654,)  change\n", "3                                     dcn_layer_0/u:0    (955072,)    (1065856,)  change\n", "4                                     dcn_layer_0/v:0    (955072,)    (1065856,)  change\n", "5                        dcn_layer_1/LayerNorm/beta:0     (14923,)      (16654,)  change\n", "6                       dcn_layer_1/LayerNorm/gamma:0     (14923,)      (16654,)  change\n", "7                                     dcn_layer_1/b:0     (14923,)      (16654,)  change\n", "8                                     dcn_layer_1/u:0    (955072,)    (1065856,)  change\n", "9                                     dcn_layer_1/v:0    (955072,)    (1065856,)  change\n", "10                           rocket_upper_layer_2/w:0    (129024,)     (110592,)  change\n", "11                       share_bottom_layer_new_0/w:0  (15281152,)   (17053696,)  change\n", "12                    first_stage_ctcvr_sparse/beta:0        (16,)          None  delete\n", "13                   first_stage_ctcvr_sparse/gamma:0        (16,)          None  delete\n", "14                      inner_order_num_sparse/beta:0        (16,)          None  delete\n", "15                     inner_order_num_sparse/gamma:0        (16,)          None  delete\n", "16                   rank_context_index_sparse/beta:0       (320,)          None  delete\n", "17                  rank_context_index_sparse/gamma:0       (320,)          None  delete\n", "18                         rank_context_sparse/beta:0        (80,)          None  delete\n", "19                        rank_context_sparse/gamma:0        (80,)          None  delete\n", "20                               ue_num_sparse/beta:0        (16,)          None  delete\n", "21                              ue_num_sparse/gamma:0        (16,)          None  delete\n", "22            ExtractDenseUserLlmDpskValidUser/beta:0         None        (256,)     add\n", "23           ExtractDenseUserLlmDpskValidUser/gamma:0         None        (256,)     add\n", "24                         ExtractGoodsHetuEmb/beta:0         None        (128,)     add\n", "25                        ExtractGoodsHetuEmb/gamma:0         None        (128,)     add\n", "26                   ExtractPhotoTextEmbedding/beta:0         None        (256,)     add\n", "27                  ExtractPhotoTextEmbedding/gamma:0         None        (256,)     add\n", "28                           ExtractPidHetuEmb/beta:0         None        (128,)     add\n", "29                          ExtractPidHetuEmb/gamma:0         None        (128,)     add\n", "30                            ExtractPidSim128/beta:0         None          (1,)     add\n", "31                           ExtractPidSim128/gamma:0         None          (1,)     add\n", "32                             ExtractPidSim1k/beta:0         None          (1,)     add\n", "33                            ExtractPidSim1k/gamma:0         None          (1,)     add\n", "34                            ExtractPidSim512/beta:0         None          (1,)     add\n", "35                           ExtractPidSim512/gamma:0         None          (1,)     add\n", "36    attention_layer/long_recent_attk_trans_matrix:0         None       (1792,)     add\n", "37    attention_layer/long_recent_attq_trans_matrix:0         None       (1536,)     add\n", "38    attention_layer/long_recent_attv_trans_matrix:0         None       (1792,)     add\n", "39                      gc_recent_attk_trans_matrix:0         None       (1920,)     add\n", "40                      gc_recent_attq_trans_matrix:0         None       (1536,)     add\n", "41                      gc_recent_attv_trans_matrix:0         None       (1920,)     add\n", "42           gc_self_attention_layer/LayerNorm/beta:0         None         (64,)     add\n", "43          gc_self_attention_layer/LayerNorm/gamma:0         None         (64,)     add\n", "44                        gc_self_attention_layer/b:0         None         (64,)     add\n", "45  gc_self_attention_layer/gc_self_attnk_trans_ma...         None       (1920,)     add\n", "46  gc_self_attention_layer/gc_self_attnq_trans_ma...         None       (1920,)     add\n", "47  gc_self_attention_layer/gc_self_attnv_trans_ma...         None       (1920,)     add\n", "48                        gc_self_attention_layer/w:0         None     (102400,)     add\n", "49         long_self_attention_layer/LayerNorm/beta:0         None         (64,)     add\n", "50        long_self_attention_layer/LayerNorm/gamma:0         None         (64,)     add\n", "51                      long_self_attention_layer/b:0         None         (64,)     add\n", "52  long_self_attention_layer/long_self_attnk_tran...         None       (1792,)     add\n", "53  long_self_attention_layer/long_self_attnq_tran...         None       (1792,)     add\n", "54  long_self_attention_layer/long_self_attnv_tran...         None       (1792,)     add\n", "55                      long_self_attention_layer/w:0         None     (204800,)     add\n", "56                               photo_content/beta:0         None        (176,)     add\n", "57                              photo_content/gamma:0         None        (176,)     add\n", "58                                user_predict/beta:0         None         (32,)     add\n", "59                               user_predict/gamma:0         None         (32,)     add\n", "\n", "粘贴到my_load_dense_func开头的origin_weight处\n", "{'dcn_layer_0/LayerNorm/beta:0': (14923, 1), 'dcn_layer_0/LayerNorm/gamma:0': (14923, 1), 'dcn_layer_0/b:0': (14923, 1), 'dcn_layer_0/u:0': (14923, 64), 'dcn_layer_0/v:0': (64, 14923), 'dcn_layer_1/LayerNorm/beta:0': (14923, 1), 'dcn_layer_1/LayerNorm/gamma:0': (14923, 1), 'dcn_layer_1/b:0': (14923, 1), 'dcn_layer_1/u:0': (14923, 64), 'dcn_layer_1/v:0': (64, 14923), 'rocket_upper_layer_2/w:0': (1008, 128), 'share_bottom_layer_new_0/w:0': (14923, 1024)}\n"]}], "source": ["old_yaml = 'xiatian06_dsp_lps_sv_xt_context_re_grpo_v2.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_good_click_soft_re_grpo_v2.yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)\n", "\n", "weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}\n", "extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}\n", "print()\n", "print('粘贴到my_load_dense_func开头的origin_weight处')\n", "print(weight_diff_change)\n", "# print(extra_diff_change)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LayerNorm/beta:0 (11057, 1)\n", "LayerNorm/gamma:0 (11057, 1)\n", "cot_dense_moe_0_expert0/w:0 (384, 128)\n", "cot_dense_moe_1_expert0/w:0 (384, 128)\n", "sft_dense_moe_0_expert0/w:0 (512, 128)\n", "sft_dense_moe_1_expert0/w:0 (512, 128)\n", "pinnerformer_input0/w:0 (128, 512)\n", "pinnerformer_input1/w:0 (512, 128)\n", "llm_other_input0/w:0 (2005, 512)\n", "llm_other_input1/w:0 (512, 128)\n", "shark_refine_layer_0/w:0 (128, 512)\n", "shark_refine_layer_1/w:0 (512, 256)\n", "shark_head_layer_0/w:0 (256, 128)\n", "long_term_pids_fake0/w:0 (800, 64)\n", "long_term_aids_fake0/w:0 (800, 64)\n", "long_term_play_fake0/w:0 (400, 64)\n", "long_term_tags_fake0/w:0 (400, 64)\n", "long_term_times_fake0/w:0 (400, 64)\n", "colossus_rs_count_index_list_cl0/w:0 (200, 64)\n", "colossus_rs_item_id_list_cl0/w:0 (800, 64)\n", "colossus_rs_lagV1_list_cl0/w:0 (200, 64)\n", "colossus_rs_lagV2_list0/w:0 (200, 64)\n", "colossus_rs_pagecode_id_list_cl0/w:0 (200, 64)\n", "colossus_rs_uniform_spu_id_list_cl0/w:0 (800, 64)\n", "SENET_layer/excitation_layer_1/w:0 (255, 45)\n", "SENET_layer/excitation_layer_2/w:0 (45, 255)\n", "candidate_layer/w:0 (4080, 256)\n", "attention_layer/w:0 (4080, 1024)\n", "attention_layer/long_term_q_trans_matrix:0 (1024, 32)\n", "attention_layer/long_seq_suffix_pooling_0/w:0 (1080, 256)\n", "attention_layer/good_show_user_embedding_pooling_0/w:0 (336, 256)\n", "good_click_cate2cate_real_price_fake0/w:0 (400, 64)\n", "good_click_cate2cate_category_fake0/w:0 (400, 64)\n", "good_click_cate2cate_carry_type_fake0/w:0 (200, 64)\n", "good_click_cate2cate_lag_fake0/w:0 (400, 64)\n", "good_click_cate2cate_item_id_fake0/w:0 (800, 64)\n", "good_click_cate2cate_seller_id_fake0/w:0 (800, 64)\n", "gc_self_attention_layer/w:0 (1600, 64)\n", "long_self_attention_layer/w:0 (3200, 64)\n", "Cross_layer_sub/w_sub:0 (4, 5820)\n", "QUEUE_SOFT/share_bottom_layer_0/w:0 (8909, 1024)\n", "QUEUE_SOFT/pxr_layer_1/w:0 (1024, 256)\n", "QUEUE_SOFT/pxr_layer_2/w:0 (256, 128)\n", "QUEUE_SOFT/ped_layer_1/w:0 (1024, 256)\n", "QUEUE_SOFT/ped_layer_2/w:0 (256, 128)\n", "QUEUE_SOFT/ctr_upper_layer_1/w:0 (1024, 256)\n", "QUEUE_SOFT/ctr_upper_layer_2/w:0 (256, 128)\n", "QUEUE_SOFT/upper_layer_1/w:0 (1024, 256)\n", "QUEUE_SOFT/upper_layer_2/w:0 (256, 128)\n", "dcn_layer_0/u:0 (16654, 64)\n", "dcn_layer_0/v:0 (64, 16654)\n", "dcn_layer_0/b:0 (16654, 1)\n", "dcn_layer_0/LayerNorm/beta:0 (16654, 1)\n", "dcn_layer_0/LayerNorm/gamma:0 (16654, 1)\n", "dcn_layer_1/u:0 (16654, 64)\n", "dcn_layer_1/v:0 (64, 16654)\n", "dcn_layer_1/b:0 (16654, 1)\n", "dcn_layer_1/LayerNorm/beta:0 (16654, 1)\n", "dcn_layer_1/LayerNorm/gamma:0 (16654, 1)\n", "share_bottom_layer_new_0/w:0 (16654, 1024)\n", "generalize_layer_0/w:0 (240, 128)\n", "reco_share_bottom_layer_new_0/w:0 (5400, 1024)\n", "pxr_layer_1/w:0 (1024, 256)\n", "pxr_layer_2/w:0 (256, 128)\n", "ped_layer_1/w:0 (1024, 256)\n", "ped_layer_2/w:0 (256, 128)\n", "ctr_upper_layer_1/w:0 (1024, 256)\n", "ctr_upper_layer_2/w:0 (256, 128)\n", "assist1_upper_layer_1/w:0 (1024, 256)\n", "assist1_upper_layer_2/w:0 (256, 128)\n", "assist2_upper_layer_1/w:0 (1024, 256)\n", "assist2_upper_layer_2/w:0 (256, 128)\n", "cid_layer_1/w:0 (1024, 256)\n", "cid_layer_2/w:0 (256, 128)\n", "upper_layer_1/w:0 (1153, 256)\n", "upper_layer_2/w:0 (256, 128)\n", "rocket_upper_layer_2/w:0 (864, 128)\n", "global_layer_1/w:0 (1024, 256)\n", "global_layer_2/w:0 (256, 128)\n", "direct_layer_1/w:0 (1024, 256)\n", "direct_layer_2/w:0 (256, 128)\n", "gmv_layer_0/w:0 (1024, 256)\n", "gmv_layer_1/w:0 (256, 128)\n", "reco_upper_layer_1/w:0 (1024, 256)\n", "reco_upper_layer_2/w:0 (256, 128)\n"]}], "source": ["l = len(dense_new.var_name_list)\n", "for i in range(l):\n", "    a,b = dense_new.shape_list[i]\n", "    if a and b and a*b >10000:\n", "        print(dense_new.var_name_list[i], dense_new.shape_list[i])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DenseTable: xiatian06_dsp_lps_sv_xt_context_re_merge_v01.yaml \n", "Weight: \n", "                                                  name          shape\n", "0                                       u_p_input0/w:0       (32, 32)\n", "1                                       u_p_input0/b:0        (32, 1)\n", "2                                       u_p_input1/w:0        (32, 2)\n", "3                                       u_p_input1/b:0         (2, 1)\n", "4                                       u_i_input0/w:0       (32, 32)\n", "5                                       u_i_input0/b:0        (32, 1)\n", "6                                       u_i_input1/w:0        (32, 2)\n", "7                                       u_i_input1/b:0         (2, 1)\n", "8                                 context_jrc_loss/w:0        (32, 1)\n", "9                                 context_jrc_loss/b:0         (1, 1)\n", "10                                    LayerNorm/beta:0     (11057, 1)\n", "11                                   LayerNorm/gamma:0     (11057, 1)\n", "12                             generalize_layer/beta:0       (240, 1)\n", "13                            generalize_layer/gamma:0       (240, 1)\n", "14                                    akg_layer/beta:0       (160, 1)\n", "15                                   akg_layer/gamma:0       (160, 1)\n", "16                                      new_fea/beta:0       (272, 1)\n", "17                                     new_fea/gamma:0       (272, 1)\n", "18                            search_feat_layer/beta:0       (128, 1)\n", "19                           search_feat_layer/gamma:0       (128, 1)\n", "20                             goods_feat_layer/beta:0       (288, 1)\n", "21                            goods_feat_layer/gamma:0       (288, 1)\n", "22                          item_add_feas_layer/beta:0       (320, 1)\n", "23                         item_add_feas_layer/gamma:0       (320, 1)\n", "24                              shark_emb_layer/beta:0       (128, 1)\n", "25                             shark_emb_layer/gamma:0       (128, 1)\n", "26                      llm_keywords_feas_layer/beta:0       (128, 1)\n", "27                     llm_keywords_feas_layer/gamma:0       (128, 1)\n", "28                           cid_add_feas_layer/beta:0       (320, 1)\n", "29                          cid_add_feas_layer/gamma:0       (320, 1)\n", "30                            add_new_fea_layer/beta:0       (280, 1)\n", "31                           add_new_fea_layer/gamma:0       (280, 1)\n", "32                            add_cpg_fea_layer/beta:0       (208, 1)\n", "33                           add_cpg_fea_layer/gamma:0       (208, 1)\n", "34                            add_adx_fea_layer/beta:0       (128, 1)\n", "35                           add_adx_fea_layer/gamma:0       (128, 1)\n", "36                      pinnerformer_user_layer/beta:0        (64, 1)\n", "37                     pinnerformer_user_layer/gamma:0        (64, 1)\n", "38                      pinnerformer_item_layer/beta:0        (64, 1)\n", "39                     pinnerformer_item_layer/gamma:0        (64, 1)\n", "40                            add_rta_fea_layer/beta:0       (176, 1)\n", "41                           add_rta_fea_layer/gamma:0       (176, 1)\n", "42                               reco_dnn_input/beta:0      (4888, 1)\n", "43                              reco_dnn_input/gamma:0      (4888, 1)\n", "44                              utype_fea_layer/beta:0        (16, 1)\n", "45                             utype_fea_layer/gamma:0        (16, 1)\n", "46                             cot_sparse_layer/beta:0       (336, 1)\n", "47                            cot_sparse_layer/gamma:0       (336, 1)\n", "48                              cot_dense_layer/beta:0       (384, 1)\n", "49                             cot_dense_layer/gamma:0       (384, 1)\n", "50                                 entity_layer/beta:0       (128, 1)\n", "51                                entity_layer/gamma:0       (128, 1)\n", "52                              is_ai_tag_layer/beta:0        (16, 1)\n", "53                             is_ai_tag_layer/gamma:0        (16, 1)\n", "54                                    sft_layer/beta:0       (512, 1)\n", "55                                   sft_layer/gamma:0       (512, 1)\n", "56                               reco_sft_layer/beta:0       (512, 1)\n", "57                              reco_sft_layer/gamma:0       (512, 1)\n", "58                             multimodal_layer/beta:0       (128, 1)\n", "59                            multimodal_layer/gamma:0       (128, 1)\n", "60                                 coupon_layer/beta:0        (80, 1)\n", "61                                coupon_layer/gamma:0        (80, 1)\n", "62                           user_ecom_rq_layer/beta:0       (288, 1)\n", "63                          user_ecom_rq_layer/gamma:0       (288, 1)\n", "64                          match_0_dense_layer/beta:0         (5, 1)\n", "65                         match_0_dense_layer/gamma:0         (5, 1)\n", "66                          match_1_dense_layer/beta:0         (5, 1)\n", "67                         match_1_dense_layer/gamma:0         (5, 1)\n", "68                          match_2_dense_layer/beta:0         (5, 1)\n", "69                         match_2_dense_layer/gamma:0         (5, 1)\n", "70                          match_3_dense_layer/beta:0         (5, 1)\n", "71                         match_3_dense_layer/gamma:0         (5, 1)\n", "72                          match_4_dense_layer/beta:0         (5, 1)\n", "73                         match_4_dense_layer/gamma:0         (5, 1)\n", "74                        ecom_multimodal_layer/beta:0       (128, 1)\n", "75                       ecom_multimodal_layer/gamma:0       (128, 1)\n", "76                             ec_detaile_layer/beta:0        (80, 1)\n", "77                            ec_detaile_layer/gamma:0        (80, 1)\n", "78                           commentstats_layer/beta:0         (5, 1)\n", "79                          commentstats_layer/gamma:0         (5, 1)\n", "80                                          dpo/beta:0        (32, 1)\n", "81                                         dpo/gamma:0        (32, 1)\n", "82                                    dense_dpo/beta:0       (128, 1)\n", "83                                   dense_dpo/gamma:0       (128, 1)\n", "84               good_click_cate2cate_dnn_layer/beta:0      (3000, 1)\n", "85              good_click_cate2cate_dnn_layer/gamma:0      (3000, 1)\n", "86                     good_show_user_embedding/beta:0      (4800, 1)\n", "87                    good_show_user_embedding/gamma:0      (4800, 1)\n", "88                                     eshop_ad/beta:0       (128, 1)\n", "89                                    eshop_ad/gamma:0       (128, 1)\n", "90                              ue_score_sparse/beta:0       (304, 1)\n", "91                             ue_score_sparse/gamma:0       (304, 1)\n", "92                      ExtractUserDenseGrpoFea/beta:0      (1024, 1)\n", "93                     ExtractUserDenseGrpoFea/gamma:0      (1024, 1)\n", "94                                  grpo_sparse/beta:0        (64, 1)\n", "95                                 grpo_sparse/gamma:0        (64, 1)\n", "96                                 user_predict/beta:0        (32, 1)\n", "97                                user_predict/gamma:0        (32, 1)\n", "98                                photo_content/beta:0       (176, 1)\n", "99                               photo_content/gamma:0       (176, 1)\n", "100            ExtractDenseUserLlmDpskValidUser/beta:0       (256, 1)\n", "101           ExtractDenseUserLlmDpskValidUser/gamma:0       (256, 1)\n", "102                   ExtractPhotoTextEmbedding/beta:0       (256, 1)\n", "103                  ExtractPhotoTextEmbedding/gamma:0       (256, 1)\n", "104                           ExtractPidHetuEmb/beta:0       (128, 1)\n", "105                          ExtractPidHetuEmb/gamma:0       (128, 1)\n", "106                         ExtractGoodsHetuEmb/beta:0       (128, 1)\n", "107                        ExtractGoodsHetuEmb/gamma:0       (128, 1)\n", "108                            ExtractPidSim512/beta:0         (1, 1)\n", "109                           ExtractPidSim512/gamma:0         (1, 1)\n", "110                            ExtractPidSim128/beta:0         (1, 1)\n", "111                           ExtractPidSim128/gamma:0         (1, 1)\n", "112                             ExtractPidSim1k/beta:0         (1, 1)\n", "113                            ExtractPidSim1k/gamma:0         (1, 1)\n", "114                 user_videoclip_emb_ln_layer/beta:0       (384, 1)\n", "115                user_videoclip_emb_ln_layer/gamma:0       (384, 1)\n", "116             ConcatKeywordVideoClip_ln_layer/beta:0        (64, 1)\n", "117            ConcatKeywordVideoClip_ln_layer/gamma:0        (64, 1)\n", "118               video_clip_attentionq_trans_matrix:0       (64, 64)\n", "119               video_clip_attentionk_trans_matrix:0       (64, 64)\n", "120               video_clip_attentionv_trans_matrix:0       (64, 64)\n", "121                 video_atten_output_ln_layer/beta:0        (64, 1)\n", "122                video_atten_output_ln_layer/gamma:0        (64, 1)\n", "123                      pinnerformer_moe_gate_net/w:0        (64, 2)\n", "124                      pinnerformer_moe_gate_net/b:0         (2, 1)\n", "125                     pinnerformer_moe_0_expert0/w:0      (64, 128)\n", "126                     pinnerformer_moe_0_expert0/b:0       (128, 1)\n", "127                     pinnerformer_moe_0_expert1/w:0      (128, 32)\n", "128                     pinnerformer_moe_0_expert1/b:0        (32, 1)\n", "129                     pinnerformer_moe_1_expert0/w:0      (64, 128)\n", "130                     pinnerformer_moe_1_expert0/b:0       (128, 1)\n", "131                     pinnerformer_moe_1_expert1/w:0      (128, 32)\n", "132                     pinnerformer_moe_1_expert1/b:0        (32, 1)\n", "133                     videoclip_emb_moe_gate_net/w:0        (64, 2)\n", "134                     videoclip_emb_moe_gate_net/b:0         (2, 1)\n", "135                    videoclip_emb_moe_0_expert0/w:0      (64, 128)\n", "136                    videoclip_emb_moe_0_expert0/b:0       (128, 1)\n", "137                    videoclip_emb_moe_0_expert1/w:0      (128, 32)\n", "138                    videoclip_emb_moe_0_expert1/b:0        (32, 1)\n", "139                    videoclip_emb_moe_1_expert0/w:0      (64, 128)\n", "140                    videoclip_emb_moe_1_expert0/b:0       (128, 1)\n", "141                    videoclip_emb_moe_1_expert1/w:0      (128, 32)\n", "142                    videoclip_emb_moe_1_expert1/b:0        (32, 1)\n", "143                         cot_dense_moe_gate_net/w:0       (384, 2)\n", "144                         cot_dense_moe_gate_net/b:0         (2, 1)\n", "145                        cot_dense_moe_0_expert0/w:0     (384, 128)\n", "146                        cot_dense_moe_0_expert0/b:0       (128, 1)\n", "147                        cot_dense_moe_0_expert1/w:0      (128, 32)\n", "148                        cot_dense_moe_0_expert1/b:0        (32, 1)\n", "149                        cot_dense_moe_1_expert0/w:0     (384, 128)\n", "150                        cot_dense_moe_1_expert0/b:0       (128, 1)\n", "151                        cot_dense_moe_1_expert1/w:0      (128, 32)\n", "152                        cot_dense_moe_1_expert1/b:0        (32, 1)\n", "153                         sft_dense_moe_gate_net/w:0       (512, 2)\n", "154                         sft_dense_moe_gate_net/b:0         (2, 1)\n", "155                        sft_dense_moe_0_expert0/w:0     (512, 128)\n", "156                        sft_dense_moe_0_expert0/b:0       (128, 1)\n", "157                        sft_dense_moe_0_expert1/w:0      (128, 32)\n", "158                        sft_dense_moe_0_expert1/b:0        (32, 1)\n", "159                        sft_dense_moe_1_expert0/w:0     (512, 128)\n", "160                        sft_dense_moe_1_expert0/b:0       (128, 1)\n", "161                        sft_dense_moe_1_expert1/w:0      (128, 32)\n", "162                        sft_dense_moe_1_expert1/b:0        (32, 1)\n", "163                            pinnerformer_input0/w:0     (128, 512)\n", "164                            pinnerformer_input0/b:0       (512, 1)\n", "165                            pinnerformer_input1/w:0     (512, 128)\n", "166                            pinnerformer_input1/b:0       (128, 1)\n", "167                            pinnerformer_input2/w:0       (128, 2)\n", "168                            pinnerformer_input2/b:0         (2, 1)\n", "169                               llm_other_input0/w:0    (2005, 512)\n", "170                               llm_other_input0/b:0       (512, 1)\n", "171                               llm_other_input1/w:0     (512, 128)\n", "172                               llm_other_input1/b:0       (128, 1)\n", "173                               llm_other_input2/w:0       (128, 2)\n", "174                               llm_other_input2/b:0         (2, 1)\n", "175                            llm_keywords_input0/w:0      (128, 64)\n", "176                            llm_keywords_input0/b:0        (64, 1)\n", "177                            llm_keywords_input1/w:0        (64, 2)\n", "178                            llm_keywords_input1/b:0         (2, 1)\n", "179                           shark_refine_layer_0/w:0     (128, 512)\n", "180                           shark_refine_layer_0/b:0       (512, 1)\n", "181              shark_refine_layer_0/LayerNorm/beta:0       (512, 1)\n", "182             shark_refine_layer_0/LayerNorm/gamma:0       (512, 1)\n", "183                           shark_refine_layer_1/w:0     (512, 256)\n", "184                           shark_refine_layer_1/b:0       (256, 1)\n", "185                             shark_head_layer_0/w:0     (256, 128)\n", "186                             shark_head_layer_0/b:0       (128, 1)\n", "187                shark_head_layer_0/LayerNorm/beta:0       (128, 1)\n", "188               shark_head_layer_0/LayerNorm/gamma:0       (128, 1)\n", "189                             shark_head_layer_1/w:0       (128, 2)\n", "190                             shark_head_layer_1/b:0         (2, 1)\n", "191                           long_term_pids_fake0/w:0      (800, 64)\n", "192                           long_term_pids_fake0/b:0        (64, 1)\n", "193                           long_term_pids_fake1/w:0       (64, 32)\n", "194                           long_term_pids_fake1/b:0        (32, 1)\n", "195                                 LayerNorm_1/beta:0        (32, 1)\n", "196                                LayerNorm_1/gamma:0        (32, 1)\n", "197                                 LayerNorm_2/beta:0        (32, 1)\n", "198                                LayerNorm_2/gamma:0        (32, 1)\n", "199                           long_term_aids_fake0/w:0      (800, 64)\n", "200                           long_term_aids_fake0/b:0        (64, 1)\n", "201                           long_term_aids_fake1/w:0       (64, 32)\n", "202                           long_term_aids_fake1/b:0        (32, 1)\n", "203                                 LayerNorm_3/beta:0        (32, 1)\n", "204                                LayerNorm_3/gamma:0        (32, 1)\n", "205                                 LayerNorm_4/beta:0        (32, 1)\n", "206                                LayerNorm_4/gamma:0        (32, 1)\n", "207                           long_term_play_fake0/w:0      (400, 64)\n", "208                           long_term_play_fake0/b:0        (64, 1)\n", "209                           long_term_play_fake1/w:0       (64, 32)\n", "210                           long_term_play_fake1/b:0        (32, 1)\n", "211                                 LayerNorm_5/beta:0        (32, 1)\n", "212                                LayerNorm_5/gamma:0        (32, 1)\n", "213                                 LayerNorm_6/beta:0        (32, 1)\n", "214                                LayerNorm_6/gamma:0        (32, 1)\n", "215                           long_term_tags_fake0/w:0      (400, 64)\n", "216                           long_term_tags_fake0/b:0        (64, 1)\n", "217                           long_term_tags_fake1/w:0       (64, 32)\n", "218                           long_term_tags_fake1/b:0        (32, 1)\n", "219                                 LayerNorm_7/beta:0        (32, 1)\n", "220                                LayerNorm_7/gamma:0        (32, 1)\n", "221                                 LayerNorm_8/beta:0        (32, 1)\n", "222                                LayerNorm_8/gamma:0        (32, 1)\n", "223                          long_term_times_fake0/w:0      (400, 64)\n", "224                          long_term_times_fake0/b:0        (64, 1)\n", "225                          long_term_times_fake1/w:0       (64, 32)\n", "226                          long_term_times_fake1/b:0        (32, 1)\n", "227                                 LayerNorm_9/beta:0        (32, 1)\n", "228                                LayerNorm_9/gamma:0        (32, 1)\n", "229                                LayerNorm_10/beta:0        (32, 1)\n", "230                               LayerNorm_10/gamma:0        (32, 1)\n", "231               colossus_rs_count_index_list_cl0/w:0      (200, 64)\n", "232               colossus_rs_count_index_list_cl0/b:0        (64, 1)\n", "233               colossus_rs_count_index_list_cl1/w:0       (64, 32)\n", "234               colossus_rs_count_index_list_cl1/b:0        (32, 1)\n", "235                                LayerNorm_11/beta:0        (32, 1)\n", "236                               LayerNorm_11/gamma:0        (32, 1)\n", "237                                LayerNorm_12/beta:0        (32, 1)\n", "238                               LayerNorm_12/gamma:0        (32, 1)\n", "239                   colossus_rs_item_id_list_cl0/w:0      (800, 64)\n", "240                   colossus_rs_item_id_list_cl0/b:0        (64, 1)\n", "241                   colossus_rs_item_id_list_cl1/w:0       (64, 32)\n", "242                   colossus_rs_item_id_list_cl1/b:0        (32, 1)\n", "243                                LayerNorm_13/beta:0        (32, 1)\n", "244                               LayerNorm_13/gamma:0        (32, 1)\n", "245                                LayerNorm_14/beta:0        (32, 1)\n", "246                               LayerNorm_14/gamma:0        (32, 1)\n", "247                     colossus_rs_lagV1_list_cl0/w:0      (200, 64)\n", "248                     colossus_rs_lagV1_list_cl0/b:0        (64, 1)\n", "249                     colossus_rs_lagV1_list_cl1/w:0       (64, 32)\n", "250                     colossus_rs_lagV1_list_cl1/b:0        (32, 1)\n", "251                                LayerNorm_15/beta:0        (32, 1)\n", "252                               LayerNorm_15/gamma:0        (32, 1)\n", "253                                LayerNorm_16/beta:0        (32, 1)\n", "254                               LayerNorm_16/gamma:0        (32, 1)\n", "255                        colossus_rs_lagV2_list0/w:0      (200, 64)\n", "256                        colossus_rs_lagV2_list0/b:0        (64, 1)\n", "257                        colossus_rs_lagV2_list1/w:0       (64, 32)\n", "258                        colossus_rs_lagV2_list1/b:0        (32, 1)\n", "259                                LayerNorm_17/beta:0        (32, 1)\n", "260                               LayerNorm_17/gamma:0        (32, 1)\n", "261                                LayerNorm_18/beta:0        (32, 1)\n", "262                               LayerNorm_18/gamma:0        (32, 1)\n", "263               colossus_rs_pagecode_id_list_cl0/w:0      (200, 64)\n", "264               colossus_rs_pagecode_id_list_cl0/b:0        (64, 1)\n", "265               colossus_rs_pagecode_id_list_cl1/w:0       (64, 32)\n", "266               colossus_rs_pagecode_id_list_cl1/b:0        (32, 1)\n", "267                                LayerNorm_19/beta:0        (32, 1)\n", "268                               LayerNorm_19/gamma:0        (32, 1)\n", "269                                LayerNorm_20/beta:0        (32, 1)\n", "270                               LayerNorm_20/gamma:0        (32, 1)\n", "271            colossus_rs_uniform_spu_id_list_cl0/w:0      (800, 64)\n", "272            colossus_rs_uniform_spu_id_list_cl0/b:0        (64, 1)\n", "273            colossus_rs_uniform_spu_id_list_cl1/w:0       (64, 32)\n", "274            colossus_rs_uniform_spu_id_list_cl1/b:0        (32, 1)\n", "275                                LayerNorm_21/beta:0        (32, 1)\n", "276                               LayerNorm_21/gamma:0        (32, 1)\n", "277                                LayerNorm_22/beta:0        (32, 1)\n", "278                               LayerNorm_22/gamma:0        (32, 1)\n", "279                 SENET_layer/excitation_layer_1/w:0      (255, 45)\n", "280                 SENET_layer/excitation_layer_1/b:0        (45, 1)\n", "281    SENET_layer/excitation_layer_1/LayerNorm/beta:0        (45, 1)\n", "282   SENET_layer/excitation_layer_1/LayerNorm/gamma:0        (45, 1)\n", "283                 SENET_layer/excitation_layer_2/w:0      (45, 255)\n", "284                 SENET_layer/excitation_layer_2/b:0       (255, 1)\n", "285                                candidate_layer/w:0    (4080, 256)\n", "286                                candidate_layer/b:0       (256, 1)\n", "287            candidate_layer/rc_seq_q_trans_matrix:0      (256, 32)\n", "288            candidate_layer/rc_seq_k_trans_matrix:0       (96, 32)\n", "289            candidate_layer/rc_seq_v_trans_matrix:0       (96, 32)\n", "290                                attention_layer/w:0   (4080, 1024)\n", "291                                attention_layer/b:0      (1024, 1)\n", "292         attention_layer/long_term_q_trans_matrix:0     (1024, 32)\n", "293         attention_layer/long_term_k_trans_matrix:0       (56, 32)\n", "294         attention_layer/long_term_v_trans_matrix:0       (56, 32)\n", "295    attention_layer/long_recent_attq_trans_matrix:0       (48, 32)\n", "296    attention_layer/long_recent_attk_trans_matrix:0       (56, 32)\n", "297    attention_layer/long_recent_attv_trans_matrix:0       (56, 32)\n", "298      attention_layer/long_seq_suffix_pooling_0/w:0    (1080, 256)\n", "299      attention_layer/long_seq_suffix_pooling_0/b:0       (256, 1)\n", "300  attention_layer/long_seq_suffix_pooling_0/Laye...       (256, 1)\n", "301  attention_layer/long_seq_suffix_pooling_0/Laye...       (256, 1)\n", "302      attention_layer/long_seq_suffix_pooling_1/w:0      (256, 32)\n", "303      attention_layer/long_seq_suffix_pooling_1/b:0        (32, 1)\n", "304  attention_layer/good_show_user_embedding_pooli...     (336, 256)\n", "305  attention_layer/good_show_user_embedding_pooli...       (256, 1)\n", "306  attention_layer/good_show_user_embedding_pooli...       (256, 1)\n", "307  attention_layer/good_show_user_embedding_pooli...       (256, 1)\n", "308  attention_layer/good_show_user_embedding_pooli...      (256, 32)\n", "309  attention_layer/good_show_user_embedding_pooli...        (32, 1)\n", "310  attention_layer/good_show_user_embedding_atten...      (288, 32)\n", "311  attention_layer/good_show_user_embedding_atten...       (48, 32)\n", "312  attention_layer/good_show_user_embedding_atten...       (48, 32)\n", "313          good_click_cate2cate_real_price_fake0/w:0      (400, 64)\n", "314          good_click_cate2cate_real_price_fake0/b:0        (64, 1)\n", "315          good_click_cate2cate_real_price_fake1/w:0       (64, 32)\n", "316          good_click_cate2cate_real_price_fake1/b:0        (32, 1)\n", "317                                LayerNorm_23/beta:0        (32, 1)\n", "318                               LayerNorm_23/gamma:0        (32, 1)\n", "319                                LayerNorm_24/beta:0        (32, 1)\n", "320                               LayerNorm_24/gamma:0        (32, 1)\n", "321            good_click_cate2cate_category_fake0/w:0      (400, 64)\n", "322            good_click_cate2cate_category_fake0/b:0        (64, 1)\n", "323            good_click_cate2cate_category_fake1/w:0       (64, 32)\n", "324            good_click_cate2cate_category_fake1/b:0        (32, 1)\n", "325                                LayerNorm_25/beta:0        (32, 1)\n", "326                               LayerNorm_25/gamma:0        (32, 1)\n", "327                                LayerNorm_26/beta:0        (32, 1)\n", "328                               LayerNorm_26/gamma:0        (32, 1)\n", "329          good_click_cate2cate_carry_type_fake0/w:0      (200, 64)\n", "330          good_click_cate2cate_carry_type_fake0/b:0        (64, 1)\n", "331          good_click_cate2cate_carry_type_fake1/w:0       (64, 32)\n", "332          good_click_cate2cate_carry_type_fake1/b:0        (32, 1)\n", "333                                LayerNorm_27/beta:0        (32, 1)\n", "334                               LayerNorm_27/gamma:0        (32, 1)\n", "335                                LayerNorm_28/beta:0        (32, 1)\n", "336                               LayerNorm_28/gamma:0        (32, 1)\n", "337                 good_click_cate2cate_lag_fake0/w:0      (400, 64)\n", "338                 good_click_cate2cate_lag_fake0/b:0        (64, 1)\n", "339                 good_click_cate2cate_lag_fake1/w:0       (64, 32)\n", "340                 good_click_cate2cate_lag_fake1/b:0        (32, 1)\n", "341                                LayerNorm_29/beta:0        (32, 1)\n", "342                               LayerNorm_29/gamma:0        (32, 1)\n", "343                                LayerNorm_30/beta:0        (32, 1)\n", "344                               LayerNorm_30/gamma:0        (32, 1)\n", "345             good_click_cate2cate_item_id_fake0/w:0      (800, 64)\n", "346             good_click_cate2cate_item_id_fake0/b:0        (64, 1)\n", "347             good_click_cate2cate_item_id_fake1/w:0       (64, 32)\n", "348             good_click_cate2cate_item_id_fake1/b:0        (32, 1)\n", "349                                LayerNorm_31/beta:0        (32, 1)\n", "350                               LayerNorm_31/gamma:0        (32, 1)\n", "351                                LayerNorm_32/beta:0        (32, 1)\n", "352                               LayerNorm_32/gamma:0        (32, 1)\n", "353           good_click_cate2cate_seller_id_fake0/w:0      (800, 64)\n", "354           good_click_cate2cate_seller_id_fake0/b:0        (64, 1)\n", "355           good_click_cate2cate_seller_id_fake1/w:0       (64, 32)\n", "356           good_click_cate2cate_seller_id_fake1/b:0        (32, 1)\n", "357                                LayerNorm_33/beta:0        (32, 1)\n", "358                               LayerNorm_33/gamma:0        (32, 1)\n", "359                                LayerNorm_34/beta:0        (32, 1)\n", "360                               LayerNorm_34/gamma:0        (32, 1)\n", "361               good_click_cate2cate_layer_new_0/w:0      (60, 128)\n", "362               good_click_cate2cate_layer_new_0/b:0       (128, 1)\n", "363  good_click_cate2cate_layer_new_0/LayerNorm/beta:0       (128, 1)\n", "364  good_click_cate2cate_layer_new_0/LayerNorm/gam...       (128, 1)\n", "365               good_click_cate2cate_layer_new_1/w:0      (128, 64)\n", "366               good_click_cate2cate_layer_new_1/b:0        (64, 1)\n", "367  good_click_cate2cate_layer_new_1/LayerNorm/beta:0        (64, 1)\n", "368  good_click_cate2cate_layer_new_1/LayerNorm/gam...        (64, 1)\n", "369               good_click_cate2cate_layer_new_2/w:0       (64, 32)\n", "370               good_click_cate2cate_layer_new_2/b:0        (32, 1)\n", "371                      gc_recent_attq_trans_matrix:0       (48, 32)\n", "372                      gc_recent_attk_trans_matrix:0       (60, 32)\n", "373                      gc_recent_attv_trans_matrix:0       (60, 32)\n", "374  gc_self_attention_layer/gc_self_attnq_trans_ma...       (60, 32)\n", "375  gc_self_attention_layer/gc_self_attnk_trans_ma...       (60, 32)\n", "376  gc_self_attention_layer/gc_self_attnv_trans_ma...       (60, 32)\n", "377                        gc_self_attention_layer/w:0     (1600, 64)\n", "378                        gc_self_attention_layer/b:0        (64, 1)\n", "379           gc_self_attention_layer/LayerNorm/beta:0        (64, 1)\n", "380          gc_self_attention_layer/LayerNorm/gamma:0        (64, 1)\n", "381  long_self_attention_layer/long_self_attnq_tran...       (56, 32)\n", "382  long_self_attention_layer/long_self_attnk_tran...       (56, 32)\n", "383  long_self_attention_layer/long_self_attnv_tran...       (56, 32)\n", "384                      long_self_attention_layer/w:0     (3200, 64)\n", "385                      long_self_attention_layer/b:0        (64, 1)\n", "386         long_self_attention_layer/LayerNorm/beta:0        (64, 1)\n", "387        long_self_attention_layer/LayerNorm/gamma:0        (64, 1)\n", "388                            Cross_layer_sub/w_sub:0      (4, 5820)\n", "389                QUEUE_SOFT/share_bottom_layer_0/w:0   (8909, 1024)\n", "390                QUEUE_SOFT/share_bottom_layer_0/b:0      (1024, 1)\n", "391   QUEUE_SOFT/share_bottom_layer_0/LayerNorm/beta:0      (1024, 1)\n", "392  QUEUE_SOFT/share_bottom_layer_0/LayerNorm/gamma:0      (1024, 1)\n", "393                         QUEUE_SOFT/pxr_layer_1/w:0    (1024, 256)\n", "394                         QUEUE_SOFT/pxr_layer_1/b:0       (256, 1)\n", "395            QUEUE_SOFT/pxr_layer_1/LayerNorm/beta:0       (256, 1)\n", "396           QUEUE_SOFT/pxr_layer_1/LayerNorm/gamma:0       (256, 1)\n", "397                         QUEUE_SOFT/pxr_layer_2/w:0     (256, 128)\n", "398                         QUEUE_SOFT/pxr_layer_2/b:0       (128, 1)\n", "399            QUEUE_SOFT/pxr_layer_2/LayerNorm/beta:0       (128, 1)\n", "400           QUEUE_SOFT/pxr_layer_2/LayerNorm/gamma:0       (128, 1)\n", "401                         QUEUE_SOFT/pxr_layer_3/w:0       (128, 2)\n", "402                         QUEUE_SOFT/pxr_layer_3/b:0         (2, 1)\n", "403                         QUEUE_SOFT/ped_layer_1/w:0    (1024, 256)\n", "404                         QUEUE_SOFT/ped_layer_1/b:0       (256, 1)\n", "405            QUEUE_SOFT/ped_layer_1/LayerNorm/beta:0       (256, 1)\n", "406           QUEUE_SOFT/ped_layer_1/LayerNorm/gamma:0       (256, 1)\n", "407                         QUEUE_SOFT/ped_layer_2/w:0     (256, 128)\n", "408                         QUEUE_SOFT/ped_layer_2/b:0       (128, 1)\n", "409            QUEUE_SOFT/ped_layer_2/LayerNorm/beta:0       (128, 1)\n", "410           QUEUE_SOFT/ped_layer_2/LayerNorm/gamma:0       (128, 1)\n", "411                         QUEUE_SOFT/ped_layer_3/w:0       (128, 2)\n", "412                         QUEUE_SOFT/ped_layer_3/b:0         (2, 1)\n", "413                   QUEUE_SOFT/ctr_upper_layer_1/w:0    (1024, 256)\n", "414                   QUEUE_SOFT/ctr_upper_layer_1/b:0       (256, 1)\n", "415      QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "416     QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "417                   QUEUE_SOFT/ctr_upper_layer_2/w:0     (256, 128)\n", "418                   QUEUE_SOFT/ctr_upper_layer_2/b:0       (128, 1)\n", "419      QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "420     QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "421                   QUEUE_SOFT/ctr_upper_layer_3/w:0       (128, 2)\n", "422                   QUEUE_SOFT/ctr_upper_layer_3/b:0         (2, 1)\n", "423                       QUEUE_SOFT/upper_layer_1/w:0    (1024, 256)\n", "424                       QUEUE_SOFT/upper_layer_1/b:0       (256, 1)\n", "425          QUEUE_SOFT/upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "426         QUEUE_SOFT/upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "427                       QUEUE_SOFT/upper_layer_2/w:0     (256, 128)\n", "428                       QUEUE_SOFT/upper_layer_2/b:0       (128, 1)\n", "429          QUEUE_SOFT/upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "430         QUEUE_SOFT/upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "431                       QUEUE_SOFT/upper_layer_3/w:0       (128, 2)\n", "432                       QUEUE_SOFT/upper_layer_3/b:0         (2, 1)\n", "433                               QUEUE_SOFT/w_ctcvr:0         (1, 1)\n", "434                                    dcn_layer_0/u:0    (16654, 64)\n", "435                                    dcn_layer_0/v:0    (64, 16654)\n", "436                                    dcn_layer_0/b:0     (16654, 1)\n", "437                       dcn_layer_0/LayerNorm/beta:0     (16654, 1)\n", "438                      dcn_layer_0/LayerNorm/gamma:0     (16654, 1)\n", "439                                    dcn_layer_1/u:0    (16654, 64)\n", "440                                    dcn_layer_1/v:0    (64, 16654)\n", "441                                    dcn_layer_1/b:0     (16654, 1)\n", "442                       dcn_layer_1/LayerNorm/beta:0     (16654, 1)\n", "443                      dcn_layer_1/LayerNorm/gamma:0     (16654, 1)\n", "444                       share_bottom_layer_new_0/w:0  (16654, 1024)\n", "445                       share_bottom_layer_new_0/b:0      (1024, 1)\n", "446          share_bottom_layer_new_0/LayerNorm/beta:0      (1024, 1)\n", "447         share_bottom_layer_new_0/LayerNorm/gamma:0      (1024, 1)\n", "448                             generalize_layer_0/w:0     (240, 128)\n", "449                             generalize_layer_0/b:0       (128, 1)\n", "450                generalize_layer_0/LayerNorm/beta:0       (128, 1)\n", "451               generalize_layer_0/LayerNorm/gamma:0       (128, 1)\n", "452                  reco_share_bottom_layer_new_0/w:0   (5400, 1024)\n", "453                  reco_share_bottom_layer_new_0/b:0      (1024, 1)\n", "454     reco_share_bottom_layer_new_0/LayerNorm/beta:0      (1024, 1)\n", "455    reco_share_bottom_layer_new_0/LayerNorm/gamma:0      (1024, 1)\n", "456                             generalize_layer_1/w:0      (128, 64)\n", "457                             generalize_layer_1/b:0        (64, 1)\n", "458                generalize_layer_1/LayerNorm/beta:0        (64, 1)\n", "459               generalize_layer_1/LayerNorm/gamma:0        (64, 1)\n", "460                             generalize_layer_2/w:0       (64, 32)\n", "461                             generalize_layer_2/b:0        (32, 1)\n", "462                generalize_layer_2/LayerNorm/beta:0        (32, 1)\n", "463               generalize_layer_2/LayerNorm/gamma:0        (32, 1)\n", "464                             generalize_layer_3/w:0        (32, 2)\n", "465                             generalize_layer_3/b:0         (2, 1)\n", "466                                    pxr_layer_1/w:0    (1024, 256)\n", "467                                    pxr_layer_1/b:0       (256, 1)\n", "468                       pxr_layer_1/LayerNorm/beta:0       (256, 1)\n", "469                      pxr_layer_1/LayerNorm/gamma:0       (256, 1)\n", "470                                    pxr_layer_2/w:0     (256, 128)\n", "471                                    pxr_layer_2/b:0       (128, 1)\n", "472                       pxr_layer_2/LayerNorm/beta:0       (128, 1)\n", "473                      pxr_layer_2/LayerNorm/gamma:0       (128, 1)\n", "474                                    pxr_layer_3/w:0       (128, 2)\n", "475                                    pxr_layer_3/b:0         (2, 1)\n", "476                                    ped_layer_1/w:0    (1024, 256)\n", "477                                    ped_layer_1/b:0       (256, 1)\n", "478                       ped_layer_1/LayerNorm/beta:0       (256, 1)\n", "479                      ped_layer_1/LayerNorm/gamma:0       (256, 1)\n", "480                                    ped_layer_2/w:0     (256, 128)\n", "481                                    ped_layer_2/b:0       (128, 1)\n", "482                       ped_layer_2/LayerNorm/beta:0       (128, 1)\n", "483                      ped_layer_2/LayerNorm/gamma:0       (128, 1)\n", "484                                    ped_layer_3/w:0       (128, 2)\n", "485                                    ped_layer_3/b:0         (2, 1)\n", "486                              ctr_upper_layer_1/w:0    (1024, 256)\n", "487                              ctr_upper_layer_1/b:0       (256, 1)\n", "488                 ctr_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "489                ctr_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "490                              ctr_upper_layer_2/w:0     (256, 128)\n", "491                              ctr_upper_layer_2/b:0       (128, 1)\n", "492                 ctr_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "493                ctr_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "494                              ctr_upper_layer_3/w:0       (128, 2)\n", "495                              ctr_upper_layer_3/b:0         (2, 1)\n", "496                          assist1_upper_layer_1/w:0    (1024, 256)\n", "497                          assist1_upper_layer_1/b:0       (256, 1)\n", "498             assist1_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "499            assist1_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "500                          assist1_upper_layer_2/w:0     (256, 128)\n", "501                          assist1_upper_layer_2/b:0       (128, 1)\n", "502             assist1_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "503            assist1_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "504                          assist1_upper_layer_3/w:0       (128, 2)\n", "505                          assist1_upper_layer_3/b:0         (2, 1)\n", "506                          assist2_upper_layer_1/w:0    (1024, 256)\n", "507                          assist2_upper_layer_1/b:0       (256, 1)\n", "508             assist2_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "509            assist2_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "510                          assist2_upper_layer_2/w:0     (256, 128)\n", "511                          assist2_upper_layer_2/b:0       (128, 1)\n", "512             assist2_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "513            assist2_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "514                          assist2_upper_layer_3/w:0       (128, 2)\n", "515                          assist2_upper_layer_3/b:0         (2, 1)\n", "516                                    cid_layer_1/w:0    (1024, 256)\n", "517                                    cid_layer_1/b:0       (256, 1)\n", "518                       cid_layer_1/LayerNorm/beta:0       (256, 1)\n", "519                      cid_layer_1/LayerNorm/gamma:0       (256, 1)\n", "520                                    cid_layer_2/w:0     (256, 128)\n", "521                                    cid_layer_2/b:0       (128, 1)\n", "522                       cid_layer_2/LayerNorm/beta:0       (128, 1)\n", "523                      cid_layer_2/LayerNorm/gamma:0       (128, 1)\n", "524                                    cid_layer_3/w:0       (128, 2)\n", "525                                    cid_layer_3/b:0         (2, 1)\n", "526                                  upper_layer_1/w:0    (1153, 256)\n", "527                                  upper_layer_1/b:0       (256, 1)\n", "528                    upper_layer_1/layer_norm/beta:0       (256, 1)\n", "529                   upper_layer_1/layer_norm/gamma:0       (256, 1)\n", "530                                  upper_layer_2/w:0     (256, 128)\n", "531                                  upper_layer_2/b:0       (128, 1)\n", "532                    upper_layer_2/layer_norm/beta:0       (128, 1)\n", "533                   upper_layer_2/layer_norm/gamma:0       (128, 1)\n", "534                                  upper_layer_3/w:0       (128, 2)\n", "535                                  upper_layer_3/b:0         (2, 1)\n", "536                           rocket_upper_layer_2/w:0     (864, 128)\n", "537                           rocket_upper_layer_2/b:0       (128, 1)\n", "538              rocket_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "539             rocket_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "540                           rocket_upper_layer_3/w:0       (128, 2)\n", "541                           rocket_upper_layer_3/b:0         (2, 1)\n", "542                                 global_layer_1/w:0    (1024, 256)\n", "543                                 global_layer_1/b:0       (256, 1)\n", "544                    global_layer_1/LayerNorm/beta:0       (256, 1)\n", "545                   global_layer_1/LayerNorm/gamma:0       (256, 1)\n", "546                                 global_layer_2/w:0     (256, 128)\n", "547                                 global_layer_2/b:0       (128, 1)\n", "548                    global_layer_2/LayerNorm/beta:0       (128, 1)\n", "549                   global_layer_2/LayerNorm/gamma:0       (128, 1)\n", "550                                 global_layer_3/w:0       (128, 2)\n", "551                                 global_layer_3/b:0         (2, 1)\n", "552                                 direct_layer_1/w:0    (1024, 256)\n", "553                                 direct_layer_1/b:0       (256, 1)\n", "554                    direct_layer_1/LayerNorm/beta:0       (256, 1)\n", "555                   direct_layer_1/LayerNorm/gamma:0       (256, 1)\n", "556                                 direct_layer_2/w:0     (256, 128)\n", "557                                 direct_layer_2/b:0       (128, 1)\n", "558                    direct_layer_2/LayerNorm/beta:0       (128, 1)\n", "559                   direct_layer_2/LayerNorm/gamma:0       (128, 1)\n", "560                                 direct_layer_3/w:0       (128, 2)\n", "561                                 direct_layer_3/b:0         (2, 1)\n", "562                      pic_calibration/cali_lambda:0         (1, 1)\n", "563               Pic_calibration/cali_weight_lambda:0         (7, 1)\n", "564                                    gmv_layer_0/w:0    (1024, 256)\n", "565                                    gmv_layer_0/b:0       (256, 1)\n", "566                       gmv_layer_0/LayerNorm/beta:0       (256, 1)\n", "567                      gmv_layer_0/LayerNorm/gamma:0       (256, 1)\n", "568                                    gmv_layer_1/w:0     (256, 128)\n", "569                                    gmv_layer_1/b:0       (128, 1)\n", "570                       gmv_layer_1/LayerNorm/beta:0       (128, 1)\n", "571                      gmv_layer_1/LayerNorm/gamma:0       (128, 1)\n", "572                                    gmv_layer_2/w:0       (128, 2)\n", "573                                    gmv_layer_2/b:0         (2, 1)\n", "574                             reco_upper_layer_1/w:0    (1024, 256)\n", "575                             reco_upper_layer_1/b:0       (256, 1)\n", "576                reco_upper_layer_1/LayerNorm/beta:0       (256, 1)\n", "577               reco_upper_layer_1/LayerNorm/gamma:0       (256, 1)\n", "578                             reco_upper_layer_2/w:0     (256, 128)\n", "579                             reco_upper_layer_2/b:0       (128, 1)\n", "580                reco_upper_layer_2/LayerNorm/beta:0       (128, 1)\n", "581               reco_upper_layer_2/LayerNorm/gamma:0       (128, 1)\n", "582                             reco_upper_layer_3/w:0       (128, 2)\n", "583                             reco_upper_layer_3/b:0         (2, 1)\n", "584                                          w_ctcvr:0         (1, 1)\n", "585                                            w_ctr:0         (1, 1)\n", "586                                            w_p3s:0         (1, 1)\n", "587                                            w_ped:0         (1, 1)\n", "588                                     w_reco_ctcvr:0         (1, 1)\n", "589                                     w_global_pay:0         (1, 1)\n", "590                                     w_direct_pay:0         (1, 1)\n", "591                                           w_ast2:0         (1, 1)\n", "592                                       w_ue_ctcvr:0         (1, 1)\n", "Extra: \n", "                                                  name        shape  opt_type\n", "0                                       u_p_input0/w:0      (1024,)  AdagradW\n", "1                                       u_p_input0/b:0        (32,)  AdagradW\n", "2                                       u_p_input1/w:0        (64,)  AdagradW\n", "3                                       u_p_input1/b:0         (2,)  AdagradW\n", "4                                       u_i_input0/w:0      (1024,)  AdagradW\n", "5                                       u_i_input0/b:0        (32,)  AdagradW\n", "6                                       u_i_input1/w:0        (64,)  AdagradW\n", "7                                       u_i_input1/b:0         (2,)  AdagradW\n", "8                                 context_jrc_loss/w:0        (32,)  AdagradW\n", "9                                 context_jrc_loss/b:0         (1,)  AdagradW\n", "10                                    LayerNorm/beta:0     (11057,)  AdagradW\n", "11                                   LayerNorm/gamma:0     (11057,)  AdagradW\n", "12                             generalize_layer/beta:0       (240,)  AdagradW\n", "13                            generalize_layer/gamma:0       (240,)  AdagradW\n", "14                                    akg_layer/beta:0       (160,)  AdagradW\n", "15                                   akg_layer/gamma:0       (160,)  AdagradW\n", "16                                      new_fea/beta:0       (272,)  AdagradW\n", "17                                     new_fea/gamma:0       (272,)  AdagradW\n", "18                            search_feat_layer/beta:0       (128,)  AdagradW\n", "19                           search_feat_layer/gamma:0       (128,)  AdagradW\n", "20                             goods_feat_layer/beta:0       (288,)  AdagradW\n", "21                            goods_feat_layer/gamma:0       (288,)  AdagradW\n", "22                          item_add_feas_layer/beta:0       (320,)  AdagradW\n", "23                         item_add_feas_layer/gamma:0       (320,)  AdagradW\n", "24                              shark_emb_layer/beta:0       (128,)  AdagradW\n", "25                             shark_emb_layer/gamma:0       (128,)  AdagradW\n", "26                      llm_keywords_feas_layer/beta:0       (128,)  AdagradW\n", "27                     llm_keywords_feas_layer/gamma:0       (128,)  AdagradW\n", "28                           cid_add_feas_layer/beta:0       (320,)  AdagradW\n", "29                          cid_add_feas_layer/gamma:0       (320,)  AdagradW\n", "30                            add_new_fea_layer/beta:0       (280,)  AdagradW\n", "31                           add_new_fea_layer/gamma:0       (280,)  AdagradW\n", "32                            add_cpg_fea_layer/beta:0       (208,)  AdagradW\n", "33                           add_cpg_fea_layer/gamma:0       (208,)  AdagradW\n", "34                            add_adx_fea_layer/beta:0       (128,)  AdagradW\n", "35                           add_adx_fea_layer/gamma:0       (128,)  AdagradW\n", "36                      pinnerformer_user_layer/beta:0        (64,)  AdagradW\n", "37                     pinnerformer_user_layer/gamma:0        (64,)  AdagradW\n", "38                      pinnerformer_item_layer/beta:0        (64,)  AdagradW\n", "39                     pinnerformer_item_layer/gamma:0        (64,)  AdagradW\n", "40                            add_rta_fea_layer/beta:0       (176,)  AdagradW\n", "41                           add_rta_fea_layer/gamma:0       (176,)  AdagradW\n", "42                               reco_dnn_input/beta:0      (4888,)  AdagradW\n", "43                              reco_dnn_input/gamma:0      (4888,)  AdagradW\n", "44                              utype_fea_layer/beta:0        (16,)  AdagradW\n", "45                             utype_fea_layer/gamma:0        (16,)  AdagradW\n", "46                             cot_sparse_layer/beta:0       (336,)  AdagradW\n", "47                            cot_sparse_layer/gamma:0       (336,)  AdagradW\n", "48                              cot_dense_layer/beta:0       (384,)  AdagradW\n", "49                             cot_dense_layer/gamma:0       (384,)  AdagradW\n", "50                                 entity_layer/beta:0       (128,)  AdagradW\n", "51                                entity_layer/gamma:0       (128,)  AdagradW\n", "52                              is_ai_tag_layer/beta:0        (16,)  AdagradW\n", "53                             is_ai_tag_layer/gamma:0        (16,)  AdagradW\n", "54                                    sft_layer/beta:0       (512,)  AdagradW\n", "55                                   sft_layer/gamma:0       (512,)  AdagradW\n", "56                               reco_sft_layer/beta:0       (512,)  AdagradW\n", "57                              reco_sft_layer/gamma:0       (512,)  AdagradW\n", "58                             multimodal_layer/beta:0       (128,)  AdagradW\n", "59                            multimodal_layer/gamma:0       (128,)  AdagradW\n", "60                                 coupon_layer/beta:0        (80,)  AdagradW\n", "61                                coupon_layer/gamma:0        (80,)  AdagradW\n", "62                           user_ecom_rq_layer/beta:0       (288,)  AdagradW\n", "63                          user_ecom_rq_layer/gamma:0       (288,)  AdagradW\n", "64                          match_0_dense_layer/beta:0         (5,)  AdagradW\n", "65                         match_0_dense_layer/gamma:0         (5,)  AdagradW\n", "66                          match_1_dense_layer/beta:0         (5,)  AdagradW\n", "67                         match_1_dense_layer/gamma:0         (5,)  AdagradW\n", "68                          match_2_dense_layer/beta:0         (5,)  AdagradW\n", "69                         match_2_dense_layer/gamma:0         (5,)  AdagradW\n", "70                          match_3_dense_layer/beta:0         (5,)  AdagradW\n", "71                         match_3_dense_layer/gamma:0         (5,)  AdagradW\n", "72                          match_4_dense_layer/beta:0         (5,)  AdagradW\n", "73                         match_4_dense_layer/gamma:0         (5,)  AdagradW\n", "74                        ecom_multimodal_layer/beta:0       (128,)  AdagradW\n", "75                       ecom_multimodal_layer/gamma:0       (128,)  AdagradW\n", "76                             ec_detaile_layer/beta:0        (80,)  AdagradW\n", "77                            ec_detaile_layer/gamma:0        (80,)  AdagradW\n", "78                           commentstats_layer/beta:0         (5,)  AdagradW\n", "79                          commentstats_layer/gamma:0         (5,)  AdagradW\n", "80                                          dpo/beta:0        (32,)  AdagradW\n", "81                                         dpo/gamma:0        (32,)  AdagradW\n", "82                                    dense_dpo/beta:0       (128,)  AdagradW\n", "83                                   dense_dpo/gamma:0       (128,)  AdagradW\n", "84               good_click_cate2cate_dnn_layer/beta:0      (3000,)  AdagradW\n", "85              good_click_cate2cate_dnn_layer/gamma:0      (3000,)  AdagradW\n", "86                     good_show_user_embedding/beta:0      (4800,)  AdagradW\n", "87                    good_show_user_embedding/gamma:0      (4800,)  AdagradW\n", "88                                     eshop_ad/beta:0       (128,)  AdagradW\n", "89                                    eshop_ad/gamma:0       (128,)  AdagradW\n", "90                              ue_score_sparse/beta:0       (304,)  AdagradW\n", "91                             ue_score_sparse/gamma:0       (304,)  AdagradW\n", "92                      ExtractUserDenseGrpoFea/beta:0      (1024,)  AdagradW\n", "93                     ExtractUserDenseGrpoFea/gamma:0      (1024,)  AdagradW\n", "94                                  grpo_sparse/beta:0        (64,)  AdagradW\n", "95                                 grpo_sparse/gamma:0        (64,)  AdagradW\n", "96                                 user_predict/beta:0        (32,)  AdagradW\n", "97                                user_predict/gamma:0        (32,)  AdagradW\n", "98                                photo_content/beta:0       (176,)  AdagradW\n", "99                               photo_content/gamma:0       (176,)  AdagradW\n", "100            ExtractDenseUserLlmDpskValidUser/beta:0       (256,)  AdagradW\n", "101           ExtractDenseUserLlmDpskValidUser/gamma:0       (256,)  AdagradW\n", "102                   ExtractPhotoTextEmbedding/beta:0       (256,)  AdagradW\n", "103                  ExtractPhotoTextEmbedding/gamma:0       (256,)  AdagradW\n", "104                           ExtractPidHetuEmb/beta:0       (128,)  AdagradW\n", "105                          ExtractPidHetuEmb/gamma:0       (128,)  AdagradW\n", "106                         ExtractGoodsHetuEmb/beta:0       (128,)  AdagradW\n", "107                        ExtractGoodsHetuEmb/gamma:0       (128,)  AdagradW\n", "108                            ExtractPidSim512/beta:0         (1,)  AdagradW\n", "109                           ExtractPidSim512/gamma:0         (1,)  AdagradW\n", "110                            ExtractPidSim128/beta:0         (1,)  AdagradW\n", "111                           ExtractPidSim128/gamma:0         (1,)  AdagradW\n", "112                             ExtractPidSim1k/beta:0         (1,)  AdagradW\n", "113                            ExtractPidSim1k/gamma:0         (1,)  AdagradW\n", "114                 user_videoclip_emb_ln_layer/beta:0       (384,)  AdagradW\n", "115                user_videoclip_emb_ln_layer/gamma:0       (384,)  AdagradW\n", "116             ConcatKeywordVideoClip_ln_layer/beta:0        (64,)  AdagradW\n", "117            ConcatKeywordVideoClip_ln_layer/gamma:0        (64,)  AdagradW\n", "118               video_clip_attentionq_trans_matrix:0      (4096,)  AdagradW\n", "119               video_clip_attentionk_trans_matrix:0      (4096,)  AdagradW\n", "120               video_clip_attentionv_trans_matrix:0      (4096,)  AdagradW\n", "121                 video_atten_output_ln_layer/beta:0        (64,)  AdagradW\n", "122                video_atten_output_ln_layer/gamma:0        (64,)  AdagradW\n", "123                      pinnerformer_moe_gate_net/w:0       (128,)  AdagradW\n", "124                      pinnerformer_moe_gate_net/b:0         (2,)  AdagradW\n", "125                     pinnerformer_moe_0_expert0/w:0      (8192,)  AdagradW\n", "126                     pinnerformer_moe_0_expert0/b:0       (128,)  AdagradW\n", "127                     pinnerformer_moe_0_expert1/w:0      (4096,)  AdagradW\n", "128                     pinnerformer_moe_0_expert1/b:0        (32,)  AdagradW\n", "129                     pinnerformer_moe_1_expert0/w:0      (8192,)  AdagradW\n", "130                     pinnerformer_moe_1_expert0/b:0       (128,)  AdagradW\n", "131                     pinnerformer_moe_1_expert1/w:0      (4096,)  AdagradW\n", "132                     pinnerformer_moe_1_expert1/b:0        (32,)  AdagradW\n", "133                     videoclip_emb_moe_gate_net/w:0       (128,)  AdagradW\n", "134                     videoclip_emb_moe_gate_net/b:0         (2,)  AdagradW\n", "135                    videoclip_emb_moe_0_expert0/w:0      (8192,)  AdagradW\n", "136                    videoclip_emb_moe_0_expert0/b:0       (128,)  AdagradW\n", "137                    videoclip_emb_moe_0_expert1/w:0      (4096,)  AdagradW\n", "138                    videoclip_emb_moe_0_expert1/b:0        (32,)  AdagradW\n", "139                    videoclip_emb_moe_1_expert0/w:0      (8192,)  AdagradW\n", "140                    videoclip_emb_moe_1_expert0/b:0       (128,)  AdagradW\n", "141                    videoclip_emb_moe_1_expert1/w:0      (4096,)  AdagradW\n", "142                    videoclip_emb_moe_1_expert1/b:0        (32,)  AdagradW\n", "143                         cot_dense_moe_gate_net/w:0       (768,)  AdagradW\n", "144                         cot_dense_moe_gate_net/b:0         (2,)  AdagradW\n", "145                        cot_dense_moe_0_expert0/w:0     (49152,)  AdagradW\n", "146                        cot_dense_moe_0_expert0/b:0       (128,)  AdagradW\n", "147                        cot_dense_moe_0_expert1/w:0      (4096,)  AdagradW\n", "148                        cot_dense_moe_0_expert1/b:0        (32,)  AdagradW\n", "149                        cot_dense_moe_1_expert0/w:0     (49152,)  AdagradW\n", "150                        cot_dense_moe_1_expert0/b:0       (128,)  AdagradW\n", "151                        cot_dense_moe_1_expert1/w:0      (4096,)  AdagradW\n", "152                        cot_dense_moe_1_expert1/b:0        (32,)  AdagradW\n", "153                         sft_dense_moe_gate_net/w:0      (1024,)  AdagradW\n", "154                         sft_dense_moe_gate_net/b:0         (2,)  AdagradW\n", "155                        sft_dense_moe_0_expert0/w:0     (65536,)  AdagradW\n", "156                        sft_dense_moe_0_expert0/b:0       (128,)  AdagradW\n", "157                        sft_dense_moe_0_expert1/w:0      (4096,)  AdagradW\n", "158                        sft_dense_moe_0_expert1/b:0        (32,)  AdagradW\n", "159                        sft_dense_moe_1_expert0/w:0     (65536,)  AdagradW\n", "160                        sft_dense_moe_1_expert0/b:0       (128,)  AdagradW\n", "161                        sft_dense_moe_1_expert1/w:0      (4096,)  AdagradW\n", "162                        sft_dense_moe_1_expert1/b:0        (32,)  AdagradW\n", "163                            pinnerformer_input0/w:0     (65536,)  AdagradW\n", "164                            pinnerformer_input0/b:0       (512,)  AdagradW\n", "165                            pinnerformer_input1/w:0     (65536,)  AdagradW\n", "166                            pinnerformer_input1/b:0       (128,)  AdagradW\n", "167                            pinnerformer_input2/w:0       (256,)  AdagradW\n", "168                            pinnerformer_input2/b:0         (2,)  AdagradW\n", "169                               llm_other_input0/w:0   (1026560,)  AdagradW\n", "170                               llm_other_input0/b:0       (512,)  AdagradW\n", "171                               llm_other_input1/w:0     (65536,)  AdagradW\n", "172                               llm_other_input1/b:0       (128,)  AdagradW\n", "173                               llm_other_input2/w:0       (256,)  AdagradW\n", "174                               llm_other_input2/b:0         (2,)  AdagradW\n", "175                            llm_keywords_input0/w:0      (8192,)  AdagradW\n", "176                            llm_keywords_input0/b:0        (64,)  AdagradW\n", "177                            llm_keywords_input1/w:0       (128,)  AdagradW\n", "178                            llm_keywords_input1/b:0         (2,)  AdagradW\n", "179                           shark_refine_layer_0/w:0     (65536,)  AdagradW\n", "180                           shark_refine_layer_0/b:0       (512,)  AdagradW\n", "181              shark_refine_layer_0/LayerNorm/beta:0       (512,)  AdagradW\n", "182             shark_refine_layer_0/LayerNorm/gamma:0       (512,)  AdagradW\n", "183                           shark_refine_layer_1/w:0    (131072,)  AdagradW\n", "184                           shark_refine_layer_1/b:0       (256,)  AdagradW\n", "185                             shark_head_layer_0/w:0     (32768,)  AdagradW\n", "186                             shark_head_layer_0/b:0       (128,)  AdagradW\n", "187                shark_head_layer_0/LayerNorm/beta:0       (128,)  AdagradW\n", "188               shark_head_layer_0/LayerNorm/gamma:0       (128,)  AdagradW\n", "189                             shark_head_layer_1/w:0       (256,)  AdagradW\n", "190                             shark_head_layer_1/b:0         (2,)  AdagradW\n", "191                           long_term_pids_fake0/w:0     (51200,)  AdagradW\n", "192                           long_term_pids_fake0/b:0        (64,)  AdagradW\n", "193                           long_term_pids_fake1/w:0      (2048,)  AdagradW\n", "194                           long_term_pids_fake1/b:0        (32,)  AdagradW\n", "195                                 LayerNorm_1/beta:0        (32,)  AdagradW\n", "196                                LayerNorm_1/gamma:0        (32,)  AdagradW\n", "197                                 LayerNorm_2/beta:0        (32,)  AdagradW\n", "198                                LayerNorm_2/gamma:0        (32,)  AdagradW\n", "199                           long_term_aids_fake0/w:0     (51200,)  AdagradW\n", "200                           long_term_aids_fake0/b:0        (64,)  AdagradW\n", "201                           long_term_aids_fake1/w:0      (2048,)  AdagradW\n", "202                           long_term_aids_fake1/b:0        (32,)  AdagradW\n", "203                                 LayerNorm_3/beta:0        (32,)  AdagradW\n", "204                                LayerNorm_3/gamma:0        (32,)  AdagradW\n", "205                                 LayerNorm_4/beta:0        (32,)  AdagradW\n", "206                                LayerNorm_4/gamma:0        (32,)  AdagradW\n", "207                           long_term_play_fake0/w:0     (25600,)  AdagradW\n", "208                           long_term_play_fake0/b:0        (64,)  AdagradW\n", "209                           long_term_play_fake1/w:0      (2048,)  AdagradW\n", "210                           long_term_play_fake1/b:0        (32,)  AdagradW\n", "211                                 LayerNorm_5/beta:0        (32,)  AdagradW\n", "212                                LayerNorm_5/gamma:0        (32,)  AdagradW\n", "213                                 LayerNorm_6/beta:0        (32,)  AdagradW\n", "214                                LayerNorm_6/gamma:0        (32,)  AdagradW\n", "215                           long_term_tags_fake0/w:0     (25600,)  AdagradW\n", "216                           long_term_tags_fake0/b:0        (64,)  AdagradW\n", "217                           long_term_tags_fake1/w:0      (2048,)  AdagradW\n", "218                           long_term_tags_fake1/b:0        (32,)  AdagradW\n", "219                                 LayerNorm_7/beta:0        (32,)  AdagradW\n", "220                                LayerNorm_7/gamma:0        (32,)  AdagradW\n", "221                                 LayerNorm_8/beta:0        (32,)  AdagradW\n", "222                                LayerNorm_8/gamma:0        (32,)  AdagradW\n", "223                          long_term_times_fake0/w:0     (25600,)  AdagradW\n", "224                          long_term_times_fake0/b:0        (64,)  AdagradW\n", "225                          long_term_times_fake1/w:0      (2048,)  AdagradW\n", "226                          long_term_times_fake1/b:0        (32,)  AdagradW\n", "227                                 LayerNorm_9/beta:0        (32,)  AdagradW\n", "228                                LayerNorm_9/gamma:0        (32,)  AdagradW\n", "229                                LayerNorm_10/beta:0        (32,)  AdagradW\n", "230                               LayerNorm_10/gamma:0        (32,)  AdagradW\n", "231               colossus_rs_count_index_list_cl0/w:0     (12800,)  AdagradW\n", "232               colossus_rs_count_index_list_cl0/b:0        (64,)  AdagradW\n", "233               colossus_rs_count_index_list_cl1/w:0      (2048,)  AdagradW\n", "234               colossus_rs_count_index_list_cl1/b:0        (32,)  AdagradW\n", "235                                LayerNorm_11/beta:0        (32,)  AdagradW\n", "236                               LayerNorm_11/gamma:0        (32,)  AdagradW\n", "237                                LayerNorm_12/beta:0        (32,)  AdagradW\n", "238                               LayerNorm_12/gamma:0        (32,)  AdagradW\n", "239                   colossus_rs_item_id_list_cl0/w:0     (51200,)  AdagradW\n", "240                   colossus_rs_item_id_list_cl0/b:0        (64,)  AdagradW\n", "241                   colossus_rs_item_id_list_cl1/w:0      (2048,)  AdagradW\n", "242                   colossus_rs_item_id_list_cl1/b:0        (32,)  AdagradW\n", "243                                LayerNorm_13/beta:0        (32,)  AdagradW\n", "244                               LayerNorm_13/gamma:0        (32,)  AdagradW\n", "245                                LayerNorm_14/beta:0        (32,)  AdagradW\n", "246                               LayerNorm_14/gamma:0        (32,)  AdagradW\n", "247                     colossus_rs_lagV1_list_cl0/w:0     (12800,)  AdagradW\n", "248                     colossus_rs_lagV1_list_cl0/b:0        (64,)  AdagradW\n", "249                     colossus_rs_lagV1_list_cl1/w:0      (2048,)  AdagradW\n", "250                     colossus_rs_lagV1_list_cl1/b:0        (32,)  AdagradW\n", "251                                LayerNorm_15/beta:0        (32,)  AdagradW\n", "252                               LayerNorm_15/gamma:0        (32,)  AdagradW\n", "253                                LayerNorm_16/beta:0        (32,)  AdagradW\n", "254                               LayerNorm_16/gamma:0        (32,)  AdagradW\n", "255                        colossus_rs_lagV2_list0/w:0     (12800,)  AdagradW\n", "256                        colossus_rs_lagV2_list0/b:0        (64,)  AdagradW\n", "257                        colossus_rs_lagV2_list1/w:0      (2048,)  AdagradW\n", "258                        colossus_rs_lagV2_list1/b:0        (32,)  AdagradW\n", "259                                LayerNorm_17/beta:0        (32,)  AdagradW\n", "260                               LayerNorm_17/gamma:0        (32,)  AdagradW\n", "261                                LayerNorm_18/beta:0        (32,)  AdagradW\n", "262                               LayerNorm_18/gamma:0        (32,)  AdagradW\n", "263               colossus_rs_pagecode_id_list_cl0/w:0     (12800,)  AdagradW\n", "264               colossus_rs_pagecode_id_list_cl0/b:0        (64,)  AdagradW\n", "265               colossus_rs_pagecode_id_list_cl1/w:0      (2048,)  AdagradW\n", "266               colossus_rs_pagecode_id_list_cl1/b:0        (32,)  AdagradW\n", "267                                LayerNorm_19/beta:0        (32,)  AdagradW\n", "268                               LayerNorm_19/gamma:0        (32,)  AdagradW\n", "269                                LayerNorm_20/beta:0        (32,)  AdagradW\n", "270                               LayerNorm_20/gamma:0        (32,)  AdagradW\n", "271            colossus_rs_uniform_spu_id_list_cl0/w:0     (51200,)  AdagradW\n", "272            colossus_rs_uniform_spu_id_list_cl0/b:0        (64,)  AdagradW\n", "273            colossus_rs_uniform_spu_id_list_cl1/w:0      (2048,)  AdagradW\n", "274            colossus_rs_uniform_spu_id_list_cl1/b:0        (32,)  AdagradW\n", "275                                LayerNorm_21/beta:0        (32,)  AdagradW\n", "276                               LayerNorm_21/gamma:0        (32,)  AdagradW\n", "277                                LayerNorm_22/beta:0        (32,)  AdagradW\n", "278                               LayerNorm_22/gamma:0        (32,)  AdagradW\n", "279                 SENET_layer/excitation_layer_1/w:0     (11475,)  AdagradW\n", "280                 SENET_layer/excitation_layer_1/b:0        (45,)  AdagradW\n", "281    SENET_layer/excitation_layer_1/LayerNorm/beta:0        (45,)  AdagradW\n", "282   SENET_layer/excitation_layer_1/LayerNorm/gamma:0        (45,)  AdagradW\n", "283                 SENET_layer/excitation_layer_2/w:0     (11475,)  AdagradW\n", "284                 SENET_layer/excitation_layer_2/b:0       (255,)  AdagradW\n", "285                                candidate_layer/w:0   (1044480,)  AdagradW\n", "286                                candidate_layer/b:0       (256,)  AdagradW\n", "287            candidate_layer/rc_seq_q_trans_matrix:0      (8192,)  AdagradW\n", "288            candidate_layer/rc_seq_k_trans_matrix:0      (3072,)  AdagradW\n", "289            candidate_layer/rc_seq_v_trans_matrix:0      (3072,)  AdagradW\n", "290                                attention_layer/w:0   (4177920,)  AdagradW\n", "291                                attention_layer/b:0      (1024,)  AdagradW\n", "292         attention_layer/long_term_q_trans_matrix:0     (32768,)  AdagradW\n", "293         attention_layer/long_term_k_trans_matrix:0      (1792,)  AdagradW\n", "294         attention_layer/long_term_v_trans_matrix:0      (1792,)  AdagradW\n", "295    attention_layer/long_recent_attq_trans_matrix:0      (1536,)  AdagradW\n", "296    attention_layer/long_recent_attk_trans_matrix:0      (1792,)  AdagradW\n", "297    attention_layer/long_recent_attv_trans_matrix:0      (1792,)  AdagradW\n", "298      attention_layer/long_seq_suffix_pooling_0/w:0    (276480,)  AdagradW\n", "299      attention_layer/long_seq_suffix_pooling_0/b:0       (256,)  AdagradW\n", "300  attention_layer/long_seq_suffix_pooling_0/Laye...       (256,)  AdagradW\n", "301  attention_layer/long_seq_suffix_pooling_0/Laye...       (256,)  AdagradW\n", "302      attention_layer/long_seq_suffix_pooling_1/w:0      (8192,)  AdagradW\n", "303      attention_layer/long_seq_suffix_pooling_1/b:0        (32,)  AdagradW\n", "304  attention_layer/good_show_user_embedding_pooli...     (86016,)  AdagradW\n", "305  attention_layer/good_show_user_embedding_pooli...       (256,)  AdagradW\n", "306  attention_layer/good_show_user_embedding_pooli...       (256,)  AdagradW\n", "307  attention_layer/good_show_user_embedding_pooli...       (256,)  AdagradW\n", "308  attention_layer/good_show_user_embedding_pooli...      (8192,)  AdagradW\n", "309  attention_layer/good_show_user_embedding_pooli...        (32,)  AdagradW\n", "310  attention_layer/good_show_user_embedding_atten...      (9216,)  AdagradW\n", "311  attention_layer/good_show_user_embedding_atten...      (1536,)  AdagradW\n", "312  attention_layer/good_show_user_embedding_atten...      (1536,)  AdagradW\n", "313          good_click_cate2cate_real_price_fake0/w:0     (25600,)  AdagradW\n", "314          good_click_cate2cate_real_price_fake0/b:0        (64,)  AdagradW\n", "315          good_click_cate2cate_real_price_fake1/w:0      (2048,)  AdagradW\n", "316          good_click_cate2cate_real_price_fake1/b:0        (32,)  AdagradW\n", "317                                LayerNorm_23/beta:0        (32,)  AdagradW\n", "318                               LayerNorm_23/gamma:0        (32,)  AdagradW\n", "319                                LayerNorm_24/beta:0        (32,)  AdagradW\n", "320                               LayerNorm_24/gamma:0        (32,)  AdagradW\n", "321            good_click_cate2cate_category_fake0/w:0     (25600,)  AdagradW\n", "322            good_click_cate2cate_category_fake0/b:0        (64,)  AdagradW\n", "323            good_click_cate2cate_category_fake1/w:0      (2048,)  AdagradW\n", "324            good_click_cate2cate_category_fake1/b:0        (32,)  AdagradW\n", "325                                LayerNorm_25/beta:0        (32,)  AdagradW\n", "326                               LayerNorm_25/gamma:0        (32,)  AdagradW\n", "327                                LayerNorm_26/beta:0        (32,)  AdagradW\n", "328                               LayerNorm_26/gamma:0        (32,)  AdagradW\n", "329          good_click_cate2cate_carry_type_fake0/w:0     (12800,)  AdagradW\n", "330          good_click_cate2cate_carry_type_fake0/b:0        (64,)  AdagradW\n", "331          good_click_cate2cate_carry_type_fake1/w:0      (2048,)  AdagradW\n", "332          good_click_cate2cate_carry_type_fake1/b:0        (32,)  AdagradW\n", "333                                LayerNorm_27/beta:0        (32,)  AdagradW\n", "334                               LayerNorm_27/gamma:0        (32,)  AdagradW\n", "335                                LayerNorm_28/beta:0        (32,)  AdagradW\n", "336                               LayerNorm_28/gamma:0        (32,)  AdagradW\n", "337                 good_click_cate2cate_lag_fake0/w:0     (25600,)  AdagradW\n", "338                 good_click_cate2cate_lag_fake0/b:0        (64,)  AdagradW\n", "339                 good_click_cate2cate_lag_fake1/w:0      (2048,)  AdagradW\n", "340                 good_click_cate2cate_lag_fake1/b:0        (32,)  AdagradW\n", "341                                LayerNorm_29/beta:0        (32,)  AdagradW\n", "342                               LayerNorm_29/gamma:0        (32,)  AdagradW\n", "343                                LayerNorm_30/beta:0        (32,)  AdagradW\n", "344                               LayerNorm_30/gamma:0        (32,)  AdagradW\n", "345             good_click_cate2cate_item_id_fake0/w:0     (51200,)  AdagradW\n", "346             good_click_cate2cate_item_id_fake0/b:0        (64,)  AdagradW\n", "347             good_click_cate2cate_item_id_fake1/w:0      (2048,)  AdagradW\n", "348             good_click_cate2cate_item_id_fake1/b:0        (32,)  AdagradW\n", "349                                LayerNorm_31/beta:0        (32,)  AdagradW\n", "350                               LayerNorm_31/gamma:0        (32,)  AdagradW\n", "351                                LayerNorm_32/beta:0        (32,)  AdagradW\n", "352                               LayerNorm_32/gamma:0        (32,)  AdagradW\n", "353           good_click_cate2cate_seller_id_fake0/w:0     (51200,)  AdagradW\n", "354           good_click_cate2cate_seller_id_fake0/b:0        (64,)  AdagradW\n", "355           good_click_cate2cate_seller_id_fake1/w:0      (2048,)  AdagradW\n", "356           good_click_cate2cate_seller_id_fake1/b:0        (32,)  AdagradW\n", "357                                LayerNorm_33/beta:0        (32,)  AdagradW\n", "358                               LayerNorm_33/gamma:0        (32,)  AdagradW\n", "359                                LayerNorm_34/beta:0        (32,)  AdagradW\n", "360                               LayerNorm_34/gamma:0        (32,)  AdagradW\n", "361               good_click_cate2cate_layer_new_0/w:0      (7680,)  AdagradW\n", "362               good_click_cate2cate_layer_new_0/b:0       (128,)  AdagradW\n", "363  good_click_cate2cate_layer_new_0/LayerNorm/beta:0       (128,)  AdagradW\n", "364  good_click_cate2cate_layer_new_0/LayerNorm/gam...       (128,)  AdagradW\n", "365               good_click_cate2cate_layer_new_1/w:0      (8192,)  AdagradW\n", "366               good_click_cate2cate_layer_new_1/b:0        (64,)  AdagradW\n", "367  good_click_cate2cate_layer_new_1/LayerNorm/beta:0        (64,)  AdagradW\n", "368  good_click_cate2cate_layer_new_1/LayerNorm/gam...        (64,)  AdagradW\n", "369               good_click_cate2cate_layer_new_2/w:0      (2048,)  AdagradW\n", "370               good_click_cate2cate_layer_new_2/b:0        (32,)  AdagradW\n", "371                      gc_recent_attq_trans_matrix:0      (1536,)  AdagradW\n", "372                      gc_recent_attk_trans_matrix:0      (1920,)  AdagradW\n", "373                      gc_recent_attv_trans_matrix:0      (1920,)  AdagradW\n", "374  gc_self_attention_layer/gc_self_attnq_trans_ma...      (1920,)  AdagradW\n", "375  gc_self_attention_layer/gc_self_attnk_trans_ma...      (1920,)  AdagradW\n", "376  gc_self_attention_layer/gc_self_attnv_trans_ma...      (1920,)  AdagradW\n", "377                        gc_self_attention_layer/w:0    (102400,)  AdagradW\n", "378                        gc_self_attention_layer/b:0        (64,)  AdagradW\n", "379           gc_self_attention_layer/LayerNorm/beta:0        (64,)  AdagradW\n", "380          gc_self_attention_layer/LayerNorm/gamma:0        (64,)  AdagradW\n", "381  long_self_attention_layer/long_self_attnq_tran...      (1792,)  AdagradW\n", "382  long_self_attention_layer/long_self_attnk_tran...      (1792,)  AdagradW\n", "383  long_self_attention_layer/long_self_attnv_tran...      (1792,)  AdagradW\n", "384                      long_self_attention_layer/w:0    (204800,)  AdagradW\n", "385                      long_self_attention_layer/b:0        (64,)  AdagradW\n", "386         long_self_attention_layer/LayerNorm/beta:0        (64,)  AdagradW\n", "387        long_self_attention_layer/LayerNorm/gamma:0        (64,)  AdagradW\n", "388                            Cross_layer_sub/w_sub:0     (23280,)  AdagradW\n", "389                QUEUE_SOFT/share_bottom_layer_0/w:0   (9122816,)  AdagradW\n", "390                QUEUE_SOFT/share_bottom_layer_0/b:0      (1024,)  AdagradW\n", "391   QUEUE_SOFT/share_bottom_layer_0/LayerNorm/beta:0      (1024,)  AdagradW\n", "392  QUEUE_SOFT/share_bottom_layer_0/LayerNorm/gamma:0      (1024,)  AdagradW\n", "393                         QUEUE_SOFT/pxr_layer_1/w:0    (262144,)  AdagradW\n", "394                         QUEUE_SOFT/pxr_layer_1/b:0       (256,)  AdagradW\n", "395            QUEUE_SOFT/pxr_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "396           QUEUE_SOFT/pxr_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "397                         QUEUE_SOFT/pxr_layer_2/w:0     (32768,)  AdagradW\n", "398                         QUEUE_SOFT/pxr_layer_2/b:0       (128,)  AdagradW\n", "399            QUEUE_SOFT/pxr_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "400           QUEUE_SOFT/pxr_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "401                         QUEUE_SOFT/pxr_layer_3/w:0       (256,)  AdagradW\n", "402                         QUEUE_SOFT/pxr_layer_3/b:0         (2,)  AdagradW\n", "403                         QUEUE_SOFT/ped_layer_1/w:0    (262144,)  AdagradW\n", "404                         QUEUE_SOFT/ped_layer_1/b:0       (256,)  AdagradW\n", "405            QUEUE_SOFT/ped_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "406           QUEUE_SOFT/ped_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "407                         QUEUE_SOFT/ped_layer_2/w:0     (32768,)  AdagradW\n", "408                         QUEUE_SOFT/ped_layer_2/b:0       (128,)  AdagradW\n", "409            QUEUE_SOFT/ped_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "410           QUEUE_SOFT/ped_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "411                         QUEUE_SOFT/ped_layer_3/w:0       (256,)  AdagradW\n", "412                         QUEUE_SOFT/ped_layer_3/b:0         (2,)  AdagradW\n", "413                   QUEUE_SOFT/ctr_upper_layer_1/w:0    (262144,)  AdagradW\n", "414                   QUEUE_SOFT/ctr_upper_layer_1/b:0       (256,)  AdagradW\n", "415      QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "416     QUEUE_SOFT/ctr_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "417                   QUEUE_SOFT/ctr_upper_layer_2/w:0     (32768,)  AdagradW\n", "418                   QUEUE_SOFT/ctr_upper_layer_2/b:0       (128,)  AdagradW\n", "419      QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "420     QUEUE_SOFT/ctr_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "421                   QUEUE_SOFT/ctr_upper_layer_3/w:0       (256,)  AdagradW\n", "422                   QUEUE_SOFT/ctr_upper_layer_3/b:0         (2,)  AdagradW\n", "423                       QUEUE_SOFT/upper_layer_1/w:0    (262144,)  AdagradW\n", "424                       QUEUE_SOFT/upper_layer_1/b:0       (256,)  AdagradW\n", "425          QUEUE_SOFT/upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "426         QUEUE_SOFT/upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "427                       QUEUE_SOFT/upper_layer_2/w:0     (32768,)  AdagradW\n", "428                       QUEUE_SOFT/upper_layer_2/b:0       (128,)  AdagradW\n", "429          QUEUE_SOFT/upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "430         QUEUE_SOFT/upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "431                       QUEUE_SOFT/upper_layer_3/w:0       (256,)  AdagradW\n", "432                       QUEUE_SOFT/upper_layer_3/b:0         (2,)  AdagradW\n", "433                               QUEUE_SOFT/w_ctcvr:0         (1,)  AdagradW\n", "434                                    dcn_layer_0/u:0   (1065856,)  AdagradW\n", "435                                    dcn_layer_0/v:0   (1065856,)  AdagradW\n", "436                                    dcn_layer_0/b:0     (16654,)  AdagradW\n", "437                       dcn_layer_0/LayerNorm/beta:0     (16654,)  AdagradW\n", "438                      dcn_layer_0/LayerNorm/gamma:0     (16654,)  AdagradW\n", "439                                    dcn_layer_1/u:0   (1065856,)  AdagradW\n", "440                                    dcn_layer_1/v:0   (1065856,)  AdagradW\n", "441                                    dcn_layer_1/b:0     (16654,)  AdagradW\n", "442                       dcn_layer_1/LayerNorm/beta:0     (16654,)  AdagradW\n", "443                      dcn_layer_1/LayerNorm/gamma:0     (16654,)  AdagradW\n", "444                       share_bottom_layer_new_0/w:0  (17053696,)  AdagradW\n", "445                       share_bottom_layer_new_0/b:0      (1024,)  AdagradW\n", "446          share_bottom_layer_new_0/LayerNorm/beta:0      (1024,)  AdagradW\n", "447         share_bottom_layer_new_0/LayerNorm/gamma:0      (1024,)  AdagradW\n", "448                             generalize_layer_0/w:0     (30720,)  AdagradW\n", "449                             generalize_layer_0/b:0       (128,)  AdagradW\n", "450                generalize_layer_0/LayerNorm/beta:0       (128,)  AdagradW\n", "451               generalize_layer_0/LayerNorm/gamma:0       (128,)  AdagradW\n", "452                  reco_share_bottom_layer_new_0/w:0   (5529600,)  AdagradW\n", "453                  reco_share_bottom_layer_new_0/b:0      (1024,)  AdagradW\n", "454     reco_share_bottom_layer_new_0/LayerNorm/beta:0      (1024,)  AdagradW\n", "455    reco_share_bottom_layer_new_0/LayerNorm/gamma:0      (1024,)  AdagradW\n", "456                             generalize_layer_1/w:0      (8192,)  AdagradW\n", "457                             generalize_layer_1/b:0        (64,)  AdagradW\n", "458                generalize_layer_1/LayerNorm/beta:0        (64,)  AdagradW\n", "459               generalize_layer_1/LayerNorm/gamma:0        (64,)  AdagradW\n", "460                             generalize_layer_2/w:0      (2048,)  AdagradW\n", "461                             generalize_layer_2/b:0        (32,)  AdagradW\n", "462                generalize_layer_2/LayerNorm/beta:0        (32,)  AdagradW\n", "463               generalize_layer_2/LayerNorm/gamma:0        (32,)  AdagradW\n", "464                             generalize_layer_3/w:0        (64,)  AdagradW\n", "465                             generalize_layer_3/b:0         (2,)  AdagradW\n", "466                                    pxr_layer_1/w:0    (262144,)  AdagradW\n", "467                                    pxr_layer_1/b:0       (256,)  AdagradW\n", "468                       pxr_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "469                      pxr_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "470                                    pxr_layer_2/w:0     (32768,)  AdagradW\n", "471                                    pxr_layer_2/b:0       (128,)  AdagradW\n", "472                       pxr_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "473                      pxr_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "474                                    pxr_layer_3/w:0       (256,)  AdagradW\n", "475                                    pxr_layer_3/b:0         (2,)  AdagradW\n", "476                                    ped_layer_1/w:0    (262144,)  AdagradW\n", "477                                    ped_layer_1/b:0       (256,)  AdagradW\n", "478                       ped_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "479                      ped_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "480                                    ped_layer_2/w:0     (32768,)  AdagradW\n", "481                                    ped_layer_2/b:0       (128,)  AdagradW\n", "482                       ped_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "483                      ped_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "484                                    ped_layer_3/w:0       (256,)  AdagradW\n", "485                                    ped_layer_3/b:0         (2,)  AdagradW\n", "486                              ctr_upper_layer_1/w:0    (262144,)  AdagradW\n", "487                              ctr_upper_layer_1/b:0       (256,)  AdagradW\n", "488                 ctr_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "489                ctr_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "490                              ctr_upper_layer_2/w:0     (32768,)  AdagradW\n", "491                              ctr_upper_layer_2/b:0       (128,)  AdagradW\n", "492                 ctr_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "493                ctr_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "494                              ctr_upper_layer_3/w:0       (256,)  AdagradW\n", "495                              ctr_upper_layer_3/b:0         (2,)  AdagradW\n", "496                          assist1_upper_layer_1/w:0    (262144,)  AdagradW\n", "497                          assist1_upper_layer_1/b:0       (256,)  AdagradW\n", "498             assist1_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "499            assist1_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "500                          assist1_upper_layer_2/w:0     (32768,)  AdagradW\n", "501                          assist1_upper_layer_2/b:0       (128,)  AdagradW\n", "502             assist1_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "503            assist1_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "504                          assist1_upper_layer_3/w:0       (256,)  AdagradW\n", "505                          assist1_upper_layer_3/b:0         (2,)  AdagradW\n", "506                          assist2_upper_layer_1/w:0    (262144,)  AdagradW\n", "507                          assist2_upper_layer_1/b:0       (256,)  AdagradW\n", "508             assist2_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "509            assist2_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "510                          assist2_upper_layer_2/w:0     (32768,)  AdagradW\n", "511                          assist2_upper_layer_2/b:0       (128,)  AdagradW\n", "512             assist2_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "513            assist2_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "514                          assist2_upper_layer_3/w:0       (256,)  AdagradW\n", "515                          assist2_upper_layer_3/b:0         (2,)  AdagradW\n", "516                                    cid_layer_1/w:0    (262144,)  AdagradW\n", "517                                    cid_layer_1/b:0       (256,)  AdagradW\n", "518                       cid_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "519                      cid_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "520                                    cid_layer_2/w:0     (32768,)  AdagradW\n", "521                                    cid_layer_2/b:0       (128,)  AdagradW\n", "522                       cid_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "523                      cid_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "524                                    cid_layer_3/w:0       (256,)  AdagradW\n", "525                                    cid_layer_3/b:0         (2,)  AdagradW\n", "526                                  upper_layer_1/w:0    (295168,)  AdagradW\n", "527                                  upper_layer_1/b:0       (256,)  AdagradW\n", "528                    upper_layer_1/layer_norm/beta:0       (256,)  AdagradW\n", "529                   upper_layer_1/layer_norm/gamma:0       (256,)  AdagradW\n", "530                                  upper_layer_2/w:0     (32768,)  AdagradW\n", "531                                  upper_layer_2/b:0       (128,)  AdagradW\n", "532                    upper_layer_2/layer_norm/beta:0       (128,)  AdagradW\n", "533                   upper_layer_2/layer_norm/gamma:0       (128,)  AdagradW\n", "534                                  upper_layer_3/w:0       (256,)  AdagradW\n", "535                                  upper_layer_3/b:0         (2,)  AdagradW\n", "536                           rocket_upper_layer_2/w:0    (110592,)  AdagradW\n", "537                           rocket_upper_layer_2/b:0       (128,)  AdagradW\n", "538              rocket_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "539             rocket_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "540                           rocket_upper_layer_3/w:0       (256,)  AdagradW\n", "541                           rocket_upper_layer_3/b:0         (2,)  AdagradW\n", "542                                 global_layer_1/w:0    (262144,)  AdagradW\n", "543                                 global_layer_1/b:0       (256,)  AdagradW\n", "544                    global_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "545                   global_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "546                                 global_layer_2/w:0     (32768,)  AdagradW\n", "547                                 global_layer_2/b:0       (128,)  AdagradW\n", "548                    global_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "549                   global_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "550                                 global_layer_3/w:0       (256,)  AdagradW\n", "551                                 global_layer_3/b:0         (2,)  AdagradW\n", "552                                 direct_layer_1/w:0    (262144,)  AdagradW\n", "553                                 direct_layer_1/b:0       (256,)  AdagradW\n", "554                    direct_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "555                   direct_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "556                                 direct_layer_2/w:0     (32768,)  AdagradW\n", "557                                 direct_layer_2/b:0       (128,)  AdagradW\n", "558                    direct_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "559                   direct_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "560                                 direct_layer_3/w:0       (256,)  AdagradW\n", "561                                 direct_layer_3/b:0         (2,)  AdagradW\n", "562                      pic_calibration/cali_lambda:0         (1,)  AdagradW\n", "563               Pic_calibration/cali_weight_lambda:0         (7,)  AdagradW\n", "564                                    gmv_layer_0/w:0    (262144,)  AdagradW\n", "565                                    gmv_layer_0/b:0       (256,)  AdagradW\n", "566                       gmv_layer_0/LayerNorm/beta:0       (256,)  AdagradW\n", "567                      gmv_layer_0/LayerNorm/gamma:0       (256,)  AdagradW\n", "568                                    gmv_layer_1/w:0     (32768,)  AdagradW\n", "569                                    gmv_layer_1/b:0       (128,)  AdagradW\n", "570                       gmv_layer_1/LayerNorm/beta:0       (128,)  AdagradW\n", "571                      gmv_layer_1/LayerNorm/gamma:0       (128,)  AdagradW\n", "572                                    gmv_layer_2/w:0       (256,)  AdagradW\n", "573                                    gmv_layer_2/b:0         (2,)  AdagradW\n", "574                             reco_upper_layer_1/w:0    (262144,)  AdagradW\n", "575                             reco_upper_layer_1/b:0       (256,)  AdagradW\n", "576                reco_upper_layer_1/LayerNorm/beta:0       (256,)  AdagradW\n", "577               reco_upper_layer_1/LayerNorm/gamma:0       (256,)  AdagradW\n", "578                             reco_upper_layer_2/w:0     (32768,)  AdagradW\n", "579                             reco_upper_layer_2/b:0       (128,)  AdagradW\n", "580                reco_upper_layer_2/LayerNorm/beta:0       (128,)  AdagradW\n", "581               reco_upper_layer_2/LayerNorm/gamma:0       (128,)  AdagradW\n", "582                             reco_upper_layer_3/w:0       (256,)  AdagradW\n", "583                             reco_upper_layer_3/b:0         (2,)  AdagradW\n", "584                                          w_ctcvr:0         (1,)  AdagradW\n", "585                                            w_ctr:0         (1,)  AdagradW\n", "586                                            w_p3s:0         (1,)  AdagradW\n", "587                                            w_ped:0         (1,)  AdagradW\n", "588                                     w_reco_ctcvr:0         (1,)  AdagradW\n", "589                                     w_global_pay:0         (1,)  AdagradW\n", "590                                     w_direct_pay:0         (1,)  AdagradW\n", "591                                           w_ast2:0         (1,)  AdagradW\n", "592                                       w_ue_ctcvr:0         (1,)  AdagradW\n"]}], "source": ["print(dense_new)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # type==change，目标是较小的560 size rocket\n", "    origin_weight = {}\n", "\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "\n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name in origin_weight:\n", "            origin_size, col = origin_weight[var_name]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                # tf_weight[]: (560, 128)\n", "                if var_name == 'rocket_upper_layer_2/w:0':\n", "                    new_size = 560\n", "                    # 原来的 weight\n", "                    ori_weight = warmup_weight[var_name]\n", "                    ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "                    # 进行赋值\n", "                    new_weight =  ori_weight[:new_size, :]\n", "                    new_extra = ori_extra[:new_size, :,:]\n", "                    # 回填\n", "                    warmup_weight[var_name]  = new_weight\n", "                    warmup_extra[var_name] = get_extra(new_extra)\n", "                    print(\"加载的 dense variable({}) 由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "                else:\n", "                    print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                    try:\n", "                        del warmup_weight[var_name]\n", "                        del warmup_extra[var_name]\n", "                    except KeyError as e:\n", "                        if var_name not in warmup_weight:\n", "                            print(f\"{var_name} not in warmup_weight\")\n", "                        else:\n", "                            print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(user_predict/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(user_predict/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(photo_content/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(photo_content/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractDenseUserLlmDpskValidUser/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractDenseUserLlmDpskValidUser/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPhotoTextEmbedding/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPhotoTextEmbedding/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidHetuEmb/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidHetuEmb/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractGoodsHetuEmb/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractGoodsHetuEmb/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidSim512/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidSim512/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidSim128/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidSim128/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidSim1k/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(ExtractPidSim1k/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(attention_layer/long_recent_attq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(attention_layer/long_recent_attk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(attention_layer/long_recent_attv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_recent_attq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_recent_attk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_recent_attv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_self_attention_layer/gc_self_attnq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_self_attention_layer/gc_self_attnk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_self_attention_layer/gc_self_attnv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_self_attention_layer/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_self_attention_layer/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_self_attention_layer/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(gc_self_attention_layer/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_self_attention_layer/long_self_attnq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_self_attention_layer/long_self_attnk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_self_attention_layer/long_self_attnv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_self_attention_layer/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_self_attention_layer/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_self_attention_layer/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_self_attention_layer/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(inner_order_num_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(inner_order_num_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(ue_num_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(ue_num_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(first_stage_ctcvr_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(first_stage_ctcvr_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_index_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_index_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_sparse/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(rank_context_sparse/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(dcn_layer_0/u:0) size (955072 vs 1065856) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_0/v:0) size (955072 vs 1065856) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_0/b:0) size (14923 vs 16654) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_0/LayerNorm/beta:0) size (14923 vs 16654) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_0/LayerNorm/gamma:0) size (14923 vs 16654) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_1/u:0) size (955072 vs 1065856) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_1/v:0) size (955072 vs 1065856) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_1/b:0) size (14923 vs 16654) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_1/LayerNorm/beta:0) size (14923 vs 16654) 不匹配，其值被忽略\n", "加载的 dense variable(dcn_layer_1/LayerNorm/gamma:0) size (14923 vs 16654) 不匹配，其值被忽略\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) size (15281152 vs 17053696) 不匹配，其值被忽略\n", "加载的 dense variable(rocket_upper_layer_2/w:0) 由rocket_upper_layer_2/w:0初始化, size is 110592 and 110592\n"]}, {"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m res_weight, res_extra \u001b[38;5;241m=\u001b[39m mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n\u001b[1;32m      2\u001b[0m dense_change \u001b[38;5;241m=\u001b[39m get_dense_table_from_dict(res_weight, res_extra)\n\u001b[1;32m      3\u001b[0m diff_dense_table(dense_new, dense_change)\n", "File \u001b[0;32m~/Documents/python/DNNFIX/pylib/dnn_fix_v2.py:308\u001b[0m, in \u001b[0;36mmock_load_dense_func\u001b[0;34m(load_dense_func, warmup_dense_table, current_dense_table, load_option)\u001b[0m\n\u001b[1;32m    305\u001b[0m load_option_obj \u001b[38;5;241m=\u001b[39m Dict2Obj(load_option)\n\u001b[1;32m    307\u001b[0m \u001b[38;5;66;03m# 调用 load_dense_func，模拟真实场景的行为，打印结果\u001b[39;00m\n\u001b[0;32m--> 308\u001b[0m weight, extra \u001b[38;5;241m=\u001b[39m load_dense_func(\n\u001b[1;32m    309\u001b[0m     warmup_weight, warmup_extra, ps_weight, ps_extra, tf_weight, load_option_obj)\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mweight_res.txt\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mw+\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m    312\u001b[0m     f\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;28mstr\u001b[39m(weight))\n", "Cell \u001b[0;32mIn[6], line 93\u001b[0m, in \u001b[0;36mmy_load_dense_func\u001b[0;34m(warmup_weight, warmup_extra, ps_weight, ps_extra, tf_weight, load_option)\u001b[0m\n\u001b[1;32m     90\u001b[0m     weight \u001b[38;5;241m=\u001b[39m tf_weight\n\u001b[1;32m     91\u001b[0m     extra \u001b[38;5;241m=\u001b[39m ps_extra\n\u001b[0;32m---> 93\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(weight) \u001b[38;5;241m==\u001b[39m dense_variable_nums\n\u001b[1;32m     94\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(extra) \u001b[38;5;241m==\u001b[39m dense_variable_nums\n\u001b[1;32m     96\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mend my_load_dense_func\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mAssertionError\u001b[0m: "]}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}