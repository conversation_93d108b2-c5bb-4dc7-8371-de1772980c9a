{"key": "bs_reco_sim_v3_opt_v4", "batch_size": 100, "run_pipelines": ["gsu_sim29"], "item_slot_attrs": ["gsu_item_slots_v2"], "item_sign_attrs": ["gsu_item_signs_v2"], "remap_slots": [380, 381, 382, 383, 384], "pipeline_manager_config": {"base_pipeline": {"processor": {"ad_bs_retriever_F4E6F5": {"$output_item_attrs": [], "attr_keys": [{"attr_name": "photo_id", "common": false, "data_type": 0, "enum_num": 22372, "list": false}], "downstream_processor": "enrich_attr_by_lua_60F3B8", "type_name": "AdBsRetriever"}, "colossus_DCE14E": {"$output_item_attrs": [], "cache_capacity": 2000000, "client_type": "common_item_client", "downstream_processor": "gsu_retriever_with_colossus_resp_v2_7BC6CB", "expire_time": 600, "output_attr": "colossus_output", "parse_to_pb": false, "service_name": "grpc_colossusSimAdStateItemNew", "type_name": "AdCommonColossus<PERSON>er", "max_resp_item_num": 2500}, "enrich_attr_by_lua_60F3B8": {"$output_item_attrs": ["photo_id_redis_key"], "export_item_attr": ["photo_id_redis_key"], "function_for_item": "calculate", "import_item_attr": ["photo_id"], "lua_script": "function calculate()\n          local photo_id = photo_id\n          return \"swingsim_photo_list:\"..photo_id\n        end", "type_name": "CommonRecoLuaAttrEnricher"}, "enrich_attr_by_lua_9AA80A": {"$output_item_attrs": ["target_photo_id", "target_photo_id_expand_1", "target_photo_id_expand_2", "target_photo_id_expand_3"], "export_item_attr": ["target_photo_id", "target_photo_id_expand_1", "target_photo_id_expand_2", "target_photo_id_expand_3"], "function_for_item": "calculate", "import_item_attr": ["photo_id", "expand_photo_id_list"], "lua_script": "function calculate()\n              local target_photo_id = photo_id\n              local expand_photo_id_list = expand_photo_id_list\n              local str_len = 0\n              if expand_photo_id_list ~= nil then\n                str_len = #expand_photo_id_list\n              end\n              if str_len < 3 then return target_photo_id, target_photo_id, target_photo_id, target_photo_id\n              else return  target_photo_id, tonumber(expand_photo_id_list[1]), tonumber(expand_photo_id_list[2]), tonumber(expand_photo_id_list[3])\n              end\n            end", "type_name": "CommonRecoLuaAttrEnricher"}, "enrich_attr_by_lua_BC6F30": {"$downstream_item_attrs": ["target_photo_embedding_expand_1", "target_photo_embedding_expand_2", "target_photo_embedding_expand_3", "target_photo_embedding_raw"], "$output_item_attrs": ["target_photo_embedding"], "export_item_attr": ["target_photo_embedding"], "function_for_item": "calculate", "import_item_attr": ["target_photo_embedding_raw", "target_photo_embedding_expand_1", "target_photo_embedding_expand_2", "target_photo_embedding_expand_3"], "lua_script": "function calculate()\n          target_photo_embedding = {}\n          local size = 0\n          if target_photo_embedding_raw ~= nil and target_photo_embedding_expand_1 ~= nil and target_photo_embedding_expand_2 ~= nil and target_photo_embedding_expand_3 ~= nil then\n            size = #target_photo_embedding_raw\n          end\n          for i = 1, size do  \n            target_photo_embedding[i] = (target_photo_embedding_raw[i]*2 + target_photo_embedding_expand_1[i] + target_photo_embedding_expand_2[i] + target_photo_embedding_expand_3[i])/4.0\n          end\n          return target_photo_embedding\n        end", "type_name": "CommonRecoLuaAttrEnricher"}, "enrich_attr_by_lua_F28858": {"$output_item_attrs": ["is_target_photo_embedding_hit"], "export_item_attr": ["is_target_photo_embedding_hit"], "function_for_item": "calculate", "import_item_attr": ["target_photo_embedding"], "lua_script": "function calculate()\n          return target_photo_embedding ~= nil\n        end", "type_name": "CommonRecoLuaAttrEnricher"}, "fetch_tower_topn_dot_product_pxtr_2A2DC9": {"$output_item_attrs": [], "downstream_processor": "gsu_with_index_C47D3C", "emb_dim": 64, "item_list_from_attr": "colossus_pid", "kess_service": "grpc_adPxtrCalcMmuHetu", "output_type": 4, "predict_labels": ["dp"], "pxtr_type": 1, "return_pxtr_value_attr": "distance", "return_sorted_item_ids_attr": "sorted_item_idx_attr", "server_request_type": "rt_tower_predict_pxtr", "shards": 8, "sorted_item_idx_attr": "sorted_item_idx", "timeout_ms": 20, "top_n": 50, "type_name": "TowerFetchTopNDotProductAttrEnricher", "user_embedding_attr": "user_embedding"}, "fetch_tower_topn_dot_product_pxtr_E15267": {"$output_item_attrs": [], "downstream_processor": "gsu_with_index_AD721B", "emb_dim": 64, "item_list_from_attr": "colossus_pid", "kess_service": "grpc_adPxtrCalcMmuHetu", "output_type": 4, "predict_labels": ["dp"], "pxtr_type": 1, "return_pxtr_value_attr": "distance", "return_sorted_item_ids_attr": "sorted_item_idx_attr", "server_request_type": "rt_tower_predict_pxtr", "shards": 8, "sorted_item_idx_attr": "sorted_item_idx_v2", "timeout_ms": 20, "top_n": 100, "type_name": "TowerFetchTopNDotProductAttrEnricher", "user_embedding_attr": "user_embedding"}, "get_item_attr_from_redis_C683EB": {"$output_item_attrs": ["expand_photo_id_raw"], "cluster_name": "adAlgPhotoSPU", "redis_key_from": "photo_id_redis_key", "save_value_to": "expand_photo_id_raw", "cache_bits": 24, "timeout_ms": 6, "cache_delay_delete_ms": 100000000, "type_name": "CommonRecoRedisItemAttrEnricher"}, "get_remote_embedding_1EA91E": {"$output_item_attrs": ["target_photo_embedding_expand_1"], "client_side_shard": true, "downstream_processor": "enrich_attr_by_lua_BC6F30", "expire_time": 600, "id_converter": {"type_name": "kuibaEmbeddingIdConverter"}, "kess_service": "grpc_adMmuHetuEmbedding", "output_attr_name": "target_photo_embedding_expand_1", "query_source_item_attr": "target_photo_id_expand_1", "query_source_type": "item_attr", "save_to_common_attr": false, "shard_num": 8, "timeout_ms": 20, "cache_capacity": 1400000, "type_name": "AdCommonRemoteEmbeddingAttrEnricher"}, "get_remote_embedding_47D5AD": {"$output_item_attrs": ["target_photo_embedding_expand_2"], "client_side_shard": true, "downstream_processor": "enrich_attr_by_lua_BC6F30", "expire_time": 600, "id_converter": {"type_name": "kuibaEmbeddingIdConverter"}, "kess_service": "grpc_adMmuHetuEmbedding", "output_attr_name": "target_photo_embedding_expand_2", "query_source_item_attr": "target_photo_id_expand_2", "query_source_type": "item_attr", "save_to_common_attr": false, "shard_num": 8, "timeout_ms": 20, "cache_capacity": 1400000, "type_name": "AdCommonRemoteEmbeddingAttrEnricher"}, "get_remote_embedding_50610D": {"$output_item_attrs": ["target_photo_embedding_expand_3"], "client_side_shard": true, "downstream_processor": "enrich_attr_by_lua_BC6F30", "expire_time": 600, "id_converter": {"type_name": "kuibaEmbeddingIdConverter"}, "kess_service": "grpc_adMmuHetuEmbedding", "output_attr_name": "target_photo_embedding_expand_3", "query_source_item_attr": "target_photo_id_expand_3", "query_source_type": "item_attr", "save_to_common_attr": false, "shard_num": 8, "timeout_ms": 20, "cache_capacity": 1400000, "type_name": "AdCommonRemoteEmbeddingAttrEnricher"}, "get_remote_embedding_87ABE3": {"$output_item_attrs": ["target_photo_embedding"], "client_side_shard": true, "downstream_processor": "enrich_attr_by_lua_F28858", "expire_time": 600, "id_converter": {"type_name": "kuibaEmbeddingIdConverter"}, "kess_service": "grpc_adMmuHetuEmbedding", "output_attr_name": "target_photo_embedding", "query_source_item_attr": "photo_id", "query_source_type": "item_attr", "save_to_common_attr": false, "shard_num": 8, "timeout_ms": 20, "cache_capacity": 1400000, "type_name": "AdCommonRemoteEmbeddingAttrEnricher"}, "gsu_retriever_with_colossus_resp_v2_7BC6CB": {"$output_item_attrs": ["aid", "duration", "label", "play", "tag", "time"], "colossus_resp_attr": "colossus_output", "filter_future_attr": true, "parse_from_pb": false, "save_author_id_to_attr": "aid", "save_duration_to_attr": "duration", "save_label_to_attr": "label", "save_play_time_to_attr": "play", "save_result_to_common_attr": "colossus_pid", "save_tag_to_attr": "tag", "save_timestamp_to_attr": "time", "type_name": "CommonRecoColossusRespRetrieverV2"}, "gsu_with_index_C47D3C": {"$output_item_attrs": ["gsu_item_signs", "gsu_item_slots"], "author_id_attr": "aid", "colossus_pid_attr": "colossus_pid", "duration_attr": "duration", "item_map_list": [["346", 346, 346, 100000000], ["347", 347, 347, 100000000], ["348", 348, 348, 5000000], ["349", 349, 349, 2000], ["350", 350, 350, 1000]], "output_sign_attr": "gsu_item_signs", "output_slot_attr": "gsu_item_slots", "enable_cache": true, "play_time_attr": "play", "sign_prefix_bit_num_input": 10, "sign_prefix_bit_num_output": 12, "sorted_item_idx_attr": "sorted_item_idx", "tag_attr": "tag", "target_item": {"is_target_photo_embedding_hit": 1}, "timestamp_attr": "time", "top_n": 50, "type_name": "FusedGsuWithIndexEnricher", "use_djb2_hash64": true}, "gsu_with_index_AD721B": {"$output_item_attrs": ["gsu_item_signs_v2", "gsu_item_slots_v2"], "author_id_attr": "aid", "colossus_pid_attr": "colossus_pid", "duration_attr": "duration", "item_map_list": [["346", 380, 346, 100000000], ["347", 381, 347, 100000000], ["348", 382, 348, 5000000], ["349", 383, 349, 2000], ["350", 384, 350, 1000]], "output_sign_attr": "gsu_item_signs_v2", "output_slot_attr": "gsu_item_slots_v2", "enable_cache": true, "play_time_attr": "play", "sign_prefix_bit_num_input": 10, "sign_prefix_bit_num_output": 12, "sorted_item_idx_attr": "sorted_item_idx_v2", "tag_attr": "tag", "target_item": {"is_target_photo_embedding_hit": 1}, "timestamp_attr": "time", "top_n": 100, "type_name": "FusedGsuWithIndexEnricher", "use_djb2_hash64": true}, "pack_item_attr_F4E6F5": {"$output_item_attrs": [], "item_source": {"reco_results": true}, "mappings": [{"from_item_attr": "target_photo_embedding", "to_common_attr": "user_embedding"}], "type_name": "CommonRecoItemAttrPackEnricher"}, "split_string_12F919": {"$output_item_attrs": ["expand_photo_id_list"], "delimiters": ",", "input_item_attr": "expand_photo_id_raw", "output_item_attr": "expand_photo_id_list", "type_name": "CommonRecoStringSplitEnricher"}}, "type_name": "CommonRecoPipeline"}, "pipeline_map": {"gsu_sim29": {"__PARENT": "base_pipeline", "pipeline": ["ad_bs_retriever_F4E6F5", "get_remote_embedding_87ABE3", "enrich_attr_by_lua_F28858", "pack_item_attr_F4E6F5", "colossus_DCE14E", "gsu_retriever_with_colossus_resp_v2_7BC6CB", "fetch_tower_topn_dot_product_pxtr_E15267", "gsu_with_index_AD721B"]}}}}