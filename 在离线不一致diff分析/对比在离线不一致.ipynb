{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功加载 9 条数据记录\n", "字段名: colossus_rs_count_index_list\n", "总数: 482, 相似度阈值: 0.98973, 查询类型: 查询\n", "\n", "📋 共有 9 个LLSID\n", "\n", "============================================================\n", "LLSID: 2008795878532395297\n", "Sample ID: 2008795878532395297159767912744\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  Jaccard相似度: 1.0000\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 1.0000\n", "  交集大小: 6\n", "  并集大小: 6\n", "  在线独有元素: 0\n", "  离线独有元素: 0\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2004999240090503506\n", "Sample ID: 2004999240090503506163869319282\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  <PERSON><PERSON><PERSON>相似度: 0.2273\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.2273\n", "  交集大小: 5\n", "  并集大小: 22\n", "  在线独有元素: 0\n", "  离线独有元素: 17\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m5\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m2\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m5\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m2\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2008795351373128993\n", "Sample ID: 2008795351373128993164534956673\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  <PERSON>ac<PERSON>相似度: 0.4167\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.4167\n", "  交集大小: 5\n", "  并集大小: 12\n", "  在线独有元素: 0\n", "  离线独有元素: 7\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2008803492330542337\n", "Sample ID: 2008803492330542337160558961064\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  <PERSON>ac<PERSON>相似度: 0.4167\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.4167\n", "  交集大小: 5\n", "  并集大小: 12\n", "  在线独有元素: 0\n", "  离线独有元素: 7\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2005002320756224690\n", "Sample ID: 2005002320756224690164537517696\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  <PERSON>ac<PERSON>相似度: 0.4167\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.4167\n", "  交集大小: 5\n", "  并集大小: 12\n", "  在线独有元素: 0\n", "  离线独有元素: 7\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2004998954355232834\n", "Sample ID: 2004998954355232834159047715240\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  <PERSON><PERSON><PERSON>相似度: 0.3333\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.3333\n", "  交集大小: 4\n", "  并集大小: 12\n", "  在线独有元素: 0\n", "  离线独有元素: 8\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2008810090154406817\n", "Sample ID: 2008810090154406817154371287332\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  <PERSON><PERSON><PERSON>相似度: 0.1818\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.1818\n", "  交集大小: 2\n", "  并集大小: 11\n", "  在线独有元素: 0\n", "  离线独有元素: 9\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2005000280134998162\n", "Sample ID: 2005000280134998162160822622813\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  Jaccard相似度: 0.2750\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.2750\n", "  交集大小: 11\n", "  并集大小: 40\n", "  在线独有元素: 0\n", "  离线独有元素: 29\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m5\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m5\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m...]\n", "\n", "============================================================\n", "LLSID: 2005002397144436834\n", "Sample ID: 2005002397144436834162606067563\n", "============================================================\n", "\n", "📊 统计信息:\n", "  在线列表长度: 100\n", "  离线列表长度: 500\n", "  相同位置元素个数: 100\n", "  位置相同率: 0.2000\n", "  Jaccard相似度: 0.3372\n", "  在线覆盖率: 1.0000\n", "  离线覆盖率: 0.3372\n", "  交集大小: 29\n", "  并集大小: 86\n", "  在线独有元素: 0\n", "  离线独有元素: 57\n", "\n", "🎨 列表对比 (绿色=相同位置相同元素, 红色=不同):\n", "\n", "\u001b[1m在线列表 (长度: 100):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m4\u001b[0m...]\n", "\n", "\u001b[1m离线列表 (长度: 500):\u001b[0m\n", "[\u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m1\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m2\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m3\u001b[0m, \u001b[92m4\u001b[0m, \u001b[92m4\u001b[0m...]\n"]}], "source": ["from analyzer import DiffAnalyzer\n", "analyzer = DiffAnalyzer(\"softv2在离线不一致0911.txt\")\n", "\n", "# 加载数据\n", "if not analyzer.load_data():\n", "    print(\"❌ 数据加载失败\")\n", "\n", "# 获取所有llsid\n", "all_llsids = analyzer.get_all_llsids()\n", "print(f\"\\n📋 共有 {len(all_llsids)} 个LLSID\")\n", "for llsid in all_llsids:\n", "    analyzer.analyze_llsid(llsid)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["set() {1} 50\n"]}], "source": ["online = [9,9,7,0,13,69,8,8,29,29,11,9,12,0,0,0,0,19,19,19,19,19,12,139,139,9,29,79,79,0,19,3,99,9,0,0,0,0,13,29,39,49,9,9,9,9,9,59,1,19]\n", "offline = [9,9,7,0,13,69,8,8,29,29,11,0,0,0,9,12,12,0,19,19,19,19,19,79,79,0,9,139,139,29,19,0,13,99,0,0,3,39,6,0,59,49,9,59,59,59,59,9,29,9]\n", "offline = [39,29,28,28,9,9,25,25,49,39,39,18,6,39,29,26,39,25,7,7,3,39,39,9,2,9,3,9,9,9,19,9,39,39,39,7,2,9,5,3,2,9,9,9,9,59,52,1,4,9]\n", "online = [39,29,28,28,9,9,39,39,25,25,49,18,6,29,39,3,26,7,7,39,25,9,39,39,3,2,9,9,9,9,19,9,9,9,9,9,9,7,9,9,52,2,39,39,39,3,2,5,4,59]\n", "\n", "print(set(online) - set(offline), set(offline) - set(online), len(online))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["online = [24132561146713, 24133275021376, 24133333505376, 23830335264797, 24674012178105, 20491126180918, 21506980646085, 24692502920025, 21949761881825, 24318383480436, 21375112259837, 24097225035204, 24714490553436, 24754574094240, 22161582594037, 24559306724859, 964520561284, 24766686430601, 22284075992654, 24750167203050, 22946121455408, 24487610397512, 20927494200796, 24795739475276, 24754574094240, 23452860087635, 24566381245305, 24801745286592, 24324421723468, 23275032876304, 24746418113130, 24746418113130, 20727077552796, 20520760892801, 21906945214062, 24077512243429, 23967166458094, 23320922403134, 23320922403134, 24653413207043, 23102893885665, 24666070375869, 24779698260830, 24347890857832, 22223243452072, 24754574094240, 24754574094240, 24689182999152, 21877375341619, 20563976651793, 24754462927965, 24754462927965, 23102893885665, 23102893885665, 24633631949171, 24032546022005, 24639959468503, 24563542506830, 24692784044713, 24741011170377, 24114049652071, 24677172809377, 21923168384970, 21923168384970, 21923168384970, 21923168384970, 21926749311970, 24665470128223, 24773447946705, 24835044892743, 21167865750363, 21167865750363, 24019758445269, 22269732692928, 22269732692928, 24683424398983, 24815882870007, 24815882870007, 22643279225507, 24724390204210, 24724390204210, 24383824563142, 21855123881261, 24025892749653, 23585601633890, 22429993169557, 22429993169557, 23865853517191, 24815882870007, 24779933951549, 24779933951549, 24757515295525, 24779933951549, 24779933951549, 24784948416923, 24572457062130, 24339825552288, 24685309909705, 24393701067151, 24747723315124, 24439586855313, 24687364751124, 24541877614938, 22109834956230, 24798200396299, 23812611996680, 24902357337417, 24901472562244, 24539198193244, 24681697855743, 24284453287916, 24205982004916, 24539198193244, 24681697855743, 24466342463338, 24620667341393, 24620667341393, 22205689406136, 22205689406136, 22205689406136, 24620667341393, 24620667341393, 21949761881825, 24525703866788, 24833266514356, 24833266514356, 22333811893848, 24850780352858, 24824009030583, 24621546047940, 22994069352196, 24942139814223, 24942139814223, 584963625595, 24620667341393, 22994069352196, 21186590895555, 24497276013711, 22429993169557, 24639725560548, 21089880225711, 23067420172127, 23429409190127, 22551988473377, 24947221310337, 24770626328427, 24615046268556, 24944727831451, 22529690527978, 1063683660211, 1063683660211, 1063683660211, 23338097187255, 21052428023099, 21224219405891, 21335531150502, 24869855973863, 24929900822609, 24173701458426, 23328370112447, 24173701458426, 24173701458426, 22643279225507, 22643279225507, 24527663433647, 24527663433647, 22658608978507, 23328370112447, 23328370112447, 23512945104447, 23180033187427, 24346275512497, 22658608978507, 20765646943798, 20765646943798, 23242909421798, 20914497886055, 24906617739214, 24906617739214, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23348790791465, 23349430337465, 23348790791465, 23348790791465, 23348790791465, 23348790791465, 23348790791465, 24527663433647, 24527663433647]\n", "offline = [24527663433647, 24527663433647, 23348790791465, 23348790791465, 23348790791465, 23348790791465, 23348790791465, 23349430337465, 23348790791465, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 24906617739214, 24906617739214, 20914497886055, 23242909421798, 20765646943798, 20765646943798, 22658608978507, 24346275512497, 23180033187427, 23512945104447, 23328370112447, 23328370112447, 22658608978507, 24527663433647, 24527663433647, 22643279225507, 22643279225507, 24173701458426, 24173701458426, 23328370112447, 24173701458426, 24929900822609, 24869855973863, 21335531150502, 21224219405891, 21052428023099, 23338097187255, 1063683660211, 1063683660211, 1063683660211, 22529690527978, 24944727831451, 24615046268556, 24770626328427, 24947221310337, 22551988473377, 23429409190127, 23067420172127, 21089880225711, 24639725560548, 22429993169557, 24497276013711, 21186590895555, 22994069352196, 24620667341393, 584963625595, 24942139814223, 24942139814223, 22994069352196, 24621546047940, 24824009030583, 24850780352858, 22333811893848, 24833266514356, 24833266514356, 24525703866788, 21949761881825, 24620667341393, 24620667341393, 22205689406136, 22205689406136, 22205689406136, 24620667341393, 24620667341393, 24466342463338, 24681697855743, 24539198193244, 24205982004916, 24284453287916, 24681697855743, 24539198193244, 24901472562244, 24902357337417, 23812611996680, 24798200396299, 22109834956230, 24541877614938, 24687364751124, 24439586855313, 24747723315124, 24393701067151, 24685309909705, 24339825552288, 24572457062130, 24784948416923, 24779933951549, 24779933951549, 24757515295525, 24779933951549, 24779933951549, 24815882870007, 23865853517191, 22429993169557, 22429993169557, 23585601633890, 24025892749653, 21855123881261, 24383824563142, 24724390204210, 24724390204210, 22643279225507, 24815882870007, 24815882870007, 24683424398983, 22269732692928, 22269732692928, 24019758445269, 21167865750363, 21167865750363, 24835044892743, 24773447946705, 24665470128223, 21926749311970, 21923168384970, 21923168384970, 21923168384970, 21923168384970, 24677172809377, 24114049652071, 24741011170377, 24692784044713, 24563542506830, 24639959468503, 24032546022005, 24633631949171, 23102893885665, 23102893885665, 24754462927965, 24754462927965, 20563976651793, 21877375341619, 24689182999152, 24754574094240, 24754574094240, 22223243452072, 24347890857832, 24779698260830, 24666070375869, 23102893885665, 24653413207043, 23320922403134, 23320922403134, 23967166458094, 24077512243429, 21906945214062, 20520760892801, 20727077552796, 24746418113130, 24746418113130, 23275032876304, 24324421723468, 24801745286592, 24566381245305, 23452860087635, 24754574094240, 24795739475276, 20927494200796, 24487610397512, 22946121455408, 24750167203050, 22284075992654, 24766686430601, 964520561284, 24559306724859, 22161582594037, 24754574094240, 24714490553436, 24097225035204, 21375112259837, 24318383480436, 21949761881825, 24692502920025, 21506980646085, 20491126180918, 24674012178105, 23830335264797, 24133333505376, 24133275021376, 24132561146713]\n", "[24527663433647, 24527663433647, 23348790791465, 23348790791465, 23348790791465, 23348790791465, 23348790791465, 23349430337465, 23348790791465, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 23988047137070, 24906617739214, 24906617739214, 20914497886055, 23242909421798, 20765646943798, 20765646943798, 22658608978507, 24346275512497, 23180033187427, 23512945104447, 23328370112447, 23328370112447, 22658608978507, 24527663433647, 24527663433647, 22643279225507, 22643279225507, 24173701458426, 24173701458426, 23328370112447, 24173701458426, 24929900822609, 24869855973863, 21335531150502, 21224219405891, 21052428023099, 23338097187255, 1063683660211, 1063683660211, 1063683660211, 22529690527978, 24944727831451, 24615046268556, 24770626328427, 24947221310337, 22551988473377, 23429409190127, 23067420172127, 21089880225711, 24639725560548, 22429993169557, 24497276013711, 21186590895555, 22994069352196, 24620667341393, 584963625595, 24942139814223, 24942139814223, 22994069352196, 24621546047940, 24824009030583, 24850780352858, 22333811893848, 24833266514356, 24833266514356, 24525703866788, 21949761881825, 24620667341393, 24620667341393, 22205689406136, 22205689406136, 22205689406136, 24620667341393, 24620667341393, 24466342463338, 24681697855743, 24539198193244, 24205982004916, 24284453287916, 24681697855743, 24539198193244, 24901472562244, 24902357337417, 23812611996680, 24798200396299, 22109834956230, 24541877614938, 24687364751124, 24439586855313, 24747723315124, 24393701067151, 24685309909705, 24339825552288, 24572457062130, 24784948416923, 24779933951549, 24779933951549, 24757515295525, 24779933951549, 24779933951549, 24815882870007, 23865853517191, 22429993169557, 22429993169557, 23585601633890, 24025892749653, 21855123881261, 24383824563142, 24724390204210, 24724390204210, 22643279225507, 24815882870007, 24815882870007, 24683424398983, 22269732692928, 22269732692928, 24019758445269, 21167865750363, 21167865750363, 24835044892743, 24773447946705, 24665470128223, 21926749311970, 21923168384970, 21923168384970, 21923168384970, 21923168384970, 24677172809377, 24114049652071, 24741011170377, 24692784044713, 24563542506830, 24639959468503, 24032546022005, 24633631949171, 23102893885665, 23102893885665, 24754462927965, 24754462927965, 20563976651793, 21877375341619, 24689182999152, 24754574094240, 24754574094240, 22223243452072, 24347890857832, 24779698260830, 24666070375869, 23102893885665, 24653413207043, 23320922403134, 23320922403134, 23967166458094, 24077512243429, 21906945214062, 20520760892801, 20727077552796, 24746418113130, 24746418113130, 23275032876304, 24324421723468, 24801745286592, 24566381245305, 23452860087635, 24754574094240, 24795739475276, 20927494200796, 24487610397512, 22946121455408, 24750167203050, 22284075992654, 24766686430601, 964520561284, 24559306724859, 22161582594037, 24754574094240, 24714490553436, 24097225035204, 21375112259837, 24318383480436, 21949761881825, 24692502920025, 21506980646085, 20491126180918, 24674012178105, 23830335264797, 24133333505376, 24133275021376, 24132561146713]\n", "\n", "online == offline[::-1]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["offline = [3841570482147033279, 3841570482147033279, 3841570482147033279, 3841570482147033340, 3841570482147033143, 3841570482147033227, 3841570482147033337, 3841570482147033122, 3841570482147033214, 3841570482147033155, 3841570482147033117, 3841570482147033117, 3841570482147033155, 3841570482147033254, 3841570482147033123, 3841570482147033337, 3841570482147033309, 3841570482147033248, 3841570482147033156, 3841570482147033254, 3841570482147033250, 3841570482147033214, 3841570482147033256, 3841570482147033113, 3841570482147033254, 3841570482147033279, 3841570482147033117, 3841570482147033182, 3841570482147033291, 3841570482147033156, 3841570482147033281, 3841570482147033281, 3841570482147033256, 3841570482147033158, 3841570482147033182, 3841570482147033117, 3841570482147033099, 3841570482147033110, 3841570482147033110, 3841570482147033259, 3841570482147033183, 3841570482147033235, 3841570482147033264, 3841570482147033279, 3841570482147033254, 3841570482147033254, 3841570482147033254, 3841570482147033250, 3841570482147033235, 3841570482147033309, 3841570482147033309, 3841570482147033144, 3841570482147033309, 3841570482147033315, 3841570482147033235, 3841570482147033180, 3841570482147033183, 3841570482147033143, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033235, 3841570482147033309, 3841570482147033309, 3841570482147033180, 3841570482147033180, 3841570482147033235, 3841570482147033183, 3841570482147033183, 3841570482147033122, 3841570482147033143, 3841570482147033143, 3841570482147033117, 3841570482147033291, 3841570482147033291, 3841570482147033235, 3841570482147033309, 3841570482147033143, 3841570482147033183, 3841570482147033117, 3841570482147033117, 3841570482147033341, 3841570482147033143, 3841570482147033218, 3841570482147033218, 3841570482147033238, 3841570482147033218, 3841570482147033218, 3841570482147033185, 3841570482147033286, 3841570482147033306, 3841570482147033175, 3841570482147033306, 3841570482147033340, 3841570482147033340, 3841570482147033306, 3841570482147033340, 3841570482147033216, 3841570482147033306, 3841570482147033311, 3841570482147033105, 3841570482147033309, 3841570482147033143, 3841570482147033214, 3841570482147033175, 3841570482147033175, 3841570482147033143, 3841570482147033214, 3841570482147033175, 3841570482147033105, 3841570482147033105, 3841570482147033178, 3841570482147033178, 3841570482147033178, 3841570482147033105, 3841570482147033105, 3841570482147033214, 3841570482147033309, 3841570482147033275, 3841570482147033275, 3841570482147033303, 3841570482147033309, 3841570482147033254, 3841570482147033216, 3841570482147033168, 3841570482147033254, 3841570482147033254, 3841570482147033309, 3841570482147033105, 3841570482147033168, 3841570482147033216, 3841570482147033127, 3841570482147033117, 3841570482147033257, 3841570482147033127, 3841570482147033127, 3841570482147033127, 3841570482147033227, 3841570482147033190, 3841570482147033235, 3841570482147033190, 3841570482147033309, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033180, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033236, 3841570482147033236, 3841570482147033236, 3841570482147033236, 3841570482147033335, 3841570482147033335, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033143, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033117, 3841570482147033117]\n", "online = [3841570482147033279, 3841570482147033279, 3841570482147033279, 3841570482147033340, 3841570482147033143, 3841570482147033227, 3841570482147033337, 3841570482147033122, 3841570482147033214, 3841570482147033155, 3841570482147033117, 3841570482147033117, 3841570482147033155, 3841570482147033254, 3841570482147033123, 3841570482147033337, 3841570482147033309, 3841570482147033248, 3841570482147033156, 3841570482147033254, 3841570482147033250, 3841570482147033214, 3841570482147033256, 3841570482147033113, 3841570482147033254, 3841570482147033279, 3841570482147033117, 3841570482147033182, 3841570482147033291, 3841570482147033156, 3841570482147033281, 3841570482147033281, 3841570482147033256, 3841570482147033158, 3841570482147033182, 3841570482147033117, 3841570482147033099, 3841570482147033110, 3841570482147033110, 3841570482147033259, 3841570482147033183, 3841570482147033235, 3841570482147033264, 3841570482147033279, 3841570482147033254, 3841570482147033254, 3841570482147033254, 3841570482147033250, 3841570482147033235, 3841570482147033309, 3841570482147033309, 3841570482147033144, 3841570482147033309, 3841570482147033315, 3841570482147033235, 3841570482147033180, 3841570482147033183, 3841570482147033143, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033183, 3841570482147033235, 3841570482147033309, 3841570482147033309, 3841570482147033180, 3841570482147033180, 3841570482147033235, 3841570482147033183, 3841570482147033183, 3841570482147033122, 3841570482147033143, 3841570482147033143, 3841570482147033117, 3841570482147033291, 3841570482147033291, 3841570482147033235, 3841570482147033309, 3841570482147033143, 3841570482147033183, 3841570482147033117, 3841570482147033117, 3841570482147033341, 3841570482147033143, 3841570482147033218, 3841570482147033218, 3841570482147033238, 3841570482147033218, 3841570482147033218, 3841570482147033185, 3841570482147033286, 3841570482147033306, 3841570482147033175, 3841570482147033306, 3841570482147033340, 3841570482147033340, 3841570482147033306, 3841570482147033340, 3841570482147033216, 3841570482147033306, 3841570482147033311, 3841570482147033105, 3841570482147033309, 3841570482147033143, 3841570482147033214, 3841570482147033175, 3841570482147033175, 3841570482147033143, 3841570482147033214, 3841570482147033175, 3841570482147033105, 3841570482147033105, 3841570482147033178, 3841570482147033178, 3841570482147033178, 3841570482147033105, 3841570482147033105, 3841570482147033214, 3841570482147033309, 3841570482147033275, 3841570482147033275, 3841570482147033303, 3841570482147033309, 3841570482147033254, 3841570482147033216, 3841570482147033168, 3841570482147033254, 3841570482147033254, 3841570482147033309, 3841570482147033105, 3841570482147033168, 3841570482147033216, 3841570482147033127, 3841570482147033117, 3841570482147033257, 3841570482147033127, 3841570482147033127, 3841570482147033127, 3841570482147033227, 3841570482147033190, 3841570482147033235, 3841570482147033190, 3841570482147033309, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033180, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033117, 3841570482147033236, 3841570482147033236, 3841570482147033236, 3841570482147033236, 3841570482147033335, 3841570482147033335, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033143, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033291, 3841570482147033117, 3841570482147033117]\n", "\n", "online == offline"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2008797016342673537159498504304 online 200 vs. offline 200, same200\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[52039,35586,35586,35586,35586,20750,50629,50444,50629,50489,50629,50444,50629,50629,50629,50629,50629,50629,50629,50629,7924,7730,7915,7915,63993,63993,7915,7915,7924,7924,7924,7730,7730,7730,7730,7790,7915,63993,63993,63993,22040,22040,52141,20758,50629,50629,50629,50629,50629,50629,7730,7924,7730,50629,50629,50629,50629,50629,50629,50629,50629,50629,50629,50629,50629,7730,7915,7790,7761,7924,7771,7771,50629,22175,63959,63959,7915,50629,50629,50629,50629,50629,50629,50629,7761,7761,7790,7771,7761,7730,7730,7915,7915,7915,7771,7915,45185,45191,7730,7730,7924,7790,7771,7761,7790,7790,7924,7915,7915,7771,7771,9591,7924,7771,7771,7771,7790,7771,7771,7771,7771,19647,19647,19647,7771,7771,7771,7730,7924,7924,7924,7924,7771,765,7771,7771,7771,7790,7790,7771,35601,35586,35601,35601,35601,35661,35586,35586,35757,35757,7915,7924,7761,7790,37563,37416,7915,7761,7761,7761,7771,7686,7686,7686,32050,32050,32244,32050,32050,33344,7778,7915,7771,27364,7771,33344,33344,32050,33344,7856,7778,33344,33344,14285,57508,42477,7856,7500,7500,15410,46580,7856,26754,35714,64695,14616,33526,7771,7771,7871]\n", "[7871,7771,7771,33526,14616,64695,35714,26754,7856,46580,15410,7500,7500,7856,42477,57508,14285,33344,33344,7778,7856,33344,32050,33344,33344,7771,27364,7771,7915,7778,33344,32050,32050,32244,32050,32050,7686,7686,7686,7771,7761,7761,7761,7915,37416,37563,7790,7761,7924,7915,35757,35757,35586,35586,35661,35601,35601,35601,35586,35601,7771,7790,7790,7771,7771,7771,765,7771,7924,7924,7924,7924,7730,7771,7771,7771,19647,19647,19647,7771,7771,7771,7771,7790,7771,7771,7771,7924,9591,7771,7771,7915,7915,7924,7790,7790,7761,7771,7790,7924,7730,7730,45191,45185,7915,7771,7915,7915,7915,7730,7730,7761,7771,7790,7761,7761,50629,50629,50629,50629,50629,50629,50629,7915,63959,63959,22175,50629,7771,7771,7924,7761,7790,7915,7730,50629,50629,50629,50629,50629,50629,50629,50629,50629,50629,50629,50629,7730,7924,7730,50629,50629,50629,50629,50629,50629,20758,52141,22040,22040,63993,63993,63993,7915,7790,7730,7730,7730,7730,7924,7924,7924,7915,7915,63993,63993,7915,7915,7730,7924,50629,50629,50629,50629,50629,50629,50629,50629,50444,50629,50489,50629,50444,50629,20750,35586,35586,35586,35586,52039]\n", "2008731852448144689159316799265 online 196 vs. offline 196, same196\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[36855,36855,36855,41155,55916,53801,36855,37954,32957,23700,20590,20695,9740,9857,41155,13353,32941,36674,15345,36674,9740,36725,32887,9887,9807,9887,46558,22750,34029,36674,16962,22750,17193,5101,64753,57781,33371,38466,36855,46446,57791,36674,36674,9757,64753,60586,33399,33344,18093,32887,64644,36674,36674,36674,36674,36674,36674,36674,33399,55842,55842,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,33399,33349,36674,64753,33378,56598,38466,36674,36674,36674,36674,16962,4487,4487,32502,9962,32502,9884,14302,56590,9857,40046,40046,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,16962,9919,17281,33399,33349,55821,9838,36674,37954,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,9884,46381,15097,15097,36674,36674,36674,37158,32818,32818,32818,36674,36674,36674,36674,6891,26178,36674,36674,36674,59328,36674,36674,36674,64675,20637,57627,57627,57627,57627,13703,13659,13703,13659,13703,13703,13659,13659,13703,7500,57627,64008,7658,7539,37158,7539,15298,9847,29425,33390,36674,56003,56003,36674,14967,17031,16962,32451,9962,27719,37821,9740,64550,64550,36674,36674,17133]\n", "[17133,36674,36674,64550,64550,9740,37821,27719,9962,32451,16962,17031,14967,36674,56003,56003,36674,33390,29425,9847,15298,7539,37158,7539,7658,64008,57627,7500,13703,13659,13659,13703,13703,13659,13703,13659,13703,57627,57627,57627,57627,20637,64675,36674,36674,36674,59328,36674,36674,36674,26178,6891,36674,36674,36674,36674,32818,32818,32818,37158,36674,36674,36674,15097,15097,46381,9884,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,37954,36674,9838,55821,33349,33399,17281,9919,16962,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,40046,40046,9857,56590,14302,9884,32502,9962,32502,4487,4487,16962,36674,36674,36674,36674,38466,56598,33378,64753,36674,33349,33399,36674,36674,36674,36674,36674,36674,36674,36674,36674,36674,55842,55842,33399,36674,36674,36674,36674,36674,36674,36674,64644,32887,18093,33344,33399,60586,64753,9757,36674,36674,57791,46446,36855,38466,33371,57781,64753,5101,17193,22750,16962,36674,34029,22750,46558,9887,9807,9887,32887,36725,9740,36674,15345,36674,32941,13353,41155,9857,9740,20695,20590,23700,32957,37954,36855,53801,55916,41155,36855,36855,36855]\n", "2004949070026182066154751280730 online 200 vs. offline 200, same200\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[55769,15117,24354,45460,37740,37740,7412,37740,52770,52770,37740,37740,37670,7402,57325,32437,32437,32437,32437,52748,13639,21588,37723,7402,7402,7402,7402,35690,46253,46656,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,28443,24360,55940,35690,13344,37869,24302,37670,37670,13802,5628,35586,32393,23707,13467,13467,13908,13908,13832,13908,13908,13908,13908,13908,13453,14190,14190,22280,35690,35690,35690,35690,35690,35690,35690,35690,13908,13908,35690,13859,13859,8968,8968,56757,56604,56757,24403,24403,24504,35690,63993,63993,35690,24567,48653,48653,48653,48854,48736,2082,48830,48830,48830,13432,63993,24457,24453,7359,7359,35690,35690,35690,35690,35690,35690,35690,14061,14061,24813,24813,4791,35690,56430,56430,56430,24347,24354,24354,24333,24527,24527,13396,63993,13320,3883,35690,35690,35690,13550,13550,24373,7287,13396,13396,13639,13639,52192,46084,15341,56582,56582,56582,32327,32327,56582,32327,13639,13639,32327,7251,37648,7251,35690,35690,7251,7251,21624,32327,23576,23576,23576,56333,17472,23576,63827,63773,23576,7327,24174,58416,7341,7349,23576,26722,7327,7327,7327,7327,7327,7368,13639]\n", "[13639,7368,7327,7327,7327,7327,7327,26722,23576,7349,7341,58416,24174,7327,23576,63773,63827,23576,17472,56333,23576,23576,23576,32327,21624,7251,7251,35690,35690,7251,37648,7251,32327,13639,13639,32327,56582,32327,32327,56582,56582,56582,15341,46084,52192,13639,13639,13396,13396,7287,24373,13550,13550,35690,35690,35690,3883,13320,63993,13396,24527,24527,24333,24354,24354,24347,56430,56430,56430,35690,4791,24813,24813,14061,14061,35690,35690,35690,35690,35690,35690,35690,7359,7359,24453,24457,63993,13432,48830,48830,48830,2082,48736,48854,48653,48653,48653,24567,35690,63993,63993,35690,24504,24403,24403,56757,56604,56757,8968,8968,13859,13859,35690,13908,13908,35690,35690,35690,35690,35690,35690,35690,35690,22280,14190,14190,13453,13908,13908,13908,13908,13908,13832,13908,13908,13467,13467,23707,32393,35586,5628,13802,37670,37670,24302,37869,13344,35690,55940,24360,28443,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,35690,46656,46253,35690,7402,7402,7402,7402,37723,21588,13639,52748,32437,32437,32437,32437,57325,7402,37670,37740,37740,52770,52770,37740,7412,37740,37740,45460,24354,15117,55769]\n", "2004948307631676658159425651440 online 178 vs. offline 178, same82\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, True, False, False, False, False, False, False, True, True, False, False, False, False, False, True, False, True, True, False, False, False, False, False, False, False, False, True, False, False, True, False, False, True, False, False, False, False, False, False, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, True, False, False, False, False, False, False, True, True, False, False, True, False, False, False, True, False, True, False, False, False, False, False, False, False, False, False, False, False, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[17458,26270,16798,16798,39098,22325,22762,3924,41083,22458,21588,21520,59787,5515,5510,49654,55952,5384,32375,42615,43280,24231,13837,5562,59784,5623,6600,32258,5388,5388,5410,8865,22545,5388,58650,5583,5583,5583,32429,59275,37803,16798,10508,10508,5565,5565,5565,5388,5562,5565,5562,5388,5562,27184,63792,63792,10923,18018,18018,44010,51995,51995,18018,55867,56582,12880,13136,34229,704,39670,577,32375,32375,44162,42653,42615,42677,35703,4469,32378,32412,32294,32334,4437,24114,24114,32334,42644,24509,26241,27719,24333,24328,24333,24428,24557,24567,24439,24338,24338,24475,24567,40741,63863,52049,24338,24338,24338,55942,11600,11600,7349,56022,22467,22467,37740,37740,38925,55890,37742,58222,37740,55821,55894,18093,55890,55823,37742,37742,58650,35694,17281,17313,17281,35694,4369,40826,16769,22578,22762,63968,37742,35703,37878,37742,37742,37742,37742,536,29262,16962,27500,42606,4402,24308,4369,17652,14151,24231,24231,24308,16877,16877,16690,16877,16813,16711,24081,24081,24237,24237,24081,28807,28807,28874,24081,17518,28807]\n", "[28807,17518,24081,28874,28807,28807,24081,24237,24237,24081,24081,16711,16813,16877,16690,16877,16877,24308,24231,24231,14151,17652,4369,24308,4402,42606,27500,16962,29262,536,37742,37742,37742,37742,37878,35703,37742,63968,22762,22578,16769,40826,4369,35694,17281,17313,17281,35694,58650,37742,55823,55890,18093,55894,55821,37740,58222,37742,55890,38925,37740,37740,22467,22467,56022,7349,11600,11600,55942,24338,24338,24338,52049,63863,40741,24567,24475,24338,24338,24439,24567,24557,24428,24333,24328,24333,27719,26241,24509,42644,32334,24114,24114,4437,32334,32294,32412,32378,4469,35703,42677,42615,42653,44162,32375,32375,577,39670,704,34229,13136,12880,56582,55867,18018,51995,51995,44010,18018,18018,10923,63792,63792,27184,5562,5388,5562,5565,5562,5388,5565,5565,5565,10508,10508,16798,37803,59275,32429,5583,5583,5583,58650,5388,22545,8865,5410,5388,5388,32258,6600,5623,59784,5562,13837,24231,43280,42615,32375,5384,55952,49654,20578,5510,5515,59787,21520,21588,22458,41083,3924,22762,22325,39098,16798,16798,26270,17458]\n", "2008742687426927649159173871835 online 196 vs. offline 196, same196\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[33524,33380,33380,43932,43932,43932,43932,38049,38050,38049,38049,33524,38049,38049,7559,7668,33286,33330,18135,17923,17923,7453,7453,18115,26853,56255,33012,7579,37912,37929,23286,15092,46455,7579,46364,56000,14228,14228,14228,14228,28397,12594,12605,12635,7559,63497,63497,63497,63497,37364,12593,12725,26093,26093,25880,12781,19446,12677,28397,28341,12594,28397,63497,28397,28333,28210,28397,7579,64013,30452,35931,36084,36084,36084,35959,35890,35975,26241,49702,53698,57844,53614,26853,26853,53750,53750,53614,53531,49652,5315,35509,15224,36084,35931,33330,19229,19229,55688,64525,64525,64525,64525,51446,7475,55769,57837,57837,57837,57719,57844,55769,55769,55769,56211,57837,35931,35931,7668,57837,60507,24417,24417,17435,57773,6706,34221,28397,28397,12654,37949,26652,33808,58868,18164,18164,46455,58662,32903,42733,14501,32478,46433,25023,5161,12788,12594,28210,12788,12788,12629,28341,64239,7515,38036,32478,32478,32478,46349,12788,25023,12635,28270,28270,28333,56782,35714,7579,12635,59297,59297,12788,12788,1999,63993,63993,63993,12781,12781,12788,12568,24951,25078,24870,35765,14857,14857,14958,55769,28344,25078,52149,14983,57761,14983,14983,33524]\n", "[33524,14983,14983,57761,14983,52149,25078,28344,55769,14958,14857,14857,35765,24870,25078,24951,12568,12788,12781,12781,63993,63993,63993,1999,12788,12788,59297,59297,12635,7579,35714,56782,28333,28270,28270,12635,25023,12788,46349,32478,32478,32478,38036,7515,64239,28341,12629,12788,12788,28210,12594,12788,5161,25023,46433,32478,14501,42733,32903,58662,46455,18164,18164,58868,33808,26652,37949,12654,28397,28397,34221,6706,57773,17435,24417,24417,60507,57837,7668,35931,35931,57837,56211,55769,55769,55769,57844,57719,57837,57837,57837,55769,7475,51446,64525,64525,64525,64525,55688,19229,19229,33330,35931,36084,15224,35509,5315,49652,53531,53614,53750,53750,26853,26853,53614,57844,53698,49702,26241,35975,35890,35959,36084,36084,36084,35931,30452,64013,7579,28397,28210,28333,28397,63497,28397,12594,28341,28397,12677,19446,12781,25880,26093,26093,12725,12593,37364,63497,63497,63497,63497,7559,12635,12605,12594,28397,14228,14228,14228,14228,56000,46364,7579,46455,15092,23286,37929,37912,7579,33012,56255,26853,18115,7453,7453,17923,17923,18135,33330,33286,7668,7559,38049,38049,33524,38049,38049,38050,38049,43932,43932,43932,43932,33380,33380,33524]\n", "2008742687426927649159201941210 online 196 vs. offline 196, same196\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[33524,33380,33380,43932,43932,43932,43932,38049,38050,38049,38049,33524,38049,38049,7559,7668,33286,33330,18135,17923,17923,7453,7453,18115,26853,56255,33012,7579,37912,37929,23286,15092,46455,7579,46364,56000,14228,14228,14228,14228,28397,12594,12605,12635,7559,63497,63497,63497,63497,37364,12593,12725,26093,26093,25880,12781,19446,12677,28397,28341,12594,28397,63497,28397,28333,28210,28397,7579,64013,30452,35931,36084,36084,36084,35959,35890,35975,26241,49702,53698,57844,53614,26853,26853,53750,53750,53614,53531,49652,5315,35509,15224,36084,35931,33330,19229,19229,55688,64525,64525,64525,64525,51446,7475,55769,57837,57837,57837,57719,57844,55769,55769,55769,56211,57837,35931,35931,7668,57837,60507,24417,24417,17435,57773,6706,34221,28397,28397,12654,37949,26652,33808,58868,18164,18164,46455,58662,32903,42733,14501,32478,46433,25023,5161,12788,12594,28210,12788,12788,12629,28341,64239,7515,38036,32478,32478,32478,46349,12788,25023,12635,28270,28270,28333,56782,35714,7579,12635,59297,59297,12788,12788,1999,63993,63993,63993,12781,12781,12788,12568,24951,25078,24870,35765,14857,14857,14958,55769,28344,25078,52149,14983,57761,14983,14983,33524]\n", "[33524,14983,14983,57761,14983,52149,25078,28344,55769,14958,14857,14857,35765,24870,25078,24951,12568,12788,12781,12781,63993,63993,63993,1999,12788,12788,59297,59297,12635,7579,35714,56782,28333,28270,28270,12635,25023,12788,46349,32478,32478,32478,38036,7515,64239,28341,12629,12788,12788,28210,12594,12788,5161,25023,46433,32478,14501,42733,32903,58662,46455,18164,18164,58868,33808,26652,37949,12654,28397,28397,34221,6706,57773,17435,24417,24417,60507,57837,7668,35931,35931,57837,56211,55769,55769,55769,57844,57719,57837,57837,57837,55769,7475,51446,64525,64525,64525,64525,55688,19229,19229,33330,35931,36084,15224,35509,5315,49652,53531,53614,53750,53750,26853,26853,53614,57844,53698,49702,26241,35975,35890,35959,36084,36084,36084,35931,30452,64013,7579,28397,28210,28333,28397,63497,28397,12594,28341,28397,12677,19446,12781,25880,26093,26093,12725,12593,37364,63497,63497,63497,63497,7559,12635,12605,12594,28397,14228,14228,14228,14228,56000,46364,7579,46455,15092,23286,37929,37912,7579,33012,56255,26853,18115,7453,7453,17923,17923,18135,33330,33286,7668,7559,38049,38049,33524,38049,38049,38050,38049,43932,43932,43932,43932,33380,33380,33524]\n", "2008742687426927649156646280433 online 196 vs. offline 196, same196\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[33524,33380,33380,43932,43932,43932,43932,38049,38050,38049,38049,33524,38049,38049,7559,7668,33286,33330,18135,17923,17923,7453,7453,18115,26853,56255,33012,7579,37912,37929,23286,15092,46455,7579,46364,56000,14228,14228,14228,14228,28397,12594,12605,12635,7559,63497,63497,63497,63497,37364,12593,12725,26093,26093,25880,12781,19446,12677,28397,28341,12594,28397,63497,28397,28333,28210,28397,7579,64013,30452,35931,36084,36084,36084,35959,35890,35975,26241,49702,53698,57844,53614,26853,26853,53750,53750,53614,53531,49652,5315,35509,15224,36084,35931,33330,19229,19229,55688,64525,64525,64525,64525,51446,7475,55769,57837,57837,57837,57719,57844,55769,55769,55769,56211,57837,35931,35931,7668,57837,60507,24417,24417,17435,57773,6706,34221,28397,28397,12654,37949,26652,33808,58868,18164,18164,46455,58662,32903,42733,14501,32478,46433,25023,5161,12788,12594,28210,12788,12788,12629,28341,64239,7515,38036,32478,32478,32478,46349,12788,25023,12635,28270,28270,28333,56782,35714,7579,12635,59297,59297,12788,12788,1999,63993,63993,63993,12781,12781,12788,12568,24951,25078,24870,35765,14857,14857,14958,55769,28344,25078,52149,14983,57761,14983,14983,33524]\n", "[33524,14983,14983,57761,14983,52149,25078,28344,55769,14958,14857,14857,35765,24870,25078,24951,12568,12788,12781,12781,63993,63993,63993,1999,12788,12788,59297,59297,12635,7579,35714,56782,28333,28270,28270,12635,25023,12788,46349,32478,32478,32478,38036,7515,64239,28341,12629,12788,12788,28210,12594,12788,5161,25023,46433,32478,14501,42733,32903,58662,46455,18164,18164,58868,33808,26652,37949,12654,28397,28397,34221,6706,57773,17435,24417,24417,60507,57837,7668,35931,35931,57837,56211,55769,55769,55769,57844,57719,57837,57837,57837,55769,7475,51446,64525,64525,64525,64525,55688,19229,19229,33330,35931,36084,15224,35509,5315,49652,53531,53614,53750,53750,26853,26853,53614,57844,53698,49702,26241,35975,35890,35959,36084,36084,36084,35931,30452,64013,7579,28397,28210,28333,28397,63497,28397,12594,28341,28397,12677,19446,12781,25880,26093,26093,12725,12593,37364,63497,63497,63497,63497,7559,12635,12605,12594,28397,14228,14228,14228,14228,56000,46364,7579,46455,15092,23286,37929,37912,7579,33012,56255,26853,18115,7453,7453,17923,17923,18135,33330,33286,7668,7559,38049,38049,33524,38049,38049,38050,38049,43932,43932,43932,43932,33380,33380,33524]\n", "2004946383463520834159195383712 online 11 vs. offline 11, same11\n", "[True, True, True, True, True, True, True, True, True, True, True]\n", "[19354,19359,19371,49653,17509,33003,32983,19261,18482,17970,32841]\n", "[32841,17970,18482,19261,32983,33003,17509,49653,19371,19359,19354]\n", "2008762965642993553158929260206 online 200 vs. offline 200, same200\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[13678,32627,3957,42486,27364,5742,27364,27758,27758,14107,33870,50182,50182,50182,50182,27773,27698,62289,62289,57325,57325,27184,27184,32502,13678,27758,27758,2588,2588,14209,42486,57325,22887,48573,17527,37869,58231,58358,45814,45814,562,22894,22894,5742,5742,9591,9591,9591,9591,9591,45814,45802,45802,45802,63868,45802,45802,45690,45690,2588,37869,37869,56667,62333,62333,62333,62333,51069,62258,62258,60963,562,637,27773,62258,22834,536,536,536,536,56822,63863,13678,13678,63825,29047,13618,13618,7293,7293,52049,13814,13678,13678,53341,62258,53341,51025,51025,51025,51025,62249,51025,62249,62333,51025,51025,11757,11544,62333,2588,62333,62389,62381,62389,62389,53341,53491,56212,11570,10882,536,536,51025,53341,53341,53341,27307,53341,62399,22135,11600,53341,46495,50251,62399,27184,591,62399,43650,6406,58596,58596,29101,29101,62333,62333,43645,53341,40817,40817,40785,40785,33895,37740,37740,7597,40833,40833,56509,53437,62333,57138,62333,57138,57138,56598,32549,32549,53437,53341,53437,53437,53437,62269,62269,43645,42871,56702,56702,56702,56506,21181,56506,33890,56506,50301,50301,50301,32549,32549,58596,14210,24114,62333,53272,53341,14210,40858,22909]\n", "[22909,40858,14210,53341,53272,62333,24114,14210,58596,32549,32549,50301,50301,50301,56506,33890,56506,21181,56506,56702,56702,56702,42871,43645,62269,62269,53437,53437,53437,53341,53437,32549,32549,56598,57138,57138,62333,57138,62333,53437,56509,40833,40833,7597,37740,37740,33895,40785,40785,40817,40817,53341,43645,62333,62333,29101,29101,58596,58596,6406,43650,62399,591,27184,62399,50251,46495,53341,11600,22135,62399,53341,27307,53341,53341,53341,51025,536,536,10882,11570,56212,53491,53341,62389,62389,62381,62389,62333,2588,62333,11544,11757,51025,51025,62333,62249,51025,62249,51025,51025,51025,51025,53341,62258,53341,13678,13678,13814,52049,7293,7293,13618,13618,29047,63825,13678,13678,63863,56822,536,536,536,536,22834,62258,27773,637,562,60963,62258,62258,51069,62333,62333,62333,62333,56667,37869,37869,2588,45690,45690,45802,45802,63868,45802,45802,45802,45814,9591,9591,9591,9591,9591,5742,5742,22894,22894,562,45814,45814,58358,58231,37869,17527,48573,22887,57325,42486,14209,2588,2588,27758,27758,13678,32502,27184,27184,57325,57325,62289,62289,27698,27773,50182,50182,50182,50182,33870,14107,27758,27758,27364,5742,27364,42486,3957,32627,13678]\n", "2004947349847267298154985069522 online 128 vs. offline 128, same128\n", "[True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True, True]\n", "[63784,17645,9529,8937,8937,8937,9509,9509,9509,22951,62289,2926,20304,4462,9639,16782,9509,43557,43557,62372,62289,9509,9509,9509,43696,43581,17458,43581,43557,36433,43618,2685,15374,15374,7904,8945,50957,8945,22951,62289,43639,26722,43687,43687,51057,51057,53363,9712,51086,22873,9522,22873,22873,22951,22951,22951,21102,32375,22829,27551,50335,50335,25906,19319,32883,25883,8643,50335,50257,57373,3697,50966,20264,26790,65012,12525,12349,12338,12401,43687,43687,9639,36519,36519,36519,24212,22951,9513,22951,22951,29490,52087,51086,51086,51120,25975,51086,59388,39321,39321,39321,19238,48397,48573,55769,55769,19229,50205,50205,39385,32472,32472,32472,19238,40817,26736,26736,26736,48425,48545,12301,12447,12532,12532,12522,12469,12525,63978]\n", "[63978,12525,12469,12522,12532,12532,12447,12301,48545,48425,26736,26736,26736,40817,19238,32472,32472,32472,39385,50205,50205,19229,55769,55769,48573,48397,19238,39321,39321,39321,59388,51086,25975,51120,51086,51086,52087,29490,22951,22951,9513,22951,24212,36519,36519,36519,9639,43687,43687,12401,12338,12349,12525,65012,26790,20264,50966,3697,57373,50257,50335,8643,25883,32883,19319,25906,50335,50335,27551,22829,32375,21102,22951,22951,22951,22873,22873,9522,22873,51086,9712,53363,51057,51057,43687,43687,26722,43639,62289,22951,8945,50957,8945,7904,15374,15374,2685,43618,36433,43557,43581,17458,43581,43696,9509,9509,9509,62289,62372,43557,43557,9509,16782,9639,4462,20304,2926,62289,22951,9509,9509,9509,8937,8937,8937,9529,17645,63784]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/kg/s79p1dr57p5fn8xrwy17cbcw0000gn/T/ipykernel_19576/1314944236.py:4: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  row2 = eval(row[2])\n", "/var/folders/kg/s79p1dr57p5fn8xrwy17cbcw0000gn/T/ipykernel_19576/1314944236.py:5: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  row3 = eval(row[3])[::-1]\n", "/var/folders/kg/s79p1dr57p5fn8xrwy17cbcw0000gn/T/ipykernel_19576/1314944236.py:7: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print(row[0], f\"online {len(row2)} vs. offline {len(row3)}, same{sum(same)}\")\n", "/var/folders/kg/s79p1dr57p5fn8xrwy17cbcw0000gn/T/ipykernel_19576/1314944236.py:9: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print(row[2])\n", "/var/folders/kg/s79p1dr57p5fn8xrwy17cbcw0000gn/T/ipykernel_19576/1314944236.py:10: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print(row[3])\n"]}], "source": ["import pandas as pd\n", "df = pd.read_csv('/Users/<USER>/Downloads/user_mmu_854.csv')\n", "for idx,row in df.iterrows():\n", "    row2 = eval(row[2])\n", "    row3 = eval(row[3])[::-1]\n", "    same = [row2[i] == row3[i] for i in range(min(len(row2), len(row3)))]\n", "    print(row[0], f\"online {len(row2)} vs. offline {len(row3)}, same{sum(same)}\")\n", "    print(same)\n", "    print(row[2])\n", "    print(row[3])\n", "    "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}