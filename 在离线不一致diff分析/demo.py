#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DiffAnalyzer 演示脚本
展示如何使用 DiffAnalyzer 类分析在离线不一致数据
"""

from analyzer import DiffAnalyzer


def demo_basic_usage():
    """基本使用演示"""
    print("🚀 DiffAnalyzer 基本使用演示")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = DiffAnalyzer("softv2在离线不一致0909.txt")
    
    # 加载数据
    if not analyzer.load_data():
        print("❌ 数据加载失败")
        return
    
    # 获取所有llsid
    all_llsids = analyzer.get_all_llsids()
    print(f"\n📋 共有 {len(all_llsids)} 个LLSID")
    
    # 分析特定llsid
    if all_llsids:
        target_llsid = all_llsids[0]  # 分析第一个
        print(f"\n🔍 详细分析LLSID: {target_llsid}")
        result = analyzer.analyze_llsid(target_llsid, show_comparison=True, max_display_length=30)
        
        if result:
            print(f"\n✅ 分析完成，位置相同率: {result['metrics']['position_similarity']:.4f}")


def demo_batch_analysis():
    """批量分析演示"""
    print("\n\n🔄 批量分析演示")
    print("=" * 60)
    
    analyzer = DiffAnalyzer("softv2在离线不一致0909.txt")
    
    if not analyzer.load_data():
        return
    
    # 批量分析前5个llsid
    all_llsids = analyzer.get_all_llsids()
    sample_llsids = all_llsids[:5]
    
    print(f"\n📊 批量分析前 {len(sample_llsids)} 个LLSID...")
    results = analyzer.batch_analyze(sample_llsids, show_details=False)
    
    # 显示结果摘要
    print(f"\n📈 批量分析结果摘要:")
    for llsid, result in results.items():
        metrics = result['metrics']
        print(f"  {llsid}: 位置相同率={metrics['position_similarity']:.4f}, "
              f"Jaccard相似度={metrics['jaccard_similarity']:.4f}")


def demo_statistics():
    """统计分析演示"""
    print("\n\n📊 统计分析演示")
    print("=" * 60)
    
    analyzer = DiffAnalyzer("softv2在离线不一致0909.txt")
    
    if not analyzer.load_data():
        return
    
    # 打印汇总统计
    analyzer.print_summary_statistics()
    
    # 找到最相似和最不相似的
    print(f"\n🏆 最相似的3个LLSID:")
    most_similar = analyzer.find_most_similar(3)
    for i, (llsid, similarity) in enumerate(most_similar, 1):
        print(f"  {i}. {llsid}: {similarity:.4f}")
    
    print(f"\n💥 最不相似的3个LLSID:")
    most_different = analyzer.find_most_different(3)
    for i, (llsid, similarity) in enumerate(most_different, 1):
        print(f"  {i}. {llsid}: {similarity:.4f}")


def demo_compare_files():
    """比较不同数据文件"""
    print("\n\n🔄 比较不同数据文件")
    print("=" * 60)
    
    files = ["softv2在离线不一致0909.txt", "softv2在离线不一致0911.txt"]
    
    for file_path in files:
        print(f"\n📁 分析文件: {file_path}")
        analyzer = DiffAnalyzer(file_path)
        
        if analyzer.load_data():
            all_llsids = analyzer.get_all_llsids()
            print(f"  - 数据条数: {len(all_llsids)}")
            print(f"  - 字段名: {analyzer.field_name}")
            print(f"  - 相似度阈值: {analyzer.similarity_threshold}")
            
            # 快速统计
            results = analyzer.batch_analyze(show_details=False)
            if results:
                similarities = [r['metrics']['position_similarity'] for r in results.values()]
                avg_similarity = sum(similarities) / len(similarities)
                print(f"  - 平均位置相同率: {avg_similarity:.4f}")
        else:
            print(f"  ❌ 文件加载失败")


def demo_interactive():
    """交互式演示"""
    print("\n\n🎮 交互式演示")
    print("=" * 60)
    
    analyzer = DiffAnalyzer("softv2在离线不一致0909.txt")
    
    if not analyzer.load_data():
        return
    
    all_llsids = analyzer.get_all_llsids()
    print(f"\n可用的LLSID数量: {len(all_llsids)}")
    print("前10个LLSID:")
    for i, llsid in enumerate(all_llsids[:10], 1):
        print(f"  {i}. {llsid}")
    
    while True:
        try:
            user_input = input(f"\n请输入要分析的LLSID (或输入 'quit' 退出): ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见!")
                break
            
            if user_input in all_llsids:
                print(f"\n🔍 分析LLSID: {user_input}")
                analyzer.analyze_llsid(user_input, show_comparison=True, max_display_length=20)
            else:
                print(f"❌ 未找到LLSID: {user_input}")
                print("提示: 请从上面列出的LLSID中选择")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


def main():
    """主函数"""
    print("🎯 DiffAnalyzer 完整演示")
    print("=" * 80)
    
    # 运行各种演示
    demo_basic_usage()
    demo_batch_analysis()
    demo_statistics()
    demo_compare_files()
    
    # 询问是否运行交互式演示
    try:
        response = input("\n是否运行交互式演示? (y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            demo_interactive()
    except KeyboardInterrupt:
        print("\n👋 演示结束!")


if __name__ == "__main__":
    main()
