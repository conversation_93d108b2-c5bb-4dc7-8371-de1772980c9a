import ast
from typing import Dict, List, Any


def calculate_list_metrics(online_list: List, offline_list: List) -> Dict[str, Any]:
    """
    计算两个列表的各种指标
    """
    online_len = len(online_list)
    offline_len = len(offline_list)

    # 位置相同元素个数
    min_len = min(online_len, offline_len)
    same_position_count = sum(1 for i in range(min_len) if online_list[i] == offline_list[i])

    # 集合操作
    online_set = set(online_list)
    offline_set = set(offline_list)
    intersection = online_set & offline_set
    union = online_set | offline_set

    # 交集比例
    intersection_ratio = len(intersection) / len(union) if len(union) > 0 else 0

    # 独特元素数目
    online_unique = len(online_set - offline_set)
    offline_unique = len(offline_set - online_set)

    return {
        'same_position_count': same_position_count,
        'intersection_ratio': intersection_ratio,
        'online_unique_count': online_unique,
        'offline_unique_count': offline_unique,
        'intersection_size': len(intersection)
    }


def calculate_cross_field_metrics(llsid_data: Dict[str, Dict]) -> Dict[str, Any]:
    """
    计算同一个llsid的不同字段中，有多少个位置的所有字段上两个list元素都相同
    """
    if not llsid_data:
        return {}

    # 获取所有字段的数据
    field_data = []
    for field_name, data in llsid_data.items():
        online_list = data['online']
        offline_list = data['offline']
        field_data.append((field_name, online_list, offline_list))

    if not field_data:
        return {}

    # 找到最短的列表长度
    min_length = min(min(len(online), len(offline)) for _, online, offline in field_data)

    # 计算每个位置上所有字段都相同的位置数
    all_same_positions = 0
    for pos in range(min_length):
        all_same = True
        for _, online_list, offline_list in field_data:
            if pos >= len(online_list) or pos >= len(offline_list) or online_list[pos] != offline_list[pos]:
                all_same = False
                break
        if all_same:
            all_same_positions += 1

    return {
        'total_fields': len(field_data),
        'min_length': min_length,
        'all_same_positions': all_same_positions,
        'all_same_ratio': all_same_positions / min_length if min_length > 0 else 0
    }


class DiffAnalyzer:
    """
    分析softv2在离线不一致数据的类
    支持解析数据文件，计算在线和离线列表的相似度，并提供可视化对比功能
    """

    def __init__(self, data_file_path: str = None):
        """
        初始化分析器
        """
        self.data_file_path = data_file_path
        self.data = {}
        self.field_name = ""
        self.all_data = {}  # 存储所有字段的数据 {llsid: {field_name: {online: [], offline: []}}}

    def load_multiple_files(self, file_paths: List[str]):
        """
        加载多个数据文件
        """
        for file_path in file_paths:
            self.data_file_path = file_path
            if self.load_data():
                # 提取字段名
                field_name = self.field_name
                # 将数据存储到all_data中
                for llsid, data in self.data.items():
                    if llsid not in self.all_data:
                        self.all_data[llsid] = {}
                    self.all_data[llsid][field_name] = {
                        'online': data['online'],
                        'offline': data['offline'],
                        'sample_id': data['sample_id']
                    }
                print(f"已加载字段: {field_name}, 数据条数: {len(self.data)}")
            self.data = {}  # 清空临时数据

    def load_data(self) -> bool:
        """
        加载数据文件
        """
        try:
            with open(self.data_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if len(lines) < 4:
                return False

            # 解析头部信息
            self.field_name = lines[0].strip()

            # 解析数据行
            for line in lines[3:]:
                line = line.strip()
                if not line:
                    continue

                parts = line.split('\t')
                if len(parts) >= 4:
                    sample_id = parts[0]
                    llsid = parts[1]
                    online_value_str = parts[2]
                    offline_value_str = parts[3]

                    # 跳过列标题行
                    if sample_id == 'sample_id' or llsid == 'llsid':
                        continue

                    # 解析列表数据
                    try:
                        online_list = ast.literal_eval(online_value_str)
                        offline_list = ast.literal_eval(offline_value_str)

                        self.data[llsid] = {
                            'sample_id': sample_id,
                            'online': online_list,
                            'offline': offline_list
                        }
                    except (ValueError, SyntaxError):
                        continue

            return True

        except FileNotFoundError:
            return False
        except Exception:
            return False

    def print_colored_lists(self, online_list: list, offline_list: list):
        """
        打印彩色对比的列表，绿色表示相同位置元素相同，红色表示不同
        """
        # ANSI颜色代码
        GREEN = '\033[92m'  # 绿色
        RED = '\033[91m'    # 红色
        RESET = '\033[0m'   # 重置颜色

        # 打印Online列表
        online_display = []
        for i in range(len(online_list)):
            value = str(online_list[i])
            if i < len(offline_list) and online_list[i] == offline_list[i]:
                online_display.append(f"{GREEN}{value}{RESET}")
            else:
                online_display.append(f"{RED}{value}{RESET}")

        print(f"Online ({len(online_list)}): [{', '.join(online_display)}]")

        # 打印Offline列表
        offline_display = []
        for i in range(len(offline_list)):
            value = str(offline_list[i])
            if i < len(online_list) and online_list[i] == offline_list[i]:
                offline_display.append(f"{GREEN}{value}{RESET}")
            else:
                offline_display.append(f"{RED}{value}{RESET}")

        print(f"Offline ({len(offline_list)}): [{', '.join(offline_display)}]")

    def analyze_llsid_simple(self, llsid: str):
        """
        极简分析指定llsid
        """
        # 单字段分析
        if llsid in self.data:
            data = self.data[llsid]
            online_list = data['online']
            offline_list = data['offline']

            print(f"LLSID: {llsid}, 字段: {self.field_name}")
            self.print_colored_lists(online_list, offline_list)

            metrics = calculate_list_metrics(online_list, offline_list)
            print(f"位置相同: {metrics['same_position_count']}")
            print(f"交集比例: {metrics['intersection_ratio']:.4f}")
            print(f"Online独特: {metrics['online_unique_count']}")
            print(f"Offline独特: {metrics['offline_unique_count']}")
            print()

        # 多字段分析
        if llsid in self.all_data:
            llsid_data = self.all_data[llsid]

            # 为每个字段单独分析
            for field_name, field_data in llsid_data.items():
                online_list = field_data['online']
                offline_list = field_data['offline']

                print(f"LLSID: {llsid}, 字段: {field_name}")
                self.print_colored_lists(online_list, offline_list)

                metrics = calculate_list_metrics(online_list, offline_list)
                print(f"位置相同: {metrics['same_position_count']}")
                print(f"交集比例: {metrics['intersection_ratio']:.4f}")
                print(f"Online独特: {metrics['online_unique_count']}")
                print(f"Offline独特: {metrics['offline_unique_count']}")
                print()

            # 跨字段分析
            cross_metrics = calculate_cross_field_metrics(llsid_data)
            if cross_metrics and cross_metrics['total_fields'] > 1:
                print(f"LLSID: {llsid} - 跨字段分析")
                print(f"总字段数: {cross_metrics['total_fields']}")
                print(f"所有字段都相同的位置数: {cross_metrics['all_same_positions']}")
                print(f"全相同比例: {cross_metrics['all_same_ratio']:.4f}")
                print()

    def analyze_all_simple(self):
        """
        极简分析所有llsid - 按字段顺序分析
        """
        # 如果有多字段数据，按字段分组分析
        if self.all_data:
            # 获取所有字段名并排序
            all_fields = set()
            for llsid_data in self.all_data.values():
                all_fields.update(llsid_data.keys())

            sorted_fields = sorted(all_fields)

            # 按字段顺序分析
            for field_name in sorted_fields:
                print(f"\n=== 字段: {field_name} ===")

                # 分析该字段下的所有LLSID
                for llsid in self.all_data:
                    if field_name in self.all_data[llsid]:
                        field_data = self.all_data[llsid][field_name]
                        online_list = field_data['online']
                        offline_list = field_data['offline']

                        print(f"LLSID: {llsid}, 字段: {field_name}")
                        self.print_colored_lists(online_list, offline_list)

                        metrics = calculate_list_metrics(online_list, offline_list)
                        print(f"位置相同: {metrics['same_position_count']}")
                        print(f"交集比例: {metrics['intersection_ratio']:.4f}")
                        print(f"Online独特: {metrics['online_unique_count']}")
                        print(f"Offline独特: {metrics['offline_unique_count']}")
                        print()
        else:
            # 单字段数据，直接分析
            for llsid in self.data:
                self.analyze_llsid_simple(llsid)


# 使用示例
def main():
    """
    极简使用示例
    """
    # 单文件分析
    analyzer = DiffAnalyzer("softv2在离线不一致0911.txt")
    if analyzer.load_data():
        analyzer.analyze_all_simple()

    # # 多文件分析
    # analyzer2 = DiffAnalyzer()
    # files = ["softv2在离线不一致0909.txt", "softv2在离线不一致0911.txt"]
    # analyzer2.load_multiple_files(files)

    # # 分析特定llsid的跨字段数据
    # for llsid in list(analyzer2.all_data.keys())[:3]:  # 只分析前3个
    #     analyzer2.analyze_llsid_simple(llsid)


if __name__ == "__main__":
    main()
    