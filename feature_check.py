data=[[779153,98451,776555,934673,871260,871260,871260,822327,822327,272449,791915,157660,963646,361959,361959,90315,90315,60879,760266,743315,431892,431892,794884,855875,227837,779559,779559,779559,557088,85574,426726,758130,758130,738519,547366,547366,547366,547366,133028,510410,253604,253604,597329,108797,515158,832675,832675,878122,789367,789367,987107,535780,591606,718127,267583,267583,267583,267583,552671,485897,99337,620292,76778,76778,760393,264637,818350,265642,176711,176711,176711,176711,176711,176711,176711,176711,792780,792780,792780,790323,745735,745735,745735,745735,670127,670127,670127,416249,969151,730102,754442,541855,541855,541855,541855,541855,541855,541855,31564,864696],
[779153,98451,934673,272449,791915,361959,361959,963646,90315,90315,157660,760266,779559,779559,779559,758130,758130,738519,832675,832675,878122,535780,987107,718127,789367,789367,485897,620292,76778,76778,730102,265642,792780,416249,790323,820258,541855,541855,541855,561524,561524,561524,561524,990843,670127,670127,670127,672800,755298,470412,553462,474819,640915,720109,720109,272862,396704,114807,726109,440491,2557,69750,69750,595975,595975,595975,758265,758265,758265,758265,758265,758265,577795,318795,263029,263029,814325,15701,848084,47651,916958,916958,224936,666234,666234,666234,666234,145019,931027,476318,28256,28256,161293,95770,843001,843001,843001,843001,843001,39987],
[72,71,24,5,47,53,54,48,49,10,6,6,4,15,16,95,96,74,34,34,77,78,63,89,26,29,30,31,18,51,63,73,80,90,75,17,18,19,50,85,75,76,1,35,68,1,2,3,97,98,19,78,72,91,48,49,50,93,86,0,7,5,14,30,85,49,84,62,20,21,34,35,76,34,35,36,68,89,36,24,81,82,87,88,5,11,59,83,16,4,50,10,13,15,55,73,64,70,95,89],
[577,2293,1015,1121,1824,318,319,1014,2115,2116,6,2054,231,232,233,1487,1494,1504,1819,1820,1821,280,19,2212,1006,1007,1616,1823,2438,2454,1822,2183,2391,2103,933,873,10,13,2439,345,346,347,348,1611,5,11,463,247,483,419,1120,129,989,1061,1062,974,264,2211,2487,2343,321,505,2243,130,131,132,9,1489,1490,1524,1526,1612,209,1171,976,977,22,1358,1589,552,2492,2493,904,632,633,634,636,492,37,1178,1639,1640,1296,1027,1790,1791,1792,1793,1795,2230],
[99,68,68,61,60,60,60,59,59,58,56,55,54,54,54,53,53,52,52,51,51,51,51,50,49,48,48,48,48,48,48,48,48,48,47,47,47,47,47,47,47,47,47,47,46,45,45,45,44,44,44,44,44,44,44,44,44,44,43,43,43,42,42,42,42,41,41,41,41,41,41,41,41,41,41,41,40,40,40,39,39,39,39,39,39,39,39,39,38,38,38,38,38,38,38,38,38,38,38,38],
[99,67,61,58,55,54,54,53,53,53,52,50,49,49,49,49,49,45,45,45,45,44,44,44,44,44,43,42,42,42,41,41,40,40,39,37,37,37,37,37,37,37,37,37,36,36,36,36,35,34,34,34,34,33,33,33,33,33,33,32,32,32,32,32,32,32,31,31,31,31,31,31,31,31,31,31,31,31,30,30,30,30,30,30,30,30,30,29,29,29,29,29,29,29,28,28,28,28,28,28],
]

assert len(data[0]) == len(data[1]) and len(data[0]) == len(data[3]), f"{len(data[0])},{len(data[1])},{len(data[3])}"

import pandas as pd
results = []
for i in range(len(data[0])):
    results.append([data[0][i], data[2][i], data[4][i], data[1][i],data[3][i],data[5][i]])
pd.DataFrame(results).to_csv(f'feature_check.csv', header="online_itemid,online_indices,online_values,item_id,indices,values".split(','))