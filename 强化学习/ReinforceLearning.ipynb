{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机生成了一个10臂伯努利老虎机\n", "获奖概率最大的拉杆为1号,其获奖概率为0.7203\n"]}], "source": ["# 导入需要使用的库,其中numpy是支持数组和矩阵运算的科学计算库,而matplotlib是绘图库\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "class BernoulliBandit:\n", "    \"\"\" 伯努利多臂老虎机,输入K表示拉杆个数 \"\"\"\n", "    def __init__(self, K):\n", "        self.probs = np.random.uniform(size=K)  # 随机生成K个0～1的数,作为拉动每根拉杆的获奖\n", "        # 概率\n", "        self.best_idx = np.argmax(self.probs)  # 获奖概率最大的拉杆\n", "        self.best_prob = self.probs[self.best_idx]  # 最大的获奖概率\n", "        self.K = K\n", "\n", "    def step(self, k):\n", "        # 当玩家选择了k号拉杆后,根据拉动该老虎机的k号拉杆获得奖励的概率返回1（获奖）或0（未\n", "        # 获奖）\n", "        if np.random.rand() < self.probs[k]:\n", "            return 1\n", "        else:\n", "            return 0\n", "\n", "class Solver:\n", "    \"\"\" 多臂老虎机算法基本框架 \"\"\"\n", "    def __init__(self, bandit):\n", "        self.bandit = bandit\n", "        self.counts = np.zeros(self.bandit.K)  # 每根拉杆的尝试次数\n", "        self.regret = 0.  # 当前步的累积懊悔\n", "        self.actions = []  # 维护一个列表,记录每一步的动作\n", "        self.regrets = []  # 维护一个列表,记录每一步的累积懊悔\n", "        self.step_regrets = []\n", "        self.actual_reward = [] #维护一个reward list，记录每一步的选择以及对应reward结果\n", "\n", "    def update_regret(self, k):\n", "        # 计算累积懊悔并保存,k为本次动作选择的拉杆的编号\n", "        self.regret += self.bandit.best_prob - self.bandit.probs[k]\n", "        self.regrets.append(self.regret)\n", "        self.step_regrets.append(self.bandit.best_prob - self.bandit.probs[k]) # action先+1，再算step_regret\n", "\n", "    def run_one_step(self):\n", "        # 返回当前动作选择哪一根拉杆,由每个具体的策略实现, 并且返回实际这次操作的reward\n", "        raise NotImplementedError\n", "    \n", "    def run(self, num_steps):\n", "        # 运行一定次数,num_steps为总运行次数\n", "        for _ in range(num_steps):\n", "            k, r = self.run_one_step()\n", "            self.counts[k] += 1\n", "            self.actions.append(k)\n", "            self.actual_reward.append((k,r))\n", "            self.update_regret(k)\n", "\n", "np.random.seed(1)  # 设定随机种子,使实验具有可重复性\n", "K = 10\n", "bandit_10_arm = BernoulliBandit(K)\n", "print(\"随机生成了一个%d臂伯努利老虎机\" % K)\n", "print(\"获奖概率最大的拉杆为%d号,其获奖概率为%.4f\" %\n", "      (bandit_10_arm.best_idx, bandit_10_arm.best_prob))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class EpsilonGreedy(Solver):\n", "    \"\"\" epsilon贪婪算法,继承Solver类 \"\"\"\n", "    def __init__(self, bandit, epsilon=0.01, init_prob=1.0):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(bandit)\n", "        self.epsilon = epsilon\n", "        #初始化拉动所有拉杆的期望奖励估值\n", "        self.estimates = np.array([init_prob] * self.bandit.K)\n", "\n", "    def run_one_step(self):\n", "        if np.random.random() < self.epsilon:\n", "            k = np.random.randint(0, self.bandit.K)  # 随机选择一根拉杆\n", "        else:\n", "            k = np.argmax(self.estimates)  # 选择期望奖励估值最大的拉杆\n", "        r = self.bandit.step(k)  # 得到本次动作的奖励\n", "        self.estimates[k] += 1. / (self.counts[k] + 1) * (r - self.estimates[k])\n", "        return k, r"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epsilon-贪婪算法的累积懊悔为： 25.526630933945313\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_regrets(solvers, solver_names):\n", "    \"\"\"生成累积懊悔随时间变化的图像。输入solvers是一个列表,列表中的每个元素是一种特定的策略。\n", "    而solver_names也是一个列表,存储每个策略的名称\"\"\"\n", "    for idx, solver in enumerate(solvers):\n", "        time_list = range(len(solver.regrets))\n", "        plt.plot(time_list, solver.regrets, label=solver_names[idx])\n", "    plt.xlabel('Time steps')\n", "    plt.ylabel('Cumulative regrets')\n", "    plt.title('%d-armed bandit' % solvers[0].bandit.K)\n", "    plt.legend()\n", "    plt.show()\n", "\n", "def plot_step_regrets(solvers, solver_names):\n", "    \"\"\"生成累积懊悔随时间变化的图像。输入solvers是一个列表,列表中的每个元素是一种特定的策略。\n", "    而solver_names也是一个列表,存储每个策略的名称\"\"\"\n", "    for idx, solver in enumerate(solvers):\n", "        time_list = range(len(solver.step_regrets))\n", "        plt.plot(time_list, solver.step_regrets, label=solver_names[idx])\n", "    plt.xlabel('Time steps')\n", "    plt.ylabel('Avg regrets per step')\n", "    plt.title('%d-armed bandit' % solvers[0].bandit.K)\n", "    plt.legend()\n", "    plt.show()\n", "\n", "def plot_rewards(actual_reward):\n", "    \"\"\"生成累积懊悔随时间变化的图像。输入solvers是一个列表,列表中的每个元素是一种特定的策略。\n", "    而solver_names也是一个列表,存储每个策略的名称\"\"\"\n", "    x1,x2,y1,y2 = [],[],[],[]\n", "    for idx, t in enumerate(actual_reward):\n", "        k, r = t\n", "        if r > 0:\n", "            x1.append(idx)\n", "            y1.append(k)\n", "        else:\n", "            x2.append(idx)\n", "            y2.append(k)\n", "\n", "    plt.scatter(x1,y1,c='red', label='got reward', linewidths=0.01)\n", "    plt.scatter(x2,y2, label='no reward', linewidths=0.01)\n", "    plt.xlabel('Time steps')\n", "    plt.ylabel('Arm K')\n", "    plt.title('10-armed bandit')\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "np.random.seed(1)\n", "epsilon_greedy_solver = EpsilonGreedy(bandit_10_arm, epsilon=0.01)\n", "epsilon_greedy_solver.run(5000)\n", "print('epsilon-贪婪算法的累积懊悔为：', epsilon_greedy_solver.regret)\n", "plot_regrets([epsilon_greedy_solver], [\"EpsilonGreedy\"])\n", "plot_step_regrets([epsilon_greedy_solver], [\"EpsilonGreedy\"])\n", "plot_rewards(epsilon_greedy_solver.actual_reward)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.animation import FuncAnimation\n", "def animation_of_reward(actual_reward):\n", "    # 1. 造一份演示数据 ----------------------------------------------------------\n", "    # 5000 个时间步，10 个位置，0/1 表示有无 reward\n", "    # 你直接把 R 换成自己的真实数据即可\n", "    # np.random.seed(42)\n", "    # R = np.random.choice([0, 1], size=(5000, 10), p=[0.85, 0.15])\n", "    R = np.zeros((5000,10))\n", "    for idx, (k, r) in enumerate(actual_reward):\n", "        if r > 0:\n", "            R[idx,k] = 1\n", "        else:\n", "            R[idx,k] = -1\n", "\n", "\n", "    # 2. 画布初始化 --------------------------------------------------------------\n", "    fig, ax = plt.subplots(figsize=(6, 4))\n", "    ax.set_xlim(0, 200)\n", "    ax.set_ylim(-0.5, 9.5)\n", "    ax.set_yticks(range(10))\n", "    ax.set_xlabel('time step')\n", "    ax.set_ylabel('position k')\n", "    ax.set_title('Reward scatter animation')\n", "\n", "    # 只保留一个散点对象，用不同的颜色/偏移区分 k\n", "    scatter = ax.scatter([], [], s=80)          # 空散点，后续用 set_offsets / set_array 更新\n", "\n", "    # 3. 逐帧更新 ---------------------------------------------------------------\n", "    def update(frame):\n", "        left = max(0, frame - 150)\n", "        ax.set_xlim(left, left + 200)\n", "\n", "        # 取出当前帧 10 个位置的状态\n", "        state = R[frame]          # shape (10,)\n", "\n", "        # 只保留非 0 的索引\n", "        idx = np.where(state != 0)[0]\n", "        x = np.full_like(idx, frame, dtype=float)   # x 坐标都是当前帧\n", "        y = idx.astype(float)                       # y 坐标就是 k 位置\n", "        colors = np.where(state[idx] == 1, '#2ca02c', '#d62728')  # 1→绿，-1→红\n", "\n", "        # 更新散点\n", "        scatter.set_offsets(np.c_[x, y])\n", "        scatter.set_color(colors)\n", "        return scatter,\n", "\n", "    # 4. 启动动画 ---------------------------------------------------------------\n", "    ani = FuncAnimation(fig, update, frames=5000,\n", "                        interval=50, blit=True, repeat=False)\n", "\n", "    # plt.show()\n", "    ani.save('reward_waterfall.mp4', writer='ffmpeg', fps=30)  # 需要 ffmpeg\n", "\n", "# animation_of_reward(epsilon_greedy_solver.actual_reward)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgsAAAGHCAYAAAA+xRHwAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA5GElEQVR4nO3deVxVdf7H8feVXYQruIIgELmmJmYpamlqLmnWmFZmptmiE2lpTeVMRVoTNW22WqZjNlaWUy4tauq41IgraWrmvmWauXGRDAW+vz8c7s8r1yNXgQP4ej4e5/Hofs/3nPv53uPpvjnbdRhjjAAAAM6ikt0FAACAso2wAAAALBEWAACAJcICAACwRFgAAACWCAsAAMASYQEAAFgiLAAAAEuEBQAAYImwgHLp/fffl8PhcE/+/v6KiorSbbfdpi1btthdXrGJj4/XoEGD7C6jkOeee04zZswo1P7jjz/q6aef1s6dO0u9puL09NNPy+Fw2PLeVp/hoEGDFB8fX+o1AYQFlGuTJk1Senq65s+frwceeECzZs1Su3btdOTIEbtLq9CswsLo0aPLfVi45557lJ6ebst7W32GTz75pKZPn176ReGi5293AcCFaNKkiVq2bClJ6tChg/Ly8pSamqoZM2borrvusrm6c/v9999VuXJlu8so80r7c4qJiVFMTEypvV9RJSYm2l0CLlIcWUCFUhAcfv31V4/2VatWqVevXoqMjFRwcLCSkpL06aefuue7XC75+/vrxRdfdLcdPHhQlSpVktPpVG5urrt9+PDhqlGjhgp+g23evHm68cYbFRMTo+DgYF166aUaMmSIDh486FFDwaHtjIwM9enTRxEREe7/+Z88eVKPPvqoateurcqVK6tdu3ZasWJFkcc9btw4XX755apSpYrCwsLUsGFD/fWvf/Xos3fvXt13332KjY1VYGCgoqOj1adPH/dn9ccff+jhhx9W8+bN5XQ6FRkZqeTkZM2cOdNjPQ6HQ9nZ2Zo8ebL7NFCHDh30/vvvq2/fvpKka6+91j3v/fffdy87f/58derUSeHh4apcubLatm2rBQsWFPlz8ua3337T/fffr8aNG6tKlSqqWbOmOnbsqG+//daj386dO+VwOPTSSy/plVdeUUJCgqpUqaLk5GQtW7bMaw2ni4+PV8+ePfXll18qKSlJISEhatSokb788ktJp06NNWrUSKGhobrqqqu0atUqj+VXrVql2267TfHx8QoJCVF8fLz69eunXbt2ufuc6zP0dhrijz/+0KhRo5SQkKDAwEDVqVNHKSkpOnr0qNf658yZoxYtWigkJEQNGzbUP//5z7N+tkABwgIqlB07dkiS6tev725buHCh2rZtq6NHj+qdd97RzJkz1bx5c916663u/wmHh4fryiuv1Pz5893LLViwQEFBQcrKyvL44p4/f746duzo/jLZtm2bkpOTNW7cOH3zzTd66qmntHz5crVr104nT54sVGPv3r116aWXatq0aXrnnXckSffee69eeukl3XnnnZo5c6Zuvvlm9e7du0inU6ZOnar7779f7du31/Tp0zVjxgyNGDFC2dnZ7j579+7VlVdeqenTp2vkyJGaPXu2xo4dK6fT6X6PnJwcHT58WI888ohmzJihjz/+WO3atVPv3r31wQcfuNeVnp6ukJAQXX/99UpPT1d6errefvtt9ejRQ88995wk6a233nLP69GjhyRpypQp6tKli8LDwzV58mR9+umnioyMVNeuXQsFhrN9Tt4cPnxYkpSamqqvvvpKkyZN0iWXXKIOHTpo0aJFhfq/9dZbmjdvnsaOHasPP/xQ2dnZuv7665WZmXnOz3rt2rUaNWqUHnvsMX3++edyOp3q3bu3UlNTNWHCBD333HP68MMPlZmZqZ49e+r48ePuZXfu3KkGDRpo7Nixmjt3rl544QXt27dPV155pTtYnuszPJMxRjfddJNeeuklDRgwQF999ZVGjhypyZMnq2PHjsrJySlU/8MPP6wRI0Zo5syZatasme6++24tWbLknGPHRc4A5dCkSZOMJLNs2TJz8uRJk5WVZebMmWNq165trrnmGnPy5El334YNG5qkpCSPNmOM6dmzp4mKijJ5eXnGGGOeeOIJExISYv744w9jjDH33HOP6datm2nWrJkZPXq0McaYvXv3Gklm/PjxXuvKz883J0+eNLt27TKSzMyZM93zUlNTjSTz1FNPeSyzceNGI8mMGDHCo/3DDz80kszAgQMtP4sHHnjAVK1a1bLP4MGDTUBAgPnxxx8t+50uNzfXnDx50tx9990mKSnJY15oaKjXuqZNm2YkmYULF3q0Z2dnm8jISHPDDTd4tOfl5ZnLL7/cXHXVVe62s31OvtbdqVMn86c//cndvmPHDiPJNG3a1OTm5rrbV6xYYSSZjz/+uFANp4uLizMhISHm559/dretWbPGSDJRUVEmOzvb3T5jxgwjycyaNcuyzmPHjpnQ0FDz2muvudvP9hkaY8zAgQNNXFyc+/WcOXOMJPOPf/zDo98nn3xS6N9pXFycCQ4ONrt27XK3HT9+3ERGRpohQ4actU7AGGM4soByrXXr1goICFBYWJi6deumiIgIzZw5U/7+py7H2bp1q3766Sf1799fkpSbm+uerr/+eu3bt0+bNm2SJHXq1EnHjx/X0qVLJZ06gnDdddepc+fOmjdvnrtNkjp37uyu4cCBAxo6dKhiY2Pl7++vgIAAxcXFSZI2btxYqOabb77Z4/XChQslyV1jgVtuucU9DitXXXWVjh49qn79+mnmzJmFTn9I0uzZs3XttdeqUaNGluuaNm2a2rZtqypVqrjHMnHiRK/j8MXSpUt1+PBhDRw40GMb5Ofnq1u3blq5cqXHkRCp8Odk5Z133lGLFi0UHBzsrnvBggVe6+7Ro4f8/Pzcr5s1ayZJHqcDzqZ58+aqU6eO+3XB59mhQwePayoK2k9f57Fjx/TYY4/p0ksvlb+/v/z9/VWlShVlZ2ef9+f7n//8R5IK3THTt29fhYaGFjpi07x5c9WtW9f9Ojg4WPXr1y/S2HFxIyygXPvggw+0cuVK/ec//9GQIUO0ceNG9evXzz2/4Hz8I488ooCAAI/p/vvvlyT3l2ubNm1UuXJlzZ8/X1u3btXOnTvdYWH58uU6duyY5s+fr0suuUQJCQmSpPz8fHXp0kWff/65Hn30US1YsEArVqxwnwM//TB0gaioKI/Xhw4dkiTVrl3bo93f31/VqlU752cwYMAA/fOf/9SuXbt08803q2bNmmrVqpU74Einzuuf64K9zz//XLfccovq1KmjKVOmKD09XStXrtTgwYP1xx9/nLMOKwXboU+fPoW2wwsvvCBjjPt0QoEzP6ezeeWVV/TnP/9ZrVq10meffaZly5Zp5cqV6tatm9fP/8zPNCgoSJL3bXWmyMhIj9eBgYGW7ad/brfffrvefPNN3XPPPZo7d65WrFihlStXqkaNGkV6b28OHTokf39/1ahRw6Pd4XCodu3a7n9bBbz9ewoKCjrv98fFg7shUK41atTIfVHjtddeq7y8PE2YMEH//ve/1adPH1WvXl2SNGrUKPXu3dvrOho0aCDp1P/g27Vrp/nz5ysmJka1a9dW06ZNdckll0iSFi1apAULFqhnz57uZdevX6+1a9fq/fff18CBA93tW7duPWvNZ144V/A/8P3793v81Zqbm1vof/Znc9ddd+muu+5Sdna2lixZotTUVPXs2VObN29WXFycatSooZ9//tlyHVOmTFFCQoI++eQTjxrPPO99Pgq2wxtvvKHWrVt77VOrVi2P10V9zsGUKVPUoUMHjRs3zqM9KyvrPCotGZmZmfryyy+Vmpqqxx9/3N1ecJ3I+apWrZpyc3P122+/eQQGY4z279+vK6+88oLqBgpwZAEVyj/+8Q9FREToqaeeUn5+vho0aKB69epp7dq1atmypdcpLCzMvXznzp21evVqffbZZ+5TDaGhoWrdurXeeOMN/fLLLx6nIAq+0Ar+Oi3w7rvvFrnmDh06SJI+/PBDj/ZPP/3U4y6MoggNDVX37t31t7/9TSdOnNCGDRskSd27d9fChQvdp1y8cTgcCgwM9PiS3r9/f6G7IaSz/zV6tr/S27Ztq6pVq+rHH38863Yo+GvcVw6Ho9Dn/8MPP9j2nARvHA6HjDGF6pwwYYLy8vI82nw50tGpUydJpwLT6T777DNlZ2e75wMXiiMLqFAiIiI0atQoPfroo/roo490xx136N1331X37t3VtWtXDRo0SHXq1NHhw4e1ceNGZWRkaNq0ae7lO3XqpLy8PC1YsECTJ092t3fu3FmpqalyOBzq2LGju71hw4ZKTEzU448/LmOMIiMj9cUXX3icAjiXRo0a6Y477tDYsWMVEBCgzp07a/369XrppZcUHh5+zuXvvfdehYSEqG3btoqKitL+/fuVlpYmp9Pp/styzJgxmj17tq655hr99a9/VdOmTXX06FHNmTNHI0eOVMOGDdWzZ099/vnnuv/++9WnTx/t2bNHzzzzjKKiogo9FbNp06ZatGiRvvjiC0VFRSksLEwNGjRQkyZNJEnjx49XWFiYgoODlZCQoGrVqumNN97QwIEDdfjwYfXp00c1a9bUb7/9prVr1+q3334rdGSgqHr27KlnnnlGqampat++vTZt2qQxY8YoISHB57BVUsLDw3XNNdfoxRdfVPXq1RUfH6/Fixdr4sSJqlq1qkdfq8/wTNddd526du2qxx57TC6XS23bttUPP/yg1NRUJSUlacCAAaUxPFwM7L2+Ejg/BXdDrFy5stC848ePm7p165p69eq5r3pfu3atueWWW0zNmjVNQECAqV27tunYsaN55513PJbNz8831atXN5LM3r173e3//e9/jSTTokWLQu/3448/muuuu86EhYWZiIgI07dvX7N7924jyaSmprr7FVxh/9tvvxVaR05Ojnn44YdNzZo1TXBwsGndurVJT083cXFx57wbYvLkyebaa681tWrVMoGBgSY6Otrccsst5ocffvDot2fPHjN48GBTu3ZtExAQ4O7366+/uvs8//zzJj4+3gQFBZlGjRqZ9957z+udAWvWrDFt27Y1lStXNpJM+/bt3fPGjh1rEhISjJ+fn5FkJk2a5J63ePFi06NHDxMZGWkCAgJMnTp1TI8ePcy0adOK9Dl5k5OTYx555BFTp04dExwcbFq0aGFmzJhR6M6BgrshXnzxxULrONu2Ol1cXJzp0aOH12VTUlI82ry9188//2xuvvlmExERYcLCwky3bt3M+vXrvW7js32GZ47JmFP/3h977DETFxdnAgICTFRUlPnzn/9sjhw5UqT627dv77H9AG8cxvzvyTIAAABecM0CAACwRFgAAACWCAsAAMASYQEAAFgiLAAAAEuEBQAAYKlcP5QpPz9fv/zyi8LCwor8aFgAAHDqseBZWVmKjo5WpUrWxw7KdVj45ZdfFBsba3cZAACUW3v27DnnD82V67BQ8Ez/PXv2FOmxuAAA4BSXy6XY2FiP38c5m3IdFgpOPYSHhxMWAAA4D0U5jc8FjgAAwBJhAQAAWCIsAAAAS4QFAABgibAAAAAsERYAAIAlwgIAALBEWAAAAJZsDQtZWVl66KGHFBcXp5CQELVp00YrV660syQAAHAGW8PCPffco3nz5ulf//qX1q1bpy5duqhz587au3evnWUBAIDTOIwxxo43Pn78uMLCwjRz5kz16NHD3d68eXP17NlTzz777DnX4XK55HQ6lZmZyeOeAQDwgS/fobb9NkRubq7y8vIUHBzs0R4SEqLvvvvO6zI5OTnKyclxv3a5XCVaIwAAsPE0RFhYmJKTk/XMM8/ol19+UV5enqZMmaLly5dr3759XpdJS0uT0+l0T/w8NQAAJc+20xCStG3bNg0ePFhLliyRn5+fWrRoofr16ysjI0M//vhjof7ejizExsZyGgIAAB+Vi9MQkpSYmKjFixcrOztbLpdLUVFRuvXWW5WQkOC1f1BQkIKCgkq5SgAALm5l4jkLoaGhioqK0pEjRzR37lzdeOONdpcEAAD+x9YjC3PnzpUxRg0aNNDWrVv1l7/8RQ0aNNBdd91lZ1kAAOA0th5ZyMzMVEpKiho2bKg777xT7dq10zfffKOAgAA7ywIAAKex9QLHC8VzFgAAOD++fIeWiWsWAABA2UVYAAAAlggLAADAEmEBAABYIiwAAABLhAUAAGCJsAAAACwRFgAAgCXCAgAAsERYAAAAlggLAADAEmEBAABYIiwAAABLhAUAAGCJsAAAACwRFgAAgCXCAgAAsERYAAAAlggLAADAEmEBAABYsjUs5Obm6oknnlBCQoJCQkJ0ySWXaMyYMcrPz7ezLAAAcBp/O9/8hRde0DvvvKPJkyfrsssu06pVq3TXXXfJ6XTqwQcftLM0AADwP7aGhfT0dN14443q0aOHJCk+Pl4ff/yxVq1aZWdZAADgNLaehmjXrp0WLFigzZs3S5LWrl2r7777Ttdff73X/jk5OXK5XB4TAAAoWbYeWXjssceUmZmphg0bys/PT3l5efr73/+ufv36ee2flpam0aNHl3KVAABc3Gw9svDJJ59oypQp+uijj5SRkaHJkyfrpZde0uTJk732HzVqlDIzM93Tnj17SrliAAAuPg5jjLHrzWNjY/X4448rJSXF3fbss89qypQp+umnn865vMvlktPpVGZmpsLDw0uyVAAAKhRfvkNtPbLw+++/q1IlzxL8/Py4dRIAgDLE1msWbrjhBv39739X3bp1ddlll+n777/XK6+8osGDB9tZFgAAOI2tpyGysrL05JNPavr06Tpw4ICio6PVr18/PfXUUwoMDDzn8pyGAADg/PjyHWprWLhQhAUAAM5PublmAQAAlH2EBQAAYImwAAAALBEWAACAJcICAACwRFgAAACWCAsAAMASYQEAAFgiLAAAAEuEBQAAYImwAAAALBEWAACAJcICAACwRFgAAACWCAsAAMASYQEAAFgiLAAAAEuEBQAAYImwAAAALBEWAACAJVvDQnx8vBwOR6EpJSXFzrIAAMBp/O1885UrVyovL8/9ev369bruuuvUt29fG6sCAACnszUs1KhRw+P1888/r8TERLVv396migAAwJlsDQunO3HihKZMmaKRI0fK4XB47ZOTk6OcnBz3a5fLVVrlAQBw0SozFzjOmDFDR48e1aBBg87aJy0tTU6n0z3FxsaWXoEAAFykHMYYY3cRktS1a1cFBgbqiy++OGsfb0cWYmNjlZmZqfDw8NIoEwCACsHlcsnpdBbpO7RMnIbYtWuX5s+fr88//9yyX1BQkIKCgkqpKgAAIJWR0xCTJk1SzZo11aNHD7tLAQAAZ7A9LOTn52vSpEkaOHCg/P3LxIEOAABwGtvDwvz587V7924NHjzY7lIAAIAXtv8p36VLF5WRaywBAIAXth9ZAAAAZRthAQAAWCIsAAAAS4QFAABgibAAAAAsERYAAIAlwgIAALBEWAAAAJYICwAAwBJhAQAAWCIsAAAAS4QFAABgibAAAAAsERYAAIAlwgIAALBEWAAAAJYICwAAwBJhAQAAWCIsAAAAS4QFAABgyfawsHfvXt1xxx2qVq2aKleurObNm2v16tV2lwUAAP7H3843P3LkiNq2batrr71Ws2fPVs2aNbVt2zZVrVrVzrIAAMBpbA0LL7zwgmJjYzVp0iR3W3x8vH0FAQCAQmw9DTFr1iy1bNlSffv2Vc2aNZWUlKT33nvvrP1zcnLkcrk8JgAAULJsDQvbt2/XuHHjVK9ePc2dO1dDhw7V8OHD9cEHH3jtn5aWJqfT6Z5iY2NLuWIAAC4+DmOMsevNAwMD1bJlSy1dutTdNnz4cK1cuVLp6emF+ufk5CgnJ8f92uVyKTY2VpmZmQoPDy+VmgEAqAhcLpecTmeRvkNtPbIQFRWlxo0be7Q1atRIu3fv9to/KChI4eHhHhMAAChZtoaFtm3batOmTR5tmzdvVlxcnE0VAQCAM9kaFkaMGKFly5bpueee09atW/XRRx9p/PjxSklJsbMsAABwGlvDwpVXXqnp06fr448/VpMmTfTMM89o7Nix6t+/v51lAQCA09h6geOF8uXiDAAA8P/KzQWOAACg7CMsAAAAS4QFAABgibAAAAAsERYAAIAlwgIAALDkc1g4ceLEWecdPHjwgooBAABlj89h4ZZbblF+fn6h9l9//VUdOnQojpoAAEAZ4nNY2Ldvn+6++26Ptv3796tDhw5q2LBhsRUGAADKBp/Dwtdff60VK1ZoxIgRkqS9e/eqffv2atq0qT799NNiLxAAANjL39cFqlWrprlz56pdu3aSpK+++kotWrTQhx9+qEqVuF4SAICKxuewIEkxMTGaN2+e2rVrp+uuu07/+te/5HA4irs2AABQBhQpLERERHgNA7///ru++OILVatWzd12+PDh4qsOAADYrkhhYezYsSVcBgAAKKuKFBYGDhxY0nUAAIAyiisSAQCAJcICAACwRFgAAACWCAsAAMCSrWHh6aeflsPh8Jhq165tZ0kAAOAMPj+UKTs7W88//7wWLFigAwcOFPpRqe3bt/u0vssuu0zz5893v/bz8/O1JAAAUIJ8Dgv33HOPFi9erAEDBigqKuqCn9zo7+/P0QQAAMown8PC7Nmz9dVXX6lt27bFUsCWLVsUHR2toKAgtWrVSs8995wuueQSr31zcnKUk5Pjfu1yuYqlBgAAcHY+X7MQERGhyMjIYnnzVq1a6YMPPtDcuXP13nvvaf/+/WrTpo0OHTrktX9aWpqcTqd7io2NLZY6AADA2TmMMcaXBaZMmaKZM2dq8uTJqly5crEWk52drcTERD366KMaOXJkofnejizExsYqMzNT4eHhxVoLAAAVmcvlktPpLNJ3qM+nIV5++WVt27ZNtWrVUnx8vAICAjzmZ2Rk+LpKt9DQUDVt2lRbtmzxOj8oKEhBQUHnvX4AAOA7n8PCTTfdVAJlnJKTk6ONGzfq6quvLrH3AAAAvvE5LKSmphbbmz/yyCO64YYbVLduXR04cEDPPvusXC4XP1wFAEAZ4nNYKLB69Wpt3LhRDodDjRs3VlJSks/r+Pnnn9WvXz8dPHhQNWrUUOvWrbVs2TLFxcWdb1kAAKCY+RwWDhw4oNtuu02LFi1S1apVZYxRZmamrr32Wk2dOlU1atQo8rqmTp3q69sDAIBS5vOtk8OGDZPL5dKGDRt0+PBhHTlyROvXr5fL5dLw4cNLokYAAGAjn2+ddDqdmj9/vq688kqP9hUrVqhLly46evRocdZnyZfbPgAAwP/z5TvU5yML+fn5hW6XlKSAgIBCvxMBAADKP5/DQseOHfXggw/ql19+cbft3btXI0aMUKdOnYq1OAAAYD+fw8Kbb76prKwsxcfHKzExUZdeeqkSEhKUlZWlN954oyRqBAAANvL5bojY2FhlZGRo3rx5+umnn2SMUePGjdW5c+eSqA8AANjM5wscyxIucAQA4PwU+29DvP7667rvvvsUHBys119/3bIvt08CAFCxFOnIQkJCglatWqVq1aopISHh7CtzOLR9+/ZiLdAKRxYAADg/xX5kYceOHV7/GwAAVHw+3w0xZswY/f7774Xajx8/rjFjxhRLUQAAoOzw+QJHPz8/7du3TzVr1vRoP3TokGrWrKm8vLxiLdAKpyEAADg/JfoER2OMHA5Hofa1a9cqMjLS19UBAIAyrsjPWYiIiJDD4ZDD4VD9+vU9AkNeXp6OHTumoUOHlkiRAADAPkUOC2PHjpUxRoMHD9bo0aPldDrd8wIDAxUfH6/k5OQSKRIAANinyGFh4MCBkk7dRtmmTRuvPyYFAAAqniKFBZfL5b74ISkpScePH9fx48e99uVCQwAAKpYihYWIiAj3HRBVq1b1eoFjwYWPpXk3BAAAKHlFCgv/+c9/3Hc6LFy4sEQLAgAAZQs/JAUAwEWoRJ+zMGfOHH333Xfu12+99ZaaN2+u22+/XUeOHPG92v9JS0uTw+HQQw89dN7rAAAAxc/nsPCXv/xFLpdLkrRu3TqNHDlS119/vbZv366RI0eeVxErV67U+PHj1axZs/NaHgAAlByfw8KOHTvUuHFjSdJnn32mG264Qc8995zefvttzZ492+cCjh07pv79++u9995TRESEz8sDAICS5XNYCAwMdP+Q1Pz589WlSxdJUmRkpPuIgy9SUlLUo0cPde7c+Zx9c3Jy5HK5PCYAAFCyivxQpgLt2rXTyJEj1bZtW61YsUKffPKJJGnz5s2KiYnxaV1Tp05VRkaGVq5cWaT+aWlpGj16tK8lAwCAC+DzkYU333xT/v7++ve//61x48apTp06kqTZs2erW7duRV7Pnj179OCDD2rKlCkKDg4u0jKjRo1SZmame9qzZ4+v5QMAAB/ZduvkjBkz9Kc//Ul+fn7utry8PDkcDlWqVEk5OTke87zh1kkAAM6PL9+hPp+GkE59qc+YMUMbN26Uw+FQo0aNdOONN57zy/10nTp10rp16zza7rrrLjVs2FCPPfaYT+sCAAAlx+ewsHXrVl1//fXau3evGjRoIGOMNm/erNjYWH311VdKTEws0nrCwsLUpEkTj7bQ0FBVq1atUDsAALCPz9csDB8+XImJidqzZ48yMjL0/fffa/fu3UpISNDw4cNLokYAAGAjn69ZCA0N1bJly9S0aVOP9rVr16pt27Y6duxYsRZohWsWAAA4PyX6uOegoCBlZWUVaj927JgCAwN9XR0AACjjfA4LPXv21H333afly5fLGCNjjJYtW6ahQ4eqV69eJVEjAACwkc9h4fXXX1diYqKSk5MVHBys4OBgtW3bVpdeeqlee+21kqgRAADYyOe7IapWraqZM2dqy5Yt2rhxoySpcePGuvTSS4u9OAAAYL/zes6CJNWrV88dEBwOR7EVBAAAyhafT0NI0sSJE9WkSRP3aYgmTZpowoQJxV0bAAAoA3w+svDkk0/q1Vdf1bBhw5ScnCxJSk9P14gRI7Rz5049++yzxV4kAACwj8/PWahevbreeOMN9evXz6P9448/1rBhw3Tw4MFiLdAKz1kAAOD8lOhzFvLy8tSyZctC7VdccYVyc3N9XR0AACjjfA4Ld9xxh8aNG1eoffz48erfv3+xFAUAAMqO87obYuLEifrmm2/UunVrSdKyZcu0Z88e3XnnnRo5cqS73yuvvFI8VQIAANv4HBbWr1+vFi1aSJK2bdsmSapRo4Zq1Kih9evXu/txOyUAABWDz2Fh4cKFJVEHAAAoo87rOQsAAODiQVgAAACWCAsAAMASYQEAAFgiLAAAAEuEBQAAYImwAAAALNkaFsaNG6dmzZopPDxc4eHhSk5O1uzZs+0sCQAAnMHWsBATE6Pnn39eq1at0qpVq9SxY0fdeOON2rBhg51lAQCA0/j8E9UlLTIyUi+++KLuvvvuc/blJ6oBADg/vnyHntcPSZWEvLw8TZs2TdnZ2UpOTvbaJycnRzk5Oe7XLpertMoDAOCiZfsFjuvWrVOVKlUUFBSkoUOHavr06WrcuLHXvmlpaXI6ne4pNja2lKsFAODiY/tpiBMnTmj37t06evSoPvvsM02YMEGLFy/2Ghi8HVmIjY3lNAQAAD7y5TSE7WHhTJ07d1ZiYqLefffdc/blmgUAAM6PL9+htp+GOJMxxuPoAQAAsJetFzj+9a9/Vffu3RUbG6usrCxNnTpVixYt0pw5c+wsCwAAnMbWsPDrr79qwIAB2rdvn5xOp5o1a6Y5c+bouuuus7MsAABwGlvDwsSJE+18ewAAUARl7poFAABQthAWAACAJcICAACwRFgAAACWCAsAAMASYQEAAFgiLAAAAEuEBQAAYImwAAAALBEWAACAJcICAACwRFgAAACWCAsAAMASYQEAAFgiLAAAAEuEBQAAYImwAAAALBEWAACAJcICAACwRFgAAACWbA0LaWlpuvLKKxUWFqaaNWvqpptu0qZNm+wsCQAAnMHWsLB48WKlpKRo2bJlmjdvnnJzc9WlSxdlZ2fbWRYAADiNwxhj7C6iwG+//aaaNWtq8eLFuuaaa87Z3+Vyyel0KjMzU+Hh4aVQIQAAFYMv36H+pVRTkWRmZkqSIiMjvc7PyclRTk6O+7XL5SqVugAAuJiVmQscjTEaOXKk2rVrpyZNmnjtk5aWJqfT6Z5iY2NLuUoAAC4+ZeY0REpKir766it99913iomJ8drH25GF2NhYTkMAAOCjcncaYtiwYZo1a5aWLFly1qAgSUFBQQoKCirFygAAgK1hwRijYcOGafr06Vq0aJESEhLsLAcAAHhha1hISUnRRx99pJkzZyosLEz79++XJDmdToWEhNhZGgAA+B9br1lwOBxe2ydNmqRBgwadc3lunQQA4PyUm2sWysi1lQAAwEKZuXUSAACUTYQFAABgibAAAAAsERYAAIAlwgIAALBEWAAAAJYICwAAwBJhAQAAWCIsAAAAS4QFAABgibAAAAAsERYAAIAlwgIAALBEWAAAAJYICwAAwBJhAQAAWCIsAAAAS/52FwAAKPtyDx7U7ytWKC87W36hoap81VXyr17d7rJQSggLAICz+mPTZh1691255s6V8vL+f4afn8K7dlW1IUMU3KC+fQWiVHAaAgDg1bFvv9POvn0LBwVJysuTa+5c7ezbV8e+/c6eAlFqbA0LS5Ys0Q033KDo6Gg5HA7NmDHDznIAAP/zx6bN+jklRebkycJBoUBenszJk/o5JUV/bNpcugWiVNkaFrKzs3X55ZfrzTfftLMMAMAZDr37rkxenmSMdUdjZPLydGj8+NIpDLaw9ZqF7t27q3v37naWAAA4Q+7Bg95PPZxNXp5cc+ao1l9Hyb9atZItDrYoV9cs5OTkyOVyeUwAgOL1+4oVRQ8KBfLyTi2HCqlchYW0tDQ5nU73FBsba3dJAFDh5GVnn99yx44VcyUoK8pVWBg1apQyMzPd0549e+wuCQAqHL/Q0PNbrkqVYq4EZUW5es5CUFCQgoKC7C4DACq0ylddJfn5+XYqws/v1HKokMrVkQUAQMnzr15d4V27ngoMReHnp/Bu3bi4sQKzNSwcO3ZMa9as0Zo1ayRJO3bs0Jo1a7R79247ywKAi161IUPk8POTHA7rjg6HHH5+qnbffaVTGGxha1hYtWqVkpKSlJSUJEkaOXKkkpKS9NRTT9lZFgBc9IIb1FfMW2/JERBw9iMMfn5yBAQo5q23eORzBecw5lxP3Ci7XC6XnE6nMjMzFR4ebnc5AFDh/LFpsw6NHy/XnDmFfxuiWzdVu+8+gkI55ct3KGEBAHBOuYcOnfrVyWPH5FelyqlfneQahXLNl+/QcnU3BADAHv7VqimcJ+5etLgbAgAAWCIsAAAAS4QFAABgibAAAAAsERYAAIAlwgIAALBEWAAAAJYICwAAwBJhAQAAWCIsAAAAS4QFAABgibAAAAAsERYAAIAlwgIAALBEWAAAAJYICwAAwBJhAQAAWLI9LLz99ttKSEhQcHCwrrjiCn377bd2lwQAAE5ja1j45JNP9NBDD+lvf/ubvv/+e1199dXq3r27du/ebWdZAADgNA5jjLHrzVu1aqUWLVpo3Lhx7rZGjRrppptuUlpa2jmXd7lccjqdyszMVHh4eEmWCgBAheLLd6htRxZOnDih1atXq0uXLh7tXbp00dKlS70uk5OTI5fL5TEBAICSZVtYOHjwoPLy8lSrVi2P9lq1amn//v1el0lLS5PT6XRPsbGxpVEqAAAXNdsvcHQ4HB6vjTGF2gqMGjVKmZmZ7mnPnj2lUSIAABc1f7veuHr16vLz8yt0FOHAgQOFjjYUCAoKUlBQUGmUBwAA/se2IwuBgYG64oorNG/ePI/2efPmqU2bNjZVBQAAzmTbkQVJGjlypAYMGKCWLVsqOTlZ48eP1+7duzV06FA7ywIAAKexNSzceuutOnTokMaMGaN9+/apSZMm+vrrrxUXF2dnWQAA4DS2PmfhQvGcBQAAzk+5eM4CAAAoHwgLAADAEmEBAABYIiwAAABLhAUAAGCJsAAAACwRFgAAgCXCAgAAsGTrExwvVMHzpFwul82VAABQvhR8dxbl2YzlOixkZWVJkmJjY22uBACA8ikrK0tOp9OyT7l+3HN+fr5++eUXGWNUt25d7dmzp8I/9tnlcik2NrbCj5VxViyMs2JhnBWDMUZZWVmKjo5WpUrWVyWU6yMLlSpVUkxMjPtQSnh4eIXcoN5cLGNlnBUL46xYGGf5d64jCgW4wBEAAFgiLAAAAEsVIiwEBQUpNTVVQUFBdpdS4i6WsTLOioVxViyM8+JTri9wBAAAJa9CHFkAAAAlh7AAAAAsERYAAIAlwgIAALBUJsNCWlqaHA6HHnroIXfbsWPH9MADDygmJkYhISFq1KiRxo0b57Fchw4d5HA4PKbbbrvNo8+RI0c0YMAAOZ1OOZ1ODRgwQEePHi2FUXl3PmPduXNnoXEWTNOmTXP3i4+PLzT/8ccfL83huXkb56+//qpBgwYpOjpalStXVrdu3bRlyxaP5XJycjRs2DBVr15doaGh6tWrl37++WePPmVpm57POA8fPqxhw4apQYMGqly5surWravhw4crMzPTY90VYXuWt330fMZZXvbPp59+utD7165d2z3fGKOnn35a0dHRCgkJUYcOHbRhwwaPdZSH/fNCx1ke988SYcqYFStWmPj4eNOsWTPz4IMPutvvuecek5iYaBYuXGh27Nhh3n33XePn52dmzJjh7tO+fXtz7733mn379rmno0ePeqy/W7dupkmTJmbp0qVm6dKlpkmTJqZnz56lNTwP5zvW3NxcjzHu27fPjB492oSGhpqsrCz3euLi4syYMWM8+p0+v7R4G2d+fr5p3bq1ufrqq82KFSvMTz/9ZO677z5Tt25dc+zYMfeyQ4cONXXq1DHz5s0zGRkZ5tprrzWXX365yc3NdfcpK9v0fMe5bt0607t3bzNr1iyzdetWs2DBAlOvXj1z8803e6y/ImzP8rSPnu84y8v+mZqaai677DKP9z9w4IB7/vPPP2/CwsLMZ599ZtatW2duvfVWExUVZVwul7tPedg/L3Sc5W3/LCllKixkZWWZevXqmXnz5pn27dt7fIFedtllZsyYMR79W7RoYZ544gn36zOXOdOPP/5oJJlly5a529LT040k89NPPxXbOIriQsd6pubNm5vBgwd7tMXFxZlXX321OMv22dnGuWnTJiPJrF+/3t03NzfXREZGmvfee88YY8zRo0dNQECAmTp1qrvP3r17TaVKlcycOXOMMWVnm17IOL359NNPTWBgoDl58qS7rbxvT2PKzz5a3NuzLO6fqamp5vLLL/c6Lz8/39SuXds8//zz7rY//vjDOJ1O88477xhjys/+eaHj9Kas7p8lqUydhkhJSVGPHj3UuXPnQvPatWunWbNmae/evTLGaOHChdq8ebO6du3q0e/DDz9U9erVddlll+mRRx5x/zKlJKWnp8vpdKpVq1buttatW8vpdGrp0qUlNzAvimOsBVavXq01a9bo7rvvLjTvhRdeULVq1dS8eXP9/e9/14kTJ4p9LFbONs6cnBxJUnBwsLvNz89PgYGB+u677ySdGtfJkyfVpUsXd5/o6Gg1adLEvb3Kyja9kHF6k5mZqfDwcPn7e/58S3nengXKwz5anNuzLO+fW7ZsUXR0tBISEnTbbbdp+/btkqQdO3Zo//79HvteUFCQ2rdv794O5Wn/vJBxelNW98+SVGZ+SGrq1KnKyMjQypUrvc5//fXXde+99yomJkb+/v6qVKmSJkyYoHbt2rn79O/fXwkJCapdu7bWr1+vUaNGae3atZo3b54kaf/+/apZs2ahddesWVP79+8vmYF5URxjPd3EiRPVqFEjtWnTxqP9wQcfVIsWLRQREaEVK1Zo1KhR2rFjhyZMmFDsY/LGapwNGzZUXFycRo0apXfffVehoaF65ZVXtH//fu3bt0/Sqe0VGBioiIgIj2Vr1arl3l5lYZte6DjPdOjQIT3zzDMaMmSIR3t5355S+dhHi3t7ltX9s1WrVvrggw9Uv359/frrr3r22WfVpk0bbdiwwf1Z16pVy2OZWrVqadeuXZLKz/55oeM8U1ndP0tamQgLe/bs0YMPPqhvvvnGI7Gf7vXXX9eyZcs0a9YsxcXFacmSJbr//vsVFRXlTv/33nuvu3+TJk1Ur149tWzZUhkZGWrRooUkyeFwFFq3McZre0korrEWOH78uD766CM9+eSThdYzYsQI9383a9ZMERER6tOnjzv9lqRzjTMgIECfffaZ7r77bkVGRsrPz0+dO3dW9+7dz7nuM7eXndu0uMfpcrnUo0cPNW7cWKmpqR7zKsL2LOv7aHFvz7K6f0ryqLlp06ZKTk5WYmKiJk+erNatW0sqvC2Ksh3K0v4pFe84y+r+WSrsOv9xuunTpxtJxs/Pzz1JMg6Hw/j5+Zljx46ZgIAA8+WXX3osd/fdd5uuXbuedb35+fke59QmTpxonE5noX5Op9P885//LNYxnU1xj/WDDz4wAQEBHhfsnM3PP/9c6PxhSTnXOE+/AOro0aPu+q+66ipz//33G2OMWbBggZFkDh8+7LHuZs2amaeeesoYY/82LY5xFnC5XCY5Odl06tTJHD9+/JzvXd62pzdlbR8t7nGW1f3zbDp37myGDh1qtm3bZiSZjIwMj/m9evUyd955pzGmfOyfZ+PLOAuU5f2zNJSJaxY6deqkdevWac2aNe6pZcuW6t+/v9asWaO8vDydPHlSlSp5luvn56f8/PyzrnfDhg06efKkoqKiJEnJycnKzMzUihUr3H2WL1+uzMzMQocIS0pxj3XixInq1auXatSocc73/v777yXJ/XmUpHON08/Pz93X6XSqRo0a2rJli1atWqUbb7xRknTFFVcoICDAfYhakvbt26f169e7t5fd27Q4ximd+oulS5cuCgwM1KxZs8561Ol05W17elPW9tHiHmdZ3T+9ycnJ0caNGxUVFeU+VXT6vnfixAktXrzYvR3Kw/7pja/jlMr+/lkq7E4rZ3PmVdPt27c3l112mVm4cKHZvn27mTRpkgkODjZvv/22McaYrVu3mtGjR5uVK1eaHTt2mK+++so0bNjQJCUlFbqNp1mzZiY9Pd2kp6ebpk2b2nbrZAFfx1pgy5YtxuFwmNmzZxda59KlS80rr7xivv/+e7N9+3bzySefmOjoaNOrV6+SHs5ZnTnOTz/91CxcuNBs27bNzJgxw8TFxZnevXt7LDN06FATExNj5s+fbzIyMkzHjh293ppVlrapr+N0uVymVatWpmnTpmbr1q0et14VjLMibM/yuo+ez79bY8r+/vnwww+bRYsWme3bt5tly5aZnj17mrCwMLNz505jzKlbCp1Op/n888/NunXrTL9+/bzeOlnW988LHWd53T+LW7kJC/v27TODBg0y0dHRJjg42DRo0MC8/PLLJj8/3xhjzO7du80111xjIiMjTWBgoElMTDTDhw83hw4d8ljvoUOHTP/+/U1YWJgJCwsz/fv3N0eOHCnFkRXm61gLjBo1ysTExJi8vLxC61y9erVp1aqVcTqd7nWkpqaa7Ozskh7OWZ05ztdee83ExMSYgIAAU7duXfPEE0+YnJwcj2WOHz9uHnjgARMZGWlCQkJMz549ze7duz36lLVt6us4Fy5caCR5nXbs2GGMqRjbs7zuo+fz79aYsr9/FjxPICAgwERHR5vevXubDRs2uOfn5+eb1NRUU7t2bRMUFGSuueYas27dOo91lIf980LHWV73z+LGT1QDAABLZeKaBQAAUHYRFgAAgCXCAgAAsERYAAAAlggLAADAEmEBAABYIiwAAABLhAUAAGCJsABchBYtWiSHw6GjR4/aXQqAcoCwAFRwHTp00EMPPeTR1qZNG+3bt09Op9Oeos4iPj5eY8eOtbsMAGfwt7sAAKUvMDBQtWvXtrsMAOUERxaACmzQoEFavHixXnvtNTkcDjkcDu3cubPQaYj3339fVatW1ZdffqkGDRqocuXK6tOnj7KzszV58mTFx8crIiJCw4YNU15ennv9J06c0KOPPqo6deooNDRUrVq10qJFiyxrevrpp1W3bl0FBQUpOjpaw4cPl3TqCMiuXbs0YsQId60Fli5dqmuuuUYhISGKjY3V8OHDlZ2d7Z4fHx+vZ555RrfffruqVKmi6OhovfHGG8X3QQIXO7t/yQpAyTl69KhJTk429957r8fP6hb8kl7Br/9NmjTJBAQEmOuuu85kZGSYxYsXm2rVqpkuXbqYW265xWzYsMF88cUXJjAw0EydOtW9/ttvv920adPGLFmyxGzdutW8+OKLJigoyGzevNlrPdOmTTPh4eHm66+/Nrt27TLLly8348ePN8ac+nXCmJgYM2bMGHetxhjzww8/mCpVqphXX33VbN682fz3v/81SUlJZtCgQe71xsXFmbCwMJOWlmY2bdpkXn/9dePn52e++eabEvpkgYsLYQGo4M78iWVjjNewIMls3brV3WfIkCGmcuXKJisry93WtWtXM2TIEGOMMVu3bjUOh8Ps3bvXY92dOnUyo0aN8lrLyy+/bOrXr29OnDjhdX5cXJx59dVXPdoGDBhg7rvvPo+2b7/91lSqVMkcP37cvVy3bt08+tx6662me/fuXt8HgG84DQFAklS5cmUlJia6X9eqVUvx8fGqUqWKR9uBAwckSRkZGTLGqH79+qpSpYp7Wrx4sbZt2+b1Pfr27avjx4/rkksu0b333qvp06crNzfXsq7Vq1fr/fff93iPrl27Kj8/Xzt27HD3S05O9lguOTlZGzdu9PlzAFAYFzgCkCQFBAR4vHY4HF7b8vPzJUn5+fny8/PT6tWr5efn59Hv9IBxutjYWG3atEnz5s3T/Pnzdf/99+vFF1/U4sWLC71Xgfz8fA0ZMsR9bcPp6tatazmm0697AHD+CAtABRcYGOhxUWJxSUpKUl5eng4cOKCrr766yMuFhISoV69e6tWrl1JSUtSwYUOtW7dOLVq08FprixYttGHDBl166aWW6122bFmh1w0bNiz6gACcFachgAouPj5ey5cv186dO3Xw4EH3kYELVb9+ffXv31933nmnPv/8c+3YsUMrV67UCy+8oK+//trrMu+//74mTpyo9evXa/v27frXv/6lkJAQxcXFuWtdsmSJ9u7dq4MHD0qSHnvsMaWnpyslJUVr1qzRli1bNGvWLA0bNsxj3f/973/1j3/8Q5s3b9Zbb72ladOm6cEHHyyWsQIXO8ICUME98sgj8vPzU+PGjVWjRg3t3r272NY9adIk3XnnnXr44YfVoEED9erVS8uXL1dsbKzX/lWrVtV7772ntm3bqlmzZlqwYIG++OILVatWTZI0ZswY7dy5U4mJiapRo4YkqVmzZlq8eLG2bNmiq6++WklJSXryyScVFRXlse6HH35Yq1evVlJSkp555hm9/PLL6tq1a7GNFbiYOYwxxu4iAOBCxMfH66GHHir0pEoAxYMjCwAAwBJhAQAAWOI0BAAAsMSRBQAAYImwAAAALBEWAACAJcICAACwRFgAAACWCAsAAMASYQEAAFgiLAAAAEv/BxK5ymc2puApAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["class ThompsonSampling(Solver):\n", "    \"\"\" 汤普森采样算法,继承Solver类 \"\"\"\n", "    def __init__(self, bandit):\n", "        super(<PERSON>, self).__init__(bandit)\n", "        self._a = np.ones(self.bandit.K)  # 列表,表示每根拉杆奖励为1的次数\n", "        self._b = np.ones(self.bandit.K)  # 列表,表示每根拉杆奖励为0的次数\n", "\n", "    def run_one_step(self):\n", "        samples = np.random.beta(self._a, self._b)  # 按照Beta分布采样一组奖励样本\n", "        k = np.argmax(samples)  # 选出采样奖励最大的拉杆\n", "        r = self.bandit.step(k)\n", "\n", "        self._a[k] += r  # 更新Beta分布的第一个参数\n", "        self._b[k] += (1 - r)  # 更新Beta分布的第二个参数\n", "        return k,r\n", "    \n", "np.random.seed(1)\n", "thompson_sampling_solver = ThompsonSampling(bandit_10_arm)\n", "thompson_sampling_solver.run(5000)\n", "animation_of_reward(thompson_sampling_solver.actual_reward)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}