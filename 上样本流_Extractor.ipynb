{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["remap_slot=657, dim=16, map_slot=657, size=0, slot=0, name=ExtractCombineSparseInnerOrderCvr, category=ad, field=498, feature_size=1000001, topic_id=0\n", "remap_slot=667, dim=16, map_slot=667, size=0, slot=0, name=ExtractCombineRankIndexUescorePctrNoPrefix, category=ad, field=499, feature_size=1001, topic_id=0\n", "remap_slot=668, dim=16, map_slot=668, size=0, slot=0, name=ExtractCombineRankIndexUescorePlvtrNoPrefix, category=ad, field=500, feature_size=1001, topic_id=0\n", "remap_slot=669, dim=16, map_slot=669, size=0, slot=0, name=ExtractCombineRankIndexUescorePsvrNoPrefix, category=ad, field=501, feature_size=1001, topic_id=0\n", "remap_slot=670, dim=16, map_slot=670, size=0, slot=0, name=ExtractCombineRankIndexUescorePvtrNoPrefix, category=ad, field=502, feature_size=1001, topic_id=0\n", "remap_slot=671, dim=16, map_slot=671, size=0, slot=0, name=ExtractCombineRankIndexUescorePwatchtimeNoPrefix, category=ad, field=503, feature_size=1001, topic_id=0\n", "remap_slot=672, dim=16, map_slot=672, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtdNoPrefix, category=ad, field=504, feature_size=1001, topic_id=0\n", "remap_slot=673, dim=16, map_slot=673, size=0, slot=0, name=ExtractCombineRankIndexUescorePcprNoPrefix, category=ad, field=505, feature_size=1001, topic_id=0\n", "remap_slot=674, dim=16, map_slot=674, size=0, slot=0, name=ExtractCombineRankIndexUescorePltrNoPrefix, category=ad, field=506, feature_size=1001, topic_id=0\n", "remap_slot=675, dim=16, map_slot=675, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtrNoPrefix, category=ad, field=507, feature_size=1001, topic_id=0\n", "remap_slot=676, dim=16, map_slot=676, size=0, slot=0, name=ExtractCombineRankIndexUescorePftrNoPrefix, category=ad, field=508, feature_size=1001, topic_id=0\n", "remap_slot=677, dim=16, map_slot=677, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmtrNoPrefix, category=ad, field=509, feature_size=1001, topic_id=0\n", "remap_slot=678, dim=16, map_slot=678, size=0, slot=0, name=ExtractCombineRankIndexUescorePhtrNoPrefix, category=ad, field=510, feature_size=1001, topic_id=0\n", "remap_slot=679, dim=16, map_slot=679, size=0, slot=0, name=ExtractCombineRankIndexUescorePclickLiveNoPrefix, category=ad, field=511, feature_size=1001, topic_id=0\n", "remap_slot=680, dim=16, map_slot=680, size=0, slot=0, name=ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix, category=ad, field=512, feature_size=1001, topic_id=0\n", "remap_slot=681, dim=16, map_slot=681, size=0, slot=0, name=ExtractCombineRankIndexUescorePptrNoPrefix, category=ad, field=513, feature_size=1001, topic_id=0\n", "remap_slot=682, dim=16, map_slot=682, size=0, slot=0, name=ExtractCombineRankIndexUescorePepstrNoPrefix, category=ad, field=514, feature_size=1001, topic_id=0\n", "remap_slot=683, dim=16, map_slot=683, size=0, slot=0, name=ExtractCombineRankIndexUescorePlstrNoPrefix, category=ad, field=515, feature_size=1001, topic_id=0\n", "remap_slot=684, dim=16, map_slot=684, size=0, slot=0, name=ExtractCombineRankIndexUescorePetcmNoPrefix, category=ad, field=516, feature_size=1001, topic_id=0\n", "remap_slot=685, dim=16, map_slot=685, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmefNoPrefix, category=ad, field=517, feature_size=1001, topic_id=0\n", "remap_slot=686, dim=16, map_slot=686, size=0, slot=0, name=ExtractCombineRankIndexInnerOrderCvrNoPrefix, category=ad, field=518, feature_size=1001, topic_id=0\n", "remap_slot=687, dim=16, map_slot=687, size=0, slot=0, name=ExtractCombineRankIndexLiveAudienceCtrNoPrefix, category=ad, field=519, feature_size=1001, topic_id=0\n"]}], "source": ["index = 656\n", "for l in \"\"\"remap_slot=656, dim=16, map_slot=656, size=0, slot=0, name=ExtractCombineSparseInnerOrderCvr, category=ad, field=498, feature_size=1000001, topic_id=0\n", "remap_slot=667, dim=16, map_slot=667, size=0, slot=0, name=ExtractCombineRankIndexUescorePctrNoPrefix, category=ad, field=499, feature_size=1001, topic_id=0\n", "remap_slot=668, dim=16, map_slot=668, size=0, slot=0, name=ExtractCombineRankIndexUescorePlvtrNoPrefix, category=ad, field=500, feature_size=1001, topic_id=0\n", "remap_slot=669, dim=16, map_slot=669, size=0, slot=0, name=ExtractCombineRankIndexUescorePsvrNoPrefix, category=ad, field=501, feature_size=1001, topic_id=0\n", "remap_slot=670, dim=16, map_slot=670, size=0, slot=0, name=ExtractCombineRankIndexUescorePvtrNoPrefix, category=ad, field=502, feature_size=1001, topic_id=0\n", "remap_slot=671, dim=16, map_slot=671, size=0, slot=0, name=ExtractCombineRankIndexUescorePwatchtimeNoPrefix, category=ad, field=503, feature_size=1001, topic_id=0\n", "remap_slot=672, dim=16, map_slot=672, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtdNoPrefix, category=ad, field=504, feature_size=1001, topic_id=0\n", "remap_slot=673, dim=16, map_slot=673, size=0, slot=0, name=ExtractCombineRankIndexUescorePcprNoPrefix, category=ad, field=505, feature_size=1001, topic_id=0\n", "remap_slot=674, dim=16, map_slot=674, size=0, slot=0, name=ExtractCombineRankIndexUescorePltrNoPrefix, category=ad, field=506, feature_size=1001, topic_id=0\n", "remap_slot=675, dim=16, map_slot=675, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtrNoPrefix, category=ad, field=507, feature_size=1001, topic_id=0\n", "remap_slot=676, dim=16, map_slot=676, size=0, slot=0, name=ExtractCombineRankIndexUescorePftrNoPrefix, category=ad, field=508, feature_size=1001, topic_id=0\n", "remap_slot=677, dim=16, map_slot=677, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmtrNoPrefix, category=ad, field=509, feature_size=1001, topic_id=0\n", "remap_slot=678, dim=16, map_slot=678, size=0, slot=0, name=ExtractCombineRankIndexUescorePhtrNoPrefix, category=ad, field=510, feature_size=1001, topic_id=0\n", "remap_slot=679, dim=16, map_slot=679, size=0, slot=0, name=ExtractCombineRankIndexUescorePclickLiveNoPrefix, category=ad, field=511, feature_size=1001, topic_id=0\n", "remap_slot=680, dim=16, map_slot=680, size=0, slot=0, name=ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix, category=ad, field=512, feature_size=1001, topic_id=0\n", "remap_slot=681, dim=16, map_slot=681, size=0, slot=0, name=ExtractCombineRankIndexUescorePptrNoPrefix, category=ad, field=513, feature_size=1001, topic_id=0\n", "remap_slot=682, dim=16, map_slot=682, size=0, slot=0, name=ExtractCombineRankIndexUescorePepstrNoPrefix, category=ad, field=514, feature_size=1001, topic_id=0\n", "remap_slot=683, dim=16, map_slot=683, size=0, slot=0, name=ExtractCombineRankIndexUescorePlstrNoPrefix, category=ad, field=515, feature_size=1001, topic_id=0\n", "remap_slot=684, dim=16, map_slot=684, size=0, slot=0, name=ExtractCombineRankIndexUescorePetcmNoPrefix, category=ad, field=516, feature_size=1001, topic_id=0\n", "remap_slot=685, dim=16, map_slot=685, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmefNoPrefix, category=ad, field=517, feature_size=1001, topic_id=0\n", "remap_slot=686, dim=16, map_slot=686, size=0, slot=0, name=ExtractCombineRankIndexInnerOrderCvrNoPrefix, category=ad, field=518, feature_size=1001, topic_id=0\n", "remap_slot=687, dim=16, map_slot=687, size=0, slot=0, name=ExtractCombineRankIndexLiveAudienceCtrNoPrefix, category=ad, field=519, feature_size=1001, topic_id=0\"\"\".splitlines():\n", "    print(l.replace(str(index), str(index+1)))\n", "    index += 1"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["3635973"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["s = 0\n", "for i in \"\"\"46086\n", "100273\n", "51821\n", "170758\n", "73150\n", "87936\n", "109794\n", "98695\n", "108904\n", "121827\n", "119712\n", "202007\n", "243126\n", "94502\n", "210814\n", "88856\n", "75082\n", "116866\n", "206956\n", "82100\n", "156421\n", "201639\n", "63836\n", "174275\n", "67313\n", "68248\n", "94125\n", "64698\n", "336153\"\"\".splitlines():\n", "    s += int(i)\n", "s"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ExtractCombineRankIndexUescorePctrNoPrefix,\n", "ExtractCombineRankIndexUescorePlvtrNoPrefix,\n", "ExtractCombineRankIndexUescorePsvrNoPrefix,\n", "ExtractCombineRankIndexUescorePvtrNoPrefix,\n", "ExtractCombineRankIndexUescorePwatchtimeNoPrefix,\n", "ExtractCombineRankIndexUescorePwtdNoPrefix,\n", "ExtractCombineRankIndexUescorePcprNoPrefix,\n", "ExtractCombineRankIndexUescorePltrNoPrefix,\n", "ExtractCombineRankIndexUescorePwtrNoPrefix,\n", "ExtractCombineRankIndexUescorePftrNoPrefix,\n", "ExtractCombineRankIndexUescorePcmtrNoPrefix,\n", "ExtractCombineRankIndexUescorePhtrNoPrefix,\n", "ExtractCombineRankIndexUescorePclickLiveNoPrefix,\n", "ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix,\n", "ExtractCombineRankIndexUescorePptrNoPrefix,\n", "ExtractCombineRankIndexUescorePepstrNoPrefix,\n", "ExtractCombineRankIndexUescorePlstrNoPrefix,\n", "ExtractCombineRankIndexUescorePetcmNoPrefix,\n", "ExtractCombineRankIndexUescorePcmefNoPrefix,\n", "ExtractCombineRankIndexInnerOrderCvrNoPrefix,\n", "ExtractUserRankInnerOrderNum,\n", "ExtractUserRankUescoreNum\n"]}], "source": ["# 特征size都是1001\n", "# combine\n", "combine = \"\"\"ExtractCombineRankIndexUescorePctrNoPrefix\n", "ExtractCombineRankIndexUescorePlvtrNoPrefix\n", "ExtractCombineRankIndexUescorePsvrNoPrefix\n", "ExtractCombineRankIndexUescorePvtrNoPrefix\n", "ExtractCombineRankIndexUescorePwatchtimeNoPrefix\n", "ExtractCombineRankIndexUescorePwtdNoPrefix\n", "ExtractCombineRankIndexUescorePcprNoPrefix\n", "ExtractCombineRankIndexUescorePltrNoPrefix\n", "ExtractCombineRankIndexUescorePwtrNoPrefix\n", "ExtractCombineRankIndexUescorePftrNoPrefix\n", "ExtractCombineRankIndexUescorePcmtrNoPrefix\n", "ExtractCombineRankIndexUescorePhtrNoPrefix\n", "ExtractCombineRankIndexUescorePclickLiveNoPrefix\n", "ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix\n", "ExtractCombineRankIndexUescorePptrNoPrefix\n", "ExtractCombineRankIndexUescorePepstrNoPrefix\n", "ExtractCombineRankIndexUescorePlstrNoPrefix\n", "ExtractCombineRankIndexUescorePetcmNoPrefix\n", "ExtractCombineRankIndexUescorePcmefNoPrefix\n", "ExtractCombineRankIndexInnerOrderCvrNoPrefix\"\"\".splitlines()\n", "user = \"\"\"ExtractUserRankInnerOrderNum\n", "ExtractUserRankUescoreNum\"\"\".splitlines()\n", "len(combine) + len(user)\n", "print(',\\n'.join(combine + user))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## fg_schema_out"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["column_name=ExtractCombineRankIndexUescorePctrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePlvtrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePsvrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePvtrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePwatchtimeNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePwtdNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePcprNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePltrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePwtrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePftrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePcmtrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePhtrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePclickLiveNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePptrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePepstrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePlstrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePetcmNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexUescorePcmefNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractCombineRankIndexInnerOrderCvrNoPrefix, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractUserRankInnerOrderNum, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n", "column_name=ExtractUserRankUescoreNum, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\n"]}], "source": ["for i in combine:\n", "    print(f\"column_name={i}, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\")\n", "for i in user:\n", "    print(f\"column_name={i}, compression_type=None, column_type=List<Long>, feature_extractor_flag = true\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## sql"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ExtractCombineRankIndexUescorePctrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePlvtrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePsvrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePvtrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePwatchtimeNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePwtdNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePcprNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePltrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePwtrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePftrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePcmtrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePhtrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePclickLiveNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePptrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePepstrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePlstrNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePetcmNoPrefix array<bigint>,\n", "ExtractCombineRankIndexUescorePcmefNoPrefix array<bigint>,\n", "ExtractCombineRankIndexInnerOrderCvrNoPrefix array<bigint>,\n", "ExtractUserRankInnerOrderNum array<bigint>,\n", "ExtractUserRankUescoreNum array<bigint>,\n"]}], "source": ["for i in combine:\n", "    print(f\"{i} array<bigint>,\")\n", "for i in user:\n", "    print(f\"{i} array<bigint>,\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# kai_feature_old.txt"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["class=ExtractCombineRankIndexUescorePctrNoPrefix, category=combine, field=0, slot=303, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePlvtrNoPrefix, category=combine, field=0, slot=304, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePsvrNoPrefix, category=combine, field=0, slot=305, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePvtrNoPrefix, category=combine, field=0, slot=306, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePwatchtimeNoPrefix, category=combine, field=0, slot=307, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePwtdNoPrefix, category=combine, field=0, slot=308, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePcprNoPrefix, category=combine, field=0, slot=309, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePltrNoPrefix, category=combine, field=0, slot=310, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePwtrNoPrefix, category=combine, field=0, slot=311, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePftrNoPrefix, category=combine, field=0, slot=312, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePcmtrNoPrefix, category=combine, field=0, slot=313, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePhtrNoPrefix, category=combine, field=0, slot=314, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePclickLiveNoPrefix, category=combine, field=0, slot=315, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix, category=combine, field=0, slot=316, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePptrNoPrefix, category=combine, field=0, slot=317, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePepstrNoPrefix, category=combine, field=0, slot=318, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePlstrNoPrefix, category=combine, field=0, slot=319, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePetcmNoPrefix, category=combine, field=0, slot=320, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexUescorePcmefNoPrefix, category=combine, field=0, slot=321, size=1001, topic_id=0\n", "class=ExtractCombineRankIndexInnerOrderCvrNoPrefix, category=combine, field=0, slot=322, size=1001, topic_id=0\n", "class=ExtractUserRankInnerOrderNum, category=user, field=0, slot=301, size=1001, topic_id=0\n", "class=ExtractUserRankUescoreNum, category=user, field=0, slot=302, size=1001, topic_id=0\n"]}], "source": ["start_slot = 303\n", "\n", "slot = start_slot\n", "for i in combine:\n", "    size = 1001\n", "    category = 'combine'\n", "    # concat_len = 50 * dim\n", "    print(f\"class={i}, category={category}, field=0, slot={slot}, size={size}, topic_id=0\")\n", "    slot += 1\n", "\n", "start_slot = 301\n", "slot = start_slot\n", "for i in user:\n", "    size = 1001\n", "    category = 'user'\n", "    print(f\"class={i}, category={category}, field=0, slot={slot}, size={size}, topic_id=0\")\n", "    slot += 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# feature_map_for_ps_old.txt"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["remap_slot=667, dim=16, map_slot=667, size=0, slot=0, name=ExtractCombineRankIndexUescorePctrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=668, dim=16, map_slot=668, size=0, slot=0, name=ExtractCombineRankIndexUescorePlvtrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=669, dim=16, map_slot=669, size=0, slot=0, name=ExtractCombineRankIndexUescorePsvrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=670, dim=16, map_slot=670, size=0, slot=0, name=ExtractCombineRankIndexUescorePvtrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=671, dim=16, map_slot=671, size=0, slot=0, name=ExtractCombineRankIndexUescorePwatchtimeNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=672, dim=16, map_slot=672, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtdNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=673, dim=16, map_slot=673, size=0, slot=0, name=ExtractCombineRankIndexUescorePcprNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=674, dim=16, map_slot=674, size=0, slot=0, name=ExtractCombineRankIndexUescorePltrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=675, dim=16, map_slot=675, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=676, dim=16, map_slot=676, size=0, slot=0, name=ExtractCombineRankIndexUescorePftrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=677, dim=16, map_slot=677, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmtrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=678, dim=16, map_slot=678, size=0, slot=0, name=ExtractCombineRankIndexUescorePhtrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=679, dim=16, map_slot=679, size=0, slot=0, name=ExtractCombineRankIndexUescorePclickLiveNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=680, dim=16, map_slot=680, size=0, slot=0, name=ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=681, dim=16, map_slot=681, size=0, slot=0, name=ExtractCombineRankIndexUescorePptrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=682, dim=16, map_slot=682, size=0, slot=0, name=ExtractCombineRankIndexUescorePepstrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=683, dim=16, map_slot=683, size=0, slot=0, name=ExtractCombineRankIndexUescorePlstrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=684, dim=16, map_slot=684, size=0, slot=0, name=ExtractCombineRankIndexUescorePetcmNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=685, dim=16, map_slot=685, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmefNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=686, dim=16, map_slot=686, size=0, slot=0, name=ExtractCombineRankIndexInnerOrderCvrNoPrefix, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=665, dim=16, map_slot=665, size=0, slot=0, name=ExtractUserRankInnerOrderNum, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=666, dim=16, map_slot=666, size=0, slot=0, name=ExtractUserRankUescoreNum, category=ad, field=0, feature_size=1001, topic_id=0\n"]}], "source": ["remap_start_slot = 667\n", "\n", "slot = remap_start_slot\n", "for i in combine:\n", "    size = 1001\n", "    dim=16\n", "    category = 'combine'\n", "    print(f\"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0\")\n", "    slot += 1\n", "\n", "remap_start_slot = 665\n", "slot = remap_start_slot\n", "for i in user:\n", "    size = 1001\n", "    dim=16\n", "    category = 'user'\n", "    print(f\"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0\")\n", "    slot += 1"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}