# Grafana AUC 数据爬虫 - HTTP 请求版本

## 概述

这是一个基于 HTTP 请求的 Grafana AUC 数据爬虫，替代了之前基于 Selenium 的版本。新版本直接发送 HTTP 请求到 Grafana API 获取数据，具有更高的效率和稳定性。

## 主要改进

### 1. 技术栈变更
- **之前**: Selenium + Chrome WebDriver
- **现在**: requests + 直接 API 调用

### 2. 性能提升
- 无需启动浏览器，执行速度更快
- 减少了系统资源占用
- 更稳定，不受页面加载时间影响

### 3. 维护性改善
- 代码更简洁，易于理解和维护
- 减少了外部依赖（不需要 Chrome 和 WebDriver）
- 更容易调试和排错

## 核心功能

### 1. 数据获取
- 直接调用 Grafana 数据源 API
- 支持 AUC 和 UE 相关指标
- 自动处理时间戳转换

### 2. 数据处理
- 自动计算实验组和对照组的差值
- 支持多种指标类型（auc, ue_auc, ue_rocket_auc）
- 智能时间范围处理：
  - **历史日期**: 使用当天 0:00 - 23:59 的完整日期范围
  - **今日数据**: 使用当前时刻往前推 24 小时的滚动窗口
- 生成详细的统计报告

### 3. 数据存储
- CSV 格式存储历史数据
- Markdown 格式生成报告
- 自动去重，避免重复数据

## API 请求详情

### 请求格式
```
POST https://grafana.corp.kuaishou.com/api/datasources/proxy/5257?
```

### 关键请求头
- `x-from`: 开始时间戳
- `x-to`: 结束时间戳
- `x-grafana-org-id`: 组织ID (3)
- `x-panel-id`: 面板ID (5)

### SQL 查询模板
```sql
SELECT 
    (intDiv(toUInt32(timestamp), 60) * 60) * 1000 AS t,
    extra2,
    extra3,
    AVG(average) / 10000 AS AUC 
FROM perf.cpp_mio_learner_perf_log 
WHERE 
    `timestamp` >= toDateTime({start_timestamp})
    AND `timestamp` < toDateTime({end_timestamp})
    AND namespace = 'mio-learner'
    AND subtag like 'auc%'
    AND extra1 = '{model_name}'
    AND extra2 in ('auc','ue_auc','ue_rocket_auc')
GROUP BY t, extra2, extra3 
HAVING AUC >= -3000 
ORDER BY t, AUC DESC 
FORMAT JSON
```

## 配置说明

### config.yaml 格式
```yaml
exp_sets:
  - base: "cart_order_re_grpo_v"
    exp: "dsp_lps_sv_xt_good_click_soft_re_grpo_v2"
    start: "2025-01-14"
    end: "2025-01-14"
    target: "auc"  # 或 "ue"
```

### 参数说明
- `base`: 对照组模型名称
- `exp`: 实验组模型名称
- `start`: 开始日期 (YYYY-MM-DD)
- `end`: 结束日期 (YYYY-MM-DD，可选)
- `target`: 指标类型 ("auc" 或 "ue")

## 认证配置

### Cookie 设置
脚本中硬编码了必要的认证 cookies，包括：
- `grafana_session_v3`: Grafana 会话ID
- `accessproxy_session`: 访问代理会话
- 其他必要的认证信息

**注意**: 需要定期更新这些 cookies 以保持认证有效性。

## 输出格式

### 控制台输出
- 实时显示查询进度
- 显示获取到的数据条数
- 智能时间范围显示：
  - 历史日期：显示标准日期范围
  - 今日数据：显示 `(Last 24h)` 标识和具体时间范围
- 展示今日临时数据（基于 last 24h）
- 汇总历史数据

### 文件输出
1. **CSV 文件**: `AUC_{exp_name}.csv`
   - 包含历史数据
   - 自动去重和排序

2. **Markdown 文件**: `markdown/AUC_{exp_name}.md`
   - 表格格式的报告
   - 便于分享和查看

## 时间范围逻辑

### 历史日期处理
对于过去的日期，使用标准的日期边界：
- 开始时间：当天 00:00:00
- 结束时间：当天 23:59:59

### 今日数据处理（Last 24h）
对于今天的数据，使用滚动 24 小时窗口：
- 结束时间：当前时刻
- 开始时间：当前时刻 - 24 小时

**优势**：
- 更准确反映最近 24 小时的模型表现
- 避免因为时间不完整导致的数据偏差
- 提供更实时的监控数据

**示例**：
```
历史日期: 2025-09-14
时间范围: 2025-09-14 00:00:00 - 2025-09-14 23:59:59

今日数据: 2025-09-15 (Last 24h)
时间范围: 2025-09-14 17:32:06 - 2025-09-15 17:32:06
```

## 使用方法

1. 更新 `config.yaml` 配置文件
2. 确保认证 cookies 有效
3. 运行脚本：
   ```bash
   python grafana_scraper.py
   ```

## 故障排除

### 常见问题
1. **获取到 0 条数据**
   - 检查模型名称是否正确
   - 确认时间范围内有数据
   - 验证认证信息是否有效

2. **请求失败**
   - 更新 cookies
   - 检查网络连接
   - 确认 API 端点可访问

3. **数据解析错误**
   - 检查返回的 JSON 格式
   - 验证字段名称是否匹配

### 调试技巧
- 启用详细日志输出
- 检查 HTTP 响应状态码
- 验证 SQL 查询语法

## 依赖项

```
pandas
requests
pyyaml
```

安装命令：
```bash
pip install pandas requests pyyaml
```
