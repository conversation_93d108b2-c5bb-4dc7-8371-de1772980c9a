import browser_cookie3

def read_cookies():
    # 从本机 Chrome 获取 Grafana 对应的 cookie
    cj = browser_cookie3.chrome()
    needed_domains = [
        "grafana.corp.kuaishou.com",
        ".grafana.corp.kuaishou.com",
        # ".corp.kuaishou.com",
        # ".kuaishou.com"
    ]
    cj = [c for c in cj if c.domain in needed_domains]

    # 把本地浏览器的 cookie 导入 Selenium
    cookies = []
    for c in cj:

        cookie_dict = {
            "name": c.name,
            "value": c.value,
            "domain": c.domain,
            "path": c.path,
            # "secure": c.secure,
            "httpOnly": c.has_nonstandard_attr('HttpOnly')
        }
        # Selenium 对跨域 cookie 有限制，要确保 domain 对得上
        try:
            # driver.add_cookie(cookie_dict)
            cookies.append(cookie_dict)
            print(cookie_dict)
        except Exception as e:
            print(f"跳过无法添加的cookie: {c.name} - {e}")
    return cookies
