import os
import pandas as pd
import requests
from datetime import datetime, timedelta
import yaml
import argparse
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# 设置pandas显示选项，避免在窄terminal中省略内容
pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.width', None)        # 不限制显示宽度
pd.set_option('display.max_colwidth', None) # 不限制列宽

# 全局变量存储最新的cookies
CURRENT_COOKIES = None
COOKIES_CACHE_FILE = "cookies_cache.json"

def save_cookies_to_cache(cookies):
    """保存cookies到缓存文件"""
    cache_data = {
        'cookies': cookies,
        'timestamp': datetime.now().isoformat()
    }
    try:
        with open(COOKIES_CACHE_FILE, 'w') as f:
            json.dump(cache_data, f, indent=2)
    except Exception as e:
        print(f"保存cookies缓存失败: {e}")

def load_cookies_from_cache():
    """从缓存文件加载cookies"""
    try:
        if os.path.exists(COOKIES_CACHE_FILE):
            with open(COOKIES_CACHE_FILE, 'r') as f:
                cache_data = json.load(f)

            # 检查缓存时间（cookies有效期通常为几小时到几天）
            cache_time = datetime.fromisoformat(cache_data['timestamp'])
            now = datetime.now()

            # 如果缓存时间超过6小时，认为可能过期
            if (now - cache_time).total_seconds() > 6 * 3600:
                return None, True  # cookies可能过期

            return cache_data['cookies'], False  # cookies可能仍有效
    except Exception as e:
        print(f"加载cookies缓存失败: {e}")

    return None, True  # 无缓存或加载失败

def test_cookies_validity(cookies, verbose=False):
    """测试cookies是否有效"""
    try:
        # 发送一个简单的测试请求
        test_url = 'https://grafana.corp.kuaishou.com/api/datasources/proxy/5257?'
        headers = {
            'accept': 'application/json, text/plain, */*',
            'x-grafana-org-id': '3',
        }

        # 构建一个简单的测试查询
        test_data = """---Cluster:ssd, TimeRangeLength:100
---from:https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong
---qtype:time_series
---user:xiatian06
SELECT 1 FORMAT JSON"""

        response = requests.post(test_url, headers=headers, cookies=cookies,
                               data=test_data, timeout=10)

        if verbose:
            print(f"Cookies测试响应状态码: {response.status_code}")

        return response.status_code != 401
    except Exception as e:
        if verbose:
            print(f"Cookies测试失败: {e}")
        return False

def get_valid_cookies(verbose=False):
    """获取有效的cookies，优先使用缓存"""
    global CURRENT_COOKIES

    # 如果内存中已有cookies，先测试是否有效
    if CURRENT_COOKIES:
        if test_cookies_validity(CURRENT_COOKIES, verbose):
            if verbose:
                print("使用内存中的有效cookies")
            return CURRENT_COOKIES
        else:
            if verbose:
                print("内存中的cookies已失效")
            CURRENT_COOKIES = None

    # 尝试从缓存文件加载cookies
    cached_cookies, might_be_expired = load_cookies_from_cache()
    if cached_cookies and not might_be_expired:
        if test_cookies_validity(cached_cookies, verbose):
            if verbose:
                print("使用缓存文件中的有效cookies")
            CURRENT_COOKIES = cached_cookies
            return cached_cookies
        else:
            if verbose:
                print("缓存文件中的cookies已失效")

    # 所有缓存的cookies都无效，使用Selenium获取新的
    if verbose:
        print("需要使用Selenium获取新的cookies")
    new_cookies = refresh_cookies_with_selenium(verbose)

    if new_cookies:
        CURRENT_COOKIES = new_cookies
        save_cookies_to_cache(new_cookies)
        return new_cookies

    return None

def refresh_cookies_with_selenium(verbose=False):
    """使用Selenium获取新的cookies"""
    global CURRENT_COOKIES

    if verbose:
        print("检测到认证失败，正在使用Selenium获取新的cookies...")

    # 配置Chrome选项
    options = Options()
    options.add_argument("--headless")  # 无头模式
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")

    driver = None
    try:
        driver = webdriver.Chrome(options=options)

        # 访问Grafana页面
        grafana_url = "https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong"
        driver.get(grafana_url)

        # 等待页面加载
        time.sleep(5)

        # 检查是否需要登录
        try:
            # 等待页面完全加载，可以通过检查特定元素来确认
            WebDriverWait(driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            time.sleep(3)  # 额外等待确保所有cookies都设置完成
        except:
            if verbose:
                print("页面加载超时，但继续尝试获取cookies")

        # 获取所有cookies
        selenium_cookies = driver.get_cookies()

        # 转换为requests格式的cookies字典
        new_cookies = {}
        for cookie in selenium_cookies:
            new_cookies[cookie['name']] = cookie['value']

        if new_cookies:
            CURRENT_COOKIES = new_cookies
            if verbose:
                print(f"成功获取到 {len(new_cookies)} 个cookies")
                print("主要cookies:", list(new_cookies.keys())[:5])
            return new_cookies
        else:
            if verbose:
                print("未能获取到有效的cookies")
            return None

    except Exception as e:
        if verbose:
            print(f"使用Selenium获取cookies失败: {e}")
        return None
    finally:
        if driver:
            driver.quit()

def get_grafana_data(exp, base, start, end, target, existing_dates, verbose=False):
    results = []

    # 设置默认的start和end日期
    if end is None:
        end = datetime.now().date()  # 今天
    else:
        end = datetime.strptime(end, "%Y-%m-%d").date()

    if start is None:
        start = end - timedelta(days=3)  # 今天之前的3天
    else:
        start = datetime.strptime(start, "%Y-%m-%d").date()

    # 遍历从start到end的每一天
    current_date = start
    while current_date <= end:
        if current_date != datetime.now().date() and current_date.isoformat() in existing_dates:
            current_date += timedelta(days=1)
            continue  # 跳过已存在的过去的日期的结果

        date_key = current_date.isoformat()

        try:
            # 判断是否为今天，如果是今天则使用last 24h逻辑
            if current_date == datetime.now().date():
                # 今天：使用当前时刻往前推24小时
                now = datetime.now()
                end_timestamp = int(now.timestamp())
                start_timestamp = int((now - timedelta(hours=24)).timestamp())
                if verbose:
                    print(f"\n指标：{target}, 查询日期: {current_date} (Last 24h)")
                    print(f"时间戳范围: {start_timestamp} - {end_timestamp}")
                    print(f"时间范围: {datetime.fromtimestamp(start_timestamp)} - {datetime.fromtimestamp(end_timestamp)}")
            else:
                # 历史日期：使用当天的开始和结束时间戳（秒）
                day_start = datetime.combine(current_date, datetime.min.time())
                day_end = datetime.combine(current_date, datetime.max.time())
                day_start = day_start.replace(microsecond=0)
                day_end = day_end.replace(microsecond=0)
                start_timestamp = int(day_start.timestamp())
                end_timestamp = int(day_end.timestamp())
                if verbose:
                    print(f"\n指标：{target}, 查询日期: {current_date}")
                    print(f"时间戳范围: {start_timestamp} - {end_timestamp}")

            # 发送HTTP请求获取数据
            response_data = fetch_auc_data(exp, start_timestamp, end_timestamp, target, verbose)

            if not response_data or 'data' not in response_data:
                if verbose:
                    print(f"未获取到有效数据: {current_date}")
                current_date += timedelta(days=1)
                continue

            # 解析返回的数据
            day_results = parse_auc_response(response_data, exp, base, target, date_key, start_timestamp, end_timestamp, verbose)

            if day_results:
                results.append(day_results)

        except Exception as e:
            print(f"{date_key} 发生错误: {e}")

        current_date += timedelta(days=1)

    return results


def fetch_auc_data(model_name, start_timestamp, end_timestamp, target, verbose=False):
    """发送HTTP请求获取AUC数据"""

    # 构建请求URL
    url = 'https://grafana.corp.kuaishou.com/api/datasources/proxy/5257?'

    # 设置请求头
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://grafana.corp.kuaishou.com',
        'priority': 'u=1, i',
        'referer': 'https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong',
        'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
        'x-caller': 'Grafana',
        'x-dashboard-id': '17635',
        'x-dashboard-uid': 'dR4px1qWz',
        'x-from': str(start_timestamp),
        'x-grafana-org-id': '3',
        'x-panel-id': '5',
        'x-realuser': 'xiatian06',
        'x-step': '60',
        'x-to': str(end_timestamp)
    }

    # 获取有效的cookies（优先使用缓存）
    cookies = get_valid_cookies(verbose)
    if not cookies:
        # 如果无法获取有效cookies，使用默认cookies作为后备
        if verbose:
            print("无法获取有效cookies，使用默认cookies")
        cookies = {
            '_did': 'web_358191091D5C6D81',
            'hdige2wqwoino': 'jFyZBDYxQi54p6HXp82FcBPMm8D3YKtSdf67f23b',
            'ksCorpDeviceid': '8807abce-95aa-4144-aa4d-ea05b106ea9e',
            'idp_theme_change_identification': '2',
            'tiangong_user_id': 'xiatian06',
            '$$$$idp_theme_change_identificationcopy_test': '2',
            'did': 'web_994895bf210a4746ad99e0100c772f00',
            'didv': '1749363345405',
            'apdid': '46f7fcfa-7af2-44e4-956a-ff59acaa43d3313b07875472b715e1f8a10d5c28f26b:1752472054:1',
            'ks_sso_gray_cookie': '1',
            'accessproxy_session': '14177363-f2a1-4b78-8636-ec04f9f388fa',
            'kwpsecproductname': 'PCLive',
            'kwfv1': 'PnGU+9+Y8008S+nH0U+0mjPf8fP08f+98f+nLlwnrIP9P9G98YPf8jPBQSweS0+nr9G0mD8B+fP/L98/qlPe4f8Bpf8e4j+0Z980LMPf+0w/Zlw/GI+ncEGnHUweG7PeHU+erAG/QD+9zYwerU8f+S+ecE8eHUGAQjP0rF8BQYwBG=',
            'Hm_lvt_86a27b7db2c5c0ae37fee4a8a35033ee': '1757064821',
            'ks_security_encrypt': 'ChVjb3Jwc2VjLnNzby5jYXMudG9rZW4SIEnQdIkbfIpGK5ZHXecfKKxRN0R829zMGTstiSC7E05qGhLRqY2kaa2mejBq4Q6dzj6iO+AiICvYhwVDJ/9W5hfXcJzNXn+nnljo1S/luiONfoKjP9SQKAUwAQ==',
            '_ga_H896PJ54TF': 'GS2.1.s1757335052$o4$g1$t1757335140$j60$l0$h0',
            '_ga': 'GA1.2.1077446757.1744697248',
            '_ga_F6CM1VE30P': 'GS2.1.s1757421322$o20$g1$t1757421509$j60$l0$h0',
            'grafana_session_v3': '90c8d4f1e17c607e2279f9925d307017',
            'ehid': '4Hp3buL_3GHZWSU6XtesFUAbyAcgIP_fVmi-3'
        }

    # 构建SQL查询
    if target == 'ue':
        target_filter = "AND extra2 in ('auc','ue_auc','ue_rocket_auc')"
    else:
        target_filter = "AND extra2 in ('auc')"

    sql_query = f"""SELECT     (intDiv(toUInt32(timestamp), 60) * 60) * 1000 AS t,     extra2,     extra3,     AVG(average) / 10000 AS AUC FROM perf.cpp_mio_learner_perf_log WHERE     `timestamp` >= toDateTime({start_timestamp})     AND `timestamp` < toDateTime({end_timestamp})     AND namespace = 'mio-learner'     AND subtag like 'auc%'     AND extra1 = '{model_name}'     {target_filter} GROUP BY t, extra2,extra3 HAVING AUC>=-3000 ORDER BY t, AUC DESC FORMAT JSON"""

    # 构建请求数据
    data = f"""---Cluster:ssd, TimeRangeLength:4513
---from:https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong
---qtype:time_series
---user:xiatian06
{sql_query}"""

    # 发送请求
    try:
        if verbose:
            print(f"发送请求到: {url}")
            print(f"查询模型: {model_name}")

        response = requests.post(url, headers=headers, cookies=cookies, data=data, timeout=30)
        if verbose:
            print(f"响应状态码: {response.status_code}")

        if response.status_code == 401:
            # 401错误，cookies可能已过期，强制刷新
            if verbose:
                print("检测到401错误，cookies已过期，强制刷新...")
            new_cookies = refresh_cookies_with_selenium(verbose)
            if new_cookies:
                # 更新全局cookies并重试
                global CURRENT_COOKIES
                CURRENT_COOKIES = new_cookies
                save_cookies_to_cache(new_cookies)

                if verbose:
                    print("cookies已刷新，重试请求...")
                response = requests.post(url, headers=headers, cookies=new_cookies, data=data, timeout=30)
                if verbose:
                    print(f"重试响应状态码: {response.status_code}")

        if response.status_code != 200:
            if verbose:
                print(f"响应内容: {response.text[:500]}")
            return None

        response_json = response.json()
        if verbose:
            print(f"获取到 {len(response_json.get('data', []))} 条数据")
        return response_json

    except Exception as e:
        if verbose:
            print(f"请求失败: {e}")
        return None


def parse_auc_response(response_data, exp, base, target, date_key, start_timestamp, end_timestamp, verbose=False):
    """解析AUC响应数据"""

    if not response_data.get('data'):
        return None

    day_results = {"date": date_key, "auc": {}, "ue_auc": {}, "ue_rocket_auc": {},
                   "start_timestamp": start_timestamp, "end_timestamp": end_timestamp}

    # 按指标类型分组数据
    metrics_data = {}
    for item in response_data['data']:
        metric_type = item['extra2']  # auc, ue_auc, ue_rocket_auc
        auc_value = item['AUC']

        if metric_type not in metrics_data:
            metrics_data[metric_type] = []
        metrics_data[metric_type].append(auc_value)

    # 计算每个指标的平均值
    for metric_type, values in metrics_data.items():
        if values:
            avg_value = sum(values) / len(values)
            day_results[metric_type][exp] = round(avg_value, 4)
            if verbose:
                print(f"{metric_type}: {avg_value:.4f}")

    # 如果需要base数据，发送另一个请求获取base模型的数据
    if base and base != exp:
        # 使用相同的时间戳范围获取base数据
        base_data = fetch_auc_data(base, start_timestamp, end_timestamp, target, verbose)

        if base_data and base_data.get('data'):
            base_metrics = {}
            for item in base_data['data']:
                metric_type = item['extra2']
                auc_value = item['AUC']

                if metric_type not in base_metrics:
                    base_metrics[metric_type] = []
                base_metrics[metric_type].append(auc_value)

            for metric_type, values in base_metrics.items():
                if values:
                    avg_value = sum(values) / len(values)
                    day_results[metric_type][base] = round(avg_value, 4)

    # 计算差值
    if day_results["auc"].get(exp, 0) and day_results["auc"].get(base, 0):
        day_results["diff"] = round((day_results["auc"].get(exp, 0) - day_results["auc"].get(base, 0)) * 100, 2)

    if target == 'ue':
        exp_ue_boost = day_results["ue_rocket_auc"].get(exp, 0) - day_results["ue_auc"].get(exp, 0)
        base_ue_boost = day_results["ue_rocket_auc"].get(base, 0) - day_results["ue_auc"].get(base, 0)
        day_results['ue_boost_diff'] = round((exp_ue_boost - base_ue_boost) * 100, 2)
        day_results['rocket_auc_diff'] = round((day_results["ue_rocket_auc"].get(exp, 0) - day_results["ue_rocket_auc"].get(base, 0)) * 100, 2)

    return day_results

def build_grafana_url(exp, base, target, start_timestamp, end_timestamp):
    """构建Grafana查询URL"""
    # 将时间戳转换为毫秒
    from_ts = start_timestamp * 1000
    to_ts = end_timestamp * 1000

    # 构建目标参数
    if target == 'ue':
        target_params = "var-TARGET=auc&var-TARGET=ue_rocket_auc"
    else:
        target_params = "var-TARGET=auc"

    # 构建完整URL
    url = f"https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong?from={from_ts}&to={to_ts}&orgId=3&fullscreen&panelId=5&var-MODEL={exp}&var-MODEL2={base}&{target_params}&var-VARIABLE=All&var-host=All&var-cmp_offset=%2B0&var-BTQTopic=All&var-KafkaTopic=All&var-KafkaGroup=All&var-SparseTableNum=30&var-BatchSize=512&var-common_leaf_service=All&refresh=1m"

    return url

def export_results(results, csv_path, exp, base, target, verbose=False):
    # 如果CSV文件存在，读取现有数据
    if os.path.exists(csv_path):
        existing_df = pd.read_csv(csv_path)
    else:
        existing_df = pd.DataFrame()

    # 分离今天和历史数据
    today = datetime.now().date().isoformat()
    today_results = []
    history_results = []
    
    for day_result in results:
        # 检查日期是否为今天（不考虑时间部分）
        if day_result["date"].split('_')[0] == today:
            today_results.append(day_result)
        else:
            history_results.append(day_result)
    
    # 处理历史数据（写入CSV）
    final_rows = []
    for day_result in history_results:
        row = {
                "date": day_result["date"],
               exp: day_result["auc"].get(exp, 0),
               base: day_result["auc"].get(base, 0),
               "auc_diff/pp": day_result.get("diff", 0)
               }
        if target == 'ue':
            row["ue_boost_diff/pp"] = day_result.get("ue_boost_diff", 0)
            row["rocket_auc_diff/pp"] = day_result.get("rocket_auc_diff", 0)
        final_rows.append(row)
    
    # 只有当有历史结果时才进行合并和排序
    if final_rows:
        new_df = pd.DataFrame(final_rows)
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
        if "date" in combined_df.columns:
            combined_df.sort_values(by="date", ascending=False, inplace=True)
        combined_df.to_csv(csv_path, index=False)
    else:
        combined_df = existing_df
    
    # 处理今天的数据（只打印不写入）
    if today_results:
        # 获取今日数据的时间戳信息来构建URL
        today_result = today_results[0]  # 取第一个今日结果
        if 'start_timestamp' in today_result and 'end_timestamp' in today_result:
            grafana_url = build_grafana_url(exp, base, target,
                                          today_result['start_timestamp'],
                                          today_result['end_timestamp'])
            print(f"\n📊 Grafana查询链接 (Last 24h): {grafana_url}")

        today_rows = []
        for day_result in today_results:
            row = {
                    "date": day_result["date"],
                   exp: day_result["auc"].get(exp, 0),
                   base: day_result["auc"].get(base, 0),
                   "auc_diff/pp": day_result.get("diff", 0)
                   }
            if target == 'ue':
                row["ue_boost_diff/pp"] = day_result.get("ue_boost_diff", 0)
                row["rocket_auc_diff/pp"] = day_result.get("rocket_auc_diff", 0)
            today_rows.append(row)

        today_df = pd.DataFrame(today_rows)

        print(f"截止今日{datetime.now().strftime('%H:%M')}临时数据:")
        # 使用context manager确保完整显示，不省略列
        with pd.option_context('display.max_columns', None,
                              'display.width', None,
                              'display.max_colwidth', None):
            print(today_df)
    
    return combined_df

def read_config():
    with open('config.yaml', 'r') as file:
        config = yaml.safe_load(file)
    return config

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Grafana AUC 数据爬虫')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细的调试信息')
    args = parser.parse_args()

    config = read_config()
    for experiment in config['exp_sets']:
        base = experiment['base']
        exp = experiment['exp']
        start = experiment.get('start', None)
        end = experiment.get('end', None)
        target = experiment.get('target', 'auc')
        csv_path = f"AUC_{exp}.csv"

        # 如果CSV文件存在，读取现有日期
        if os.path.exists(csv_path):
            existing_df = pd.read_csv(csv_path)
            existing_dates = set(existing_df["date"])
        else:
            existing_dates = set()

        # 获取数据
        results = get_grafana_data(exp, base, start, end, target, existing_dates, args.verbose)

        # 导出结果（今日数据只打印不写入）
        combined_df = export_results(results, csv_path, exp, base, target, args.verbose)

        # dataframe转成markdown表格，保存到本地txt文件，去掉index
        with open(f"markdown/AUC_{exp}.md", "w", encoding="utf-8") as f:
            f.write(combined_df.to_markdown(index=False))

        # 打印汇总结果（历史数据），但不显示URL列
        if len(combined_df) != 0:
            print("\n历史数据汇总:")
            # 使用context manager确保完整显示，不省略列
            with pd.option_context('display.max_columns', None,
                                  'display.width', None,
                                  'display.max_colwidth', None):
                print(combined_df.drop(columns=["url"]) if "url" in combined_df.columns else combined_df)
            print('*' * 50)
