import os
import pandas as pd
import requests
from datetime import datetime, timedelta
import yaml

# 设置pandas显示选项，避免在窄terminal中省略内容
pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.width', None)        # 不限制显示宽度
pd.set_option('display.max_colwidth', None) # 不限制列宽

def get_grafana_data(exp, base, start, end, target, existing_dates):
    results = []

    # 设置默认的start和end日期
    if end is None:
        end = datetime.now().date()  # 今天
    else:
        end = datetime.strptime(end, "%Y-%m-%d").date()

    if start is None:
        start = end - timedelta(days=3)  # 今天之前的3天
    else:
        start = datetime.strptime(start, "%Y-%m-%d").date()

    # 遍历从start到end的每一天
    current_date = start
    while current_date <= end:
        if current_date != datetime.now().date() and current_date.isoformat() in existing_dates:
            current_date += timedelta(days=1)
            continue  # 跳过已存在的过去的日期的结果

        date_key = current_date.isoformat()

        try:
            # 判断是否为今天，如果是今天则使用last 24h逻辑
            if current_date == datetime.now().date():
                # 今天：使用当前时刻往前推24小时
                now = datetime.now()
                end_timestamp = int(now.timestamp())
                start_timestamp = int((now - timedelta(hours=24)).timestamp())
                print(f"\n指标：{target}, 查询日期: {current_date} (Last 24h)")
                print(f"时间戳范围: {start_timestamp} - {end_timestamp}")
                print(f"时间范围: {datetime.fromtimestamp(start_timestamp)} - {datetime.fromtimestamp(end_timestamp)}")
            else:
                # 历史日期：使用当天的开始和结束时间戳（秒）
                day_start = datetime.combine(current_date, datetime.min.time())
                day_end = datetime.combine(current_date, datetime.max.time())
                day_start = day_start.replace(microsecond=0)
                day_end = day_end.replace(microsecond=0)
                start_timestamp = int(day_start.timestamp())
                end_timestamp = int(day_end.timestamp())
                print(f"\n指标：{target}, 查询日期: {current_date}")
                print(f"时间戳范围: {start_timestamp} - {end_timestamp}")

            # 发送HTTP请求获取数据
            response_data = fetch_auc_data(exp, start_timestamp, end_timestamp, target)

            if not response_data or 'data' not in response_data:
                print(f"未获取到有效数据: {current_date}")
                current_date += timedelta(days=1)
                continue

            # 解析返回的数据
            day_results = parse_auc_response(response_data, exp, base, target, date_key, start_timestamp, end_timestamp)

            if day_results:
                results.append(day_results)

        except Exception as e:
            print(f"{date_key} 发生错误: {e}")

        current_date += timedelta(days=1)

    return results


def fetch_auc_data(model_name, start_timestamp, end_timestamp, target):
    """发送HTTP请求获取AUC数据"""

    # 构建请求URL
    url = 'https://grafana.corp.kuaishou.com/api/datasources/proxy/5257?'

    # 设置请求头
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://grafana.corp.kuaishou.com',
        'priority': 'u=1, i',
        'referer': 'https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong',
        'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
        'x-caller': 'Grafana',
        'x-dashboard-id': '17635',
        'x-dashboard-uid': 'dR4px1qWz',
        'x-from': str(start_timestamp),
        'x-grafana-org-id': '3',
        'x-panel-id': '5',
        'x-realuser': 'xiatian06',
        'x-step': '60',
        'x-to': str(end_timestamp)
    }

    # 设置cookies（需要根据实际情况更新）
    cookies = {
        '_did': 'web_358191091D5C6D81',
        'hdige2wqwoino': 'jFyZBDYxQi54p6HXp82FcBPMm8D3YKtSdf67f23b',
        'ksCorpDeviceid': '8807abce-95aa-4144-aa4d-ea05b106ea9e',
        'idp_theme_change_identification': '2',
        'tiangong_user_id': 'xiatian06',
        'did': 'web_994895bf210a4746ad99e0100c772f00',
        'didv': '1749363345405',
        'apdid': '46f7fcfa-7af2-44e4-956a-ff59acaa43d3313b07875472b715e1f8a10d5c28f26b:1752472054:1',
        'ks_sso_gray_cookie': '1',
        'accessproxy_session': '14177363-f2a1-4b78-8636-ec04f9f388fa',
        'kwpsecproductname': 'PCLive',
        'grafana_session_v3': '90c8d4f1e17c607e2279f9925d307017'
    }

    # 构建SQL查询
    if target == 'ue':
        target_filter = "AND extra2 in ('auc','ue_auc','ue_rocket_auc')"
    else:
        target_filter = "AND extra2 in ('auc')"

    sql_query = f"""SELECT     (intDiv(toUInt32(timestamp), 60) * 60) * 1000 AS t,     extra2,     extra3,     AVG(average) / 10000 AS AUC FROM perf.cpp_mio_learner_perf_log WHERE     `timestamp` >= toDateTime({start_timestamp})     AND `timestamp` < toDateTime({end_timestamp})     AND namespace = 'mio-learner'     AND subtag like 'auc%'     AND extra1 = '{model_name}'     {target_filter} GROUP BY t, extra2,extra3 HAVING AUC>=-3000 ORDER BY t, AUC DESC FORMAT JSON"""

    # 构建请求数据
    data = f"""---Cluster:ssd, TimeRangeLength:4513
---from:https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong
---qtype:time_series
---user:xiatian06
{sql_query}"""

    try:
        print(f"发送请求到: {url}")
        print(f"查询模型: {model_name}")
        response = requests.post(url, headers=headers, cookies=cookies, data=data, timeout=30)
        print(f"响应状态码: {response.status_code}")

        if response.status_code != 200:
            print(f"响应内容: {response.text[:500]}")
            return None

        response_json = response.json()
        print(f"获取到 {len(response_json.get('data', []))} 条数据")
        return response_json
    except Exception as e:
        print(f"请求失败: {e}")
        return None


def parse_auc_response(response_data, exp, base, target, date_key, start_timestamp, end_timestamp):
    """解析AUC响应数据"""

    if not response_data.get('data'):
        return None

    day_results = {"date": date_key, "auc": {}, "ue_auc": {}, "ue_rocket_auc": {}}

    # 按指标类型分组数据
    metrics_data = {}
    for item in response_data['data']:
        metric_type = item['extra2']  # auc, ue_auc, ue_rocket_auc
        auc_value = item['AUC']

        if metric_type not in metrics_data:
            metrics_data[metric_type] = []
        metrics_data[metric_type].append(auc_value)

    # 计算每个指标的平均值
    for metric_type, values in metrics_data.items():
        if values:
            avg_value = sum(values) / len(values)
            day_results[metric_type][exp] = round(avg_value, 4)
            print(f"{metric_type}: {avg_value:.4f}")

    # 如果需要base数据，发送另一个请求获取base模型的数据
    if base and base != exp:
        # 使用相同的时间戳范围获取base数据
        base_data = fetch_auc_data(base, start_timestamp, end_timestamp, target)

        if base_data and base_data.get('data'):
            base_metrics = {}
            for item in base_data['data']:
                metric_type = item['extra2']
                auc_value = item['AUC']

                if metric_type not in base_metrics:
                    base_metrics[metric_type] = []
                base_metrics[metric_type].append(auc_value)

            for metric_type, values in base_metrics.items():
                if values:
                    avg_value = sum(values) / len(values)
                    day_results[metric_type][base] = round(avg_value, 4)

    # 计算差值
    if day_results["auc"].get(exp, 0) and day_results["auc"].get(base, 0):
        day_results["diff"] = round((day_results["auc"].get(exp, 0) - day_results["auc"].get(base, 0)) * 100, 2)

    if target == 'ue':
        exp_ue_boost = day_results["ue_rocket_auc"].get(exp, 0) - day_results["ue_auc"].get(exp, 0)
        base_ue_boost = day_results["ue_rocket_auc"].get(base, 0) - day_results["ue_auc"].get(base, 0)
        day_results['ue_boost_diff'] = round((exp_ue_boost - base_ue_boost) * 100, 2)
        day_results['rocket_auc_diff'] = round((day_results["ue_rocket_auc"].get(exp, 0) - day_results["ue_rocket_auc"].get(base, 0)) * 100, 2)

    return day_results

def export_results(results, csv_path, exp, base, target):
    # 如果CSV文件存在，读取现有数据
    if os.path.exists(csv_path):
        existing_df = pd.read_csv(csv_path)
    else:
        existing_df = pd.DataFrame()

    # 分离今天和历史数据
    today = datetime.now().date().isoformat()
    today_results = []
    history_results = []
    
    for day_result in results:
        # 检查日期是否为今天（不考虑时间部分）
        if day_result["date"].split('_')[0] == today:
            today_results.append(day_result)
        else:
            history_results.append(day_result)
    
    # 处理历史数据（写入CSV）
    final_rows = []
    for day_result in history_results:
        row = {
                "date": day_result["date"],
               exp: day_result["auc"].get(exp, 0),
               base: day_result["auc"].get(base, 0),
               "auc_diff/pp": day_result.get("diff", 0)
               }
        if target == 'ue':
            row["ue_boost_diff/pp"] = day_result.get("ue_boost_diff", 0)
            row["rocket_auc_diff/pp"] = day_result.get("rocket_auc_diff", 0)
        final_rows.append(row)
    
    # 只有当有历史结果时才进行合并和排序
    if final_rows:
        new_df = pd.DataFrame(final_rows)
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
        if "date" in combined_df.columns:
            combined_df.sort_values(by="date", ascending=False, inplace=True)
        combined_df.to_csv(csv_path, index=False)
    else:
        combined_df = existing_df
    
    # 处理今天的数据（只打印不写入）
    if today_results:
        today_rows = []
        for day_result in today_results:
            row = {
                    "date": day_result["date"],
                   exp: day_result["auc"].get(exp, 0),
                   base: day_result["auc"].get(base, 0),
                   "auc_diff/pp": day_result.get("diff", 0)
                   }
            if target == 'ue':
                row["ue_boost_diff/pp"] = day_result.get("ue_boost_diff", 0)
                row["rocket_auc_diff/pp"] = day_result.get("rocket_auc_diff", 0)
            today_rows.append(row)
        
        today_df = pd.DataFrame(today_rows)

        print(f"截止今日{datetime.now().strftime('%H:%M')}临时数据:")
        # 使用context manager确保完整显示，不省略列
        with pd.option_context('display.max_columns', None,
                              'display.width', None,
                              'display.max_colwidth', None):
            print(today_df)
    
    return combined_df

def read_config():
    with open('config.yaml', 'r') as file:
        config = yaml.safe_load(file)
    return config

if __name__ == "__main__":
    config = read_config()
    for experiment in config['exp_sets']:
        base = experiment['base']
        exp = experiment['exp']
        start = experiment.get('start', None)
        end = experiment.get('end', None)
        target = experiment.get('target', 'auc')
        csv_path = f"AUC_{exp}.csv"

        # 如果CSV文件存在，读取现有日期
        if os.path.exists(csv_path):
            existing_df = pd.read_csv(csv_path)
            existing_dates = set(existing_df["date"])
        else:
            existing_dates = set()

        # 获取数据
        results = get_grafana_data(exp, base, start, end, target, existing_dates)

        # 导出结果（今日数据只打印不写入）
        combined_df = export_results(results, csv_path, exp, base, target)

        # dataframe转成markdown表格，保存到本地txt文件，去掉index
        with open(f"markdown/AUC_{exp}.md", "w", encoding="utf-8") as f:
            f.write(combined_df.to_markdown(index=False))

        # 打印汇总结果（历史数据），但不显示URL列
        if len(combined_df) != 0:
            print("\n历史数据汇总:")
            # 使用context manager确保完整显示，不省略列
            with pd.option_context('display.max_columns', None,
                                  'display.width', None,
                                  'display.max_colwidth', None):
                print(combined_df.drop(columns=["url"]) if "url" in combined_df.columns else combined_df)
            print('*' * 50)
