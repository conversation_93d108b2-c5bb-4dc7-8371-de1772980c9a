import os
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from get_cookie import read_cookies
import time
from datetime import datetime, timedelta
import yaml

# 设置pandas显示选项，避免在窄terminal中省略内容
pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.width', None)        # 不限制显示宽度
pd.set_option('display.max_colwidth', None) # 不限制列宽

def get_grafana_data(exp, base, start, end, target, existing_dates):
    results = []

    # 设置默认的start和end日期
    if end is None:
        end = datetime.now().date()  # 今天
    else:
        end = datetime.strptime(end, "%Y-%m-%d").date()

    if start is None:
        start = end - timedelta(days=3)  # 今天之前的3天
    else:
        start = datetime.strptime(start, "%Y-%m-%d").date()

    # 遍历从start到end的每一天
    current_date = start
    while current_date <= end:
        if current_date != datetime.now().date() and current_date.isoformat() in existing_dates:
            current_date += timedelta(days=1)
            continue  # 跳过已存在的过去的日期的结果
        
        date_key = current_date.isoformat()
        
        try:
            # 计算当天的开始和结束时间戳（毫秒）
            # 这里最后只精确到秒，毫秒设为0
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            day_start = day_start.replace(microsecond=0)
            day_end = day_end.replace(microsecond=0)
            start_timestamp = int(day_start.timestamp() * 1000)
            end_timestamp = int(day_end.timestamp() * 1000)

            if target == 'ue':
                target_request = 'auc&var-TARGET=ue_auc&var-TARGET=ue_rocket_auc'
            else:
                target_request = 'auc'
            # 构建URL
            url = f"https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong?orgId=3&from={start_timestamp}&to={end_timestamp}&refresh=1m&fullscreen&panelId=5&var-MODEL={exp}&var-MODEL2={base}&var-TARGET={target_request}&var-VARIABLE=All&var-host=All&var-cmp_offset=%2B0&var-BTQTopic=All&var-KafkaTopic=All&var-KafkaGroup=All&var-SparseTableNum=30&var-BatchSize=512&var-common_leaf_service=All"
            # url = f"https://tianwen.corp.kuaishou.com/dashboard/2330/detail?breadcrumb_type=grafana&org=14&currentEnv=prod&grafana_query=kiosk%253Dtianwen%2526orgId%253D3%2526theme%253Dlight%2526tab%253Dqueries%2526undefined%2526fullscreen%2526panelId%253D5%2526var-MODEL%253D{exp}%2526var-MODEL2%253D{base}%2526var-TARGET%253Dauc"
            print("\n正在查询URL:", url)
            print(f"指标：{target}, 查询日期: {current_date}")

            # 访问URL
            driver.get(url)

            # 等待页面加载完成（等待图表元素出现）
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.CLASS_NAME, "graph-legend-series"))
            )

            # 给页面一些额外时间加载图表数据
            time.sleep(0.2)

            # 查找并提取avg auc,train值
            auc_elements = driver.find_elements(By.CLASS_NAME, "graph-legend-series")

            day_results = {"date": date_key, "auc": {}, "ue_auc": {}, "ue_rocket_auc": {}, "url": url}

            for e in auc_elements:
                name = e.find_element(By.CLASS_NAME, "graph-legend-alias").text
                avg = e.find_element(By.CLASS_NAME, "avg").text
                print(f"name: {name}, avg: {avg}")

                namelist = [i.strip() for i in name.split(',')]
                if len(namelist) == 2:
                    day_results[namelist[0]][exp] = float(avg)
                elif len(namelist) == 3:
                    assert namelist[0] == base
                    day_results[namelist[1]][base] = float(avg)
                else:
                    print(f"graph-legend-alias {name} wrong")
            

            day_results["diff"] = round(day_results["auc"].get(exp, 0) - day_results["auc"].get(base, 0), 5) * 100
            if target == 'ue':
                exp_ue_boost = day_results["ue_rocket_auc"].get(exp, 0) - day_results["ue_auc"].get(exp, 0)
                base_ue_boost = day_results["ue_rocket_auc"].get(base, 0) - day_results["ue_auc"].get(base, 0)
                day_results['ue_boost_diff'] = round(exp_ue_boost - base_ue_boost, 5) * 100
                # 添加rocket_auc_diff计算：exp和base的ue_rocket_auc的差值
                day_results['rocket_auc_diff'] = round(day_results["ue_rocket_auc"].get(exp, 0) - day_results["ue_rocket_auc"].get(base, 0), 5) * 100

            results.append(day_results)

        except Exception as e:
            print(f"{date_key} 发生错误: {e}")

        current_date += timedelta(days=1)

    return results

def export_results(results, csv_path, exp, base, target):
    # 如果CSV文件存在，读取现有数据
    if os.path.exists(csv_path):
        existing_df = pd.read_csv(csv_path)
    else:
        existing_df = pd.DataFrame()

    # 分离今天和历史数据
    today = datetime.now().date().isoformat()
    today_results = []
    history_results = []
    
    for day_result in results:
        # 检查日期是否为今天（不考虑时间部分）
        if day_result["date"].split('_')[0] == today:
            today_results.append(day_result)
        else:
            history_results.append(day_result)
    
    # 处理历史数据（写入CSV）
    final_rows = []
    for day_result in history_results:
        row = {
                "date": day_result["date"],
               exp: day_result["auc"].get(exp, 0),
               base: day_result["auc"].get(base, 0),
               "auc_diff/pp": day_result["diff"],
               "url": day_result["url"]
               }
        if target == 'ue':
            row["ue_boost_diff/pp"] = day_result["ue_boost_diff"]
            row["rocket_auc_diff/pp"] = day_result["rocket_auc_diff"]
        final_rows.append(row)
    
    # 只有当有历史结果时才进行合并和排序
    if final_rows:
        new_df = pd.DataFrame(final_rows)
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
        if "date" in combined_df.columns:
            combined_df.sort_values(by="date", ascending=False, inplace=True)
        combined_df.to_csv(csv_path, index=False)
    else:
        combined_df = existing_df
    
    # 处理今天的数据（只打印不写入）
    if today_results:
        today_rows = []
        for day_result in today_results:
            row = {
                    "date": day_result["date"],
                   exp: day_result["auc"].get(exp, 0),
                   base: day_result["auc"].get(base, 0),
                   "auc_diff/pp": day_result["diff"],
                   "url": day_result["url"]
                   }
            if target == 'ue':
                row["ue_boost_diff/pp"] = day_result["ue_boost_diff"]
                row["rocket_auc_diff/pp"] = day_result["rocket_auc_diff"]
            today_rows.append(row)
        
        today_df = pd.DataFrame(today_rows)

        print(f"截止今日{datetime.now().strftime('%H:%M')}临时数据:")
        # 使用context manager确保完整显示，不省略列
        with pd.option_context('display.max_columns', None,
                              'display.width', None,
                              'display.max_colwidth', None):
            print(today_df.drop(columns=["url"]))
    
    return combined_df

def read_config():
    with open('config.yaml', 'r') as file:
        config = yaml.safe_load(file)
    return config

if __name__ == "__main__":
    # 配置 Selenium
    options = Options()
    # 可选：让 Selenium 使用有界面模式（方便调试）
    # options.add_argument("--headless")
    driver = webdriver.Chrome(options=options)

    # driver.get("https://grafana.corp.kuaishou.com/d/dR4px1qWz/mioxun-lian-jian-kong")
    # # 把本地浏览器的 cookie 导入 Selenium
    # for cookie_dict in read_cookies():
    #     driver.add_cookie(cookie_dict)
    # # 再次访问 Grafana，这时已带上你的登录 cookie

    config = read_config()
    for experiment in config['exp_sets']:
        base = experiment['base']
        exp = experiment['exp']
        start = experiment.get('start', None)
        end = experiment.get('end', None)
        target = experiment.get('target', 'auc')
        csv_path = f"AUC_{exp}.csv"

        # 如果CSV文件存在，读取现有日期
        if os.path.exists(csv_path):
            existing_df = pd.read_csv(csv_path)
            existing_dates = set(existing_df["date"])
        else:
            existing_dates = set()

        # 获取数据
        results = get_grafana_data(exp, base, start, end, target, existing_dates)

        # 导出结果（今日数据只打印不写入）
        combined_df = export_results(results, csv_path, exp, base, target)

        # dataframe转成markdown表格，保存到本地txt文件，去掉index
        with open(f"markdown/AUC_{exp}.md", "w", encoding="utf-8") as f:
            f.write(combined_df.to_markdown(index=False))

        # 打印汇总结果（历史数据），但不显示URL列
        if len(combined_df) != 0:
            print("\n历史数据汇总:")
            # 使用context manager确保完整显示，不省略列
            with pd.option_context('display.max_columns', None,
                                  'display.width', None,
                                  'display.max_colwidth', None):
                print(combined_df.drop(columns=["url"]))
            print('*' * 50)

    # 关闭浏览器
    driver.quit()
